{"remove": "Remove", "selectedCount": "{{count}} selected", "pause": "Pause", "disabled": "Disabled", "timePeriod.custom": "Custom...", "emptyWidget.requestTimeoutError.title": "Oops", "goBack": "Go back", "incomplete": "Incomplete", "loadMore": "Load more", "resourceChanged": "Resource has changed", "filters.filterText": "Filters", "name": "Name", "emptyWidget.noPermission.title": "You don't have access to this", "close": "Close", "add": "Add", "discard": "Discard", "create": "Create", "emptyRowValue": "N/A", "next": "Next", "emptyWidget.noConnection.message": "We weren't able to process your request. Please try again", "from": "From", "outOfBoundsAndIntOnlyWarningMessage": "Enter a number between {{min}} and {{max}}", "leave": "Leave", "newName": "New name", "dropdownLoadMoreButton": "Load more...", "exceedingGlobalMaxAttemptsErrorMessage": "Some attempts exceed this limit", "hours.count": "{{count}} hour", "hours.count_plural": "{{count}} hours", "delete": "Delete", "noItemsFoundTitle": "No items found", "file": "file", "hours.general": "hour(s)", "emptyWidget.minSearchInputLength.message": "Type at least {{minInputLength}} characters for us to fetch your search results", "cancel": "Cancel\t", "searchExactNumberPlaceHolderText": "Search for an exact number", "listPagination.jumpTo": "Jump to", "maxCharacterLimitExceded": "The maximum number of characters has been exceeded\t", "emptyWidget.noTeam.message": "Check with your manager about your permissions.", "numberOfRecords": "Number of records", "search": "Search", "teamDropdownPlaceholder": "Select an option", "download": "download", "createCampaign.outScope": "You can't create a campaign for this team. Please select a team within your management scope.", "timePeriod.lastSixHours": "Last 6 hours", "timePeriod.lastWeek": "Last week", "dismissibleTags.activity": "activity", "dismissibleTags.activities": "activities", "outOfBoundsAndIntOnlyWarning": "Please insert a value between {{min}} and {{max}}, without decimal places", "searchExactNumberTooltipText": "Type at least {{minLength}} characters", "emptyWidget.minSearchInputLength.title": "Type {{minInputLength}} characters to start searching", "somethingWentWrong": "Something went wrong", "errorNameNotUnique": "This campaign name is already taken", "teamDropdownLabel": "Team", "enabled": "Enabled", "outMaxAttempt": "The number cannot exceed the max attempts per record", "emptyWidget.noConnection.title": "No internet connection", "createdAt": "Created at", "days.general": "day(s)", "filters.clearAllButton": "Clear all", "maxLengthWarning": "We can only support a maximum number of {{maxLength}} characters", "refresh": "Refresh", "emptyWidget.searchWithNoResults.message": "Please use different criteria to search or filter for campaigns", "list.emptyTitle": "No entries", "list.emptyMessage": "There are currently no available entries", "save": "Save", "emptyWidget.noTeam.title": "You don't belong to any team", "timePeriod.lastDay": "Last 24 hours", "searchByName": "Search by name", "enterIsOutOfBoundsAndIntOnlyWarning": "Entering a number between {{min}} and {{max}}, without decimal places", "emptyWidget.searchWithNoResults.title": "No campaigns found", "exceedingGlobalMaxAttemptsError": "The attempts in record chaining is exceeding the global max. attempts per record", "seconds.count": "{{count}} second", "seconds.count_plural": "{{count}} seconds", "minutes.count_plural": "{{count}} minutes", "days.count_plural": "{{count}} days", "running": "Running", "emptyWidget.requestTimeoutError.message": "there was a problem loading data.", "timePeriod.anyTime": "All time", "select": "Select", "warningNameCouldNotBeVerified": "Couldn't verify name. Please try again.", "requiredFieldWarning": "This field is required", "ringGroups": "Ring groups", "warningNameCouldNotHaveRestrictedCharacters": "Characters \"&\" and \"#\" are not allowed when creating campaign's name. Please choose a different name without those characters.", "paused": "Paused", "pausing": "Pausing", "previous": "Previous", "ready": "Ready", "noMatchesFoundMessage": "Please search for another keyword", "minutes.count": "{{count}} minute", "seconds.general": "second(s)", "start": "Start", "starting": "Starting", "status": "Status", "emptyWidget.noPermission.message": "It looks like you don't have permission to see this content.", "days.count": "{{count}} day", "nameAllOptionText": "All", "timePeriod.lastMonth": "Last month", "teamAllOptionText": "All teams", "loading": "Loading", "apply": "Apply", "to": "To", "minutes.general": "minute(s)", "clear": "Clear", "dropdownLoadingText": "Loading...", "noMatchesFoundTitle": "No results found", "option.name.default": "All"}