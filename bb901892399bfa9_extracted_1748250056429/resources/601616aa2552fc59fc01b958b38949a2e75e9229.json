{"id": "0d1505c6-1eee-4bfb-a9ed-ea3be3a97d7e", "page": 1, "per_page": 3, "total_results": 10, "total_pages": 4, "search_hits": [{"id": "0c44c0f9-461c-4978-8315-e39c9d82c2b2-guide-id", "score": 0.03809808, "title": "16 Customer Service Apology Statements", "source": {"components": [{"stripped_content": "Tell me, has this happened before?\nExplain to me, what impact has this had on your…?\nDescribe how you felt about that.\nI’m sorry, I can see how [INSERT PROBLEM] must have been frustrating for you.\nI’m very sorry, I appreciate how difficult it must have been for you to deal with [INSERT PROBLEM].\nThanks for alerting us to [INSERT PROBLEM]. I recognise that this can’t have been an easy thing to deal with and I’m so sorry that we have caused you to feel [INSERT EXPRESSED EMOTION].\nWhat I’m now doing to help you is [INSERT ACTION].\nYou were absolutely right to bring this to my attention, so I can immediately [INSERT ACTION].\nAs I investigate, I can see that….\nI will try everything I can to get this fixed by the end of the day.\nI will contact you as soon as I have an update.\nI will work on resolving the problem and I will be in touch shortly, so you can enjoy your (vacation/birthday/holidays etc.).\nThis situation is difficult, but one solution that we could try is to [INSERT SOLUTION].\nNow that I’m aware of the situation, we can look to fix it by [INSERT SOLUTION].\nIf I were in your situation, I would feel the same. What I would try, and I suggest that we do too, is to [INSERT SOLUTION].\nI’m sorry that you’ve had to call in today, but we have managed to [INSERT SOLUTION]. Now we have done that, our next steps are [INSERT NEXT STEPS]", "stripped_content_table": "", "content": "<p>Tell me, has this happened before?</p>\n<p>Explain to me, what impact has this had on your…?</p>\n<p>Describe how you felt about that</p>\n<p>I’m sorry, I can see how [INSERT PROBLEM] must have been frustrating for you.</p>\n<p>I’m very sorry, I appreciate how difficult it must have been for you to deal with [INSERT PROBLEM]</p>\n<p>Thanks for alerting us to [INSERT PROBLEM]. I recognise that this can’t have been an easy thing to deal with and I’m so sorry that we have caused you to feel [INSERT EXPRESSED EMOTION]</p>\n<p>What I’m now doing to help you is [INSERT ACTION]</p>\n<p>You were absolutely right to bring this to my attention, so I can immediately [INSERT ACTION]</p>\n<p>As I investigate, I can see that…</p>\n<p>I will try everything I can to get this fixed by the end of the day</p>\n<p>I will contact you as soon as I have an update</p>\n<p>I will work on resolving the problem and I will be in touch shortly, so you can enjoy your (vacation/birthday/holidays etc.)</p>\n<p>This situation is difficult, but one solution that we could try is to [INSERT SOLUTION]</p>\n<p>Now that I’m aware of the situation, we can look to fix it by [INSERT SOLUTION]</p>\n<p>If I were in your situation, I would feel the same. What I would try, and I suggest that we do too, is to [INSERT SOLUTION]</p>\n<p>I’m sorry that you’ve had to call in today, but we have managed to [INSERT SOLUTION]. Now we have done that, our next steps are [INSERT NEXT STEPS]</p>\n", "tags": ["intent_id:DEFAULT", "intent_name:DEFAULT", "modality:DEFAULT", "ring_group:DEFAULT", "knowledge_segment:DEFAULT", "parameter_id:DEFAULT", "parameter_name:DEFAULT", "entity_id:DEFAULT", "entity_synonym_name:DEFAULT"]}], "content_type": "INTENT_CARD", "language": "en", "source": {"system": "internal", "ring_groups": [], "name": "guide", "id": "guide-id", "type": "guide"}, "space": {"id": "CARD_DEFAULT_SPACE_ID", "title": "CARD_DEFAULT_SPACE_TITLE"}, "url": "https://637b683d24e55622d4899257./atlas/apps/guide/intent_card/0c44c0f9-461c-4978-8315-e39c9d82c2b2"}, "updated_at": "2022-11-21T12:01:10.624624962Z"}, {"id": "1f5f628e-ce77-403f-980a-a4c00893b7ef-guide-id", "score": 0.03809808, "title": "How to Handle an Escalated Customer Confrontation", "source": {"components": [{"stripped_content": "Step 1: Let go of your ego.\nIt’s critical that you maintain a certain emotional distance during the interaction. Don’t let your ego enter the equation. Otherwise, the conflict will become personal — and you don’t want that. Make a conscious effort to control your responses. Manage them carefully. Be cautious with your words and tone, as well as with your body language. In this way, you can ensure you behave with the utmost professionalism.\nStep 2: Decide to defuse.\nManage the conversation cadence. If the customer is shouting, speak softly. If he is talking very quickly, speak slowly.\nAsk for and use the customer’s name and introduce yourself by name.\nAsk the customer to explain what happened so you know the real problem. Actively listen.\nAgree that there is a problem. Don’t defend, deny, or explain why the problem happened — it’ll just sound like you’re making excuses. Besides, those are all offstage issues that the customer doesn’t really care about.\nApologize . . . several times, if need be.\nTell the customer that you’re going to act immediately to fix the problem.\nStep 3: Understand the problem.\nAvoid these three common errors:\nFocusing on the facts: understanding a problem is as much about understanding and acknowledging the emotions that have resulted from the problem as it is about understanding the cold, hard facts of the situation.\nAssuming your view of the situation is correct: You must at least consider the possibility that the customer’s view is, in fact, the correct one.\nPlaying the blame game: Apologize, take ownership of the problem, and then move to understand the real issues.\nStep 4: Allow time for venting.\nLet the customer speak. Give him a chance to vent — that is, to safely discharge his anger and/or frustration. <PERSON>’t rush him, and don’t jump in to defend yourself. Be patient.\nStep 5: Get to common ground.\nMost major negotiations are merely a series of small sub-agreements and even smaller yeses. When interacting with an incensed customer — or when engaged in any high-stakes conversation — your job is to listen for any small “yes” that can move the conversation forward. The idea is to quickly reach some small agreements.", "stripped_content_table": "", "content": "<p>Step 1: Let go of your ego</p>\n<p>It’s critical that you maintain a certain emotional distance during the interaction. Don’t let your ego enter the equation. Otherwise, the conflict will become personal — and you don’t want that. Make a conscious effort to control your responses. Manage them carefully. Be cautious with your words and tone, as well as with your body language. In this way, you can ensure you behave with the utmost professionalism.</p>\n<p><br></p>\n<p><br></p>\n<p>Step 2: Decide to defuse</p>\n<p>Manage the conversation cadence. If the customer is shouting, speak softly. If he is talking very quickly, speak slowly.</p>\n<p>Ask for and use the customer’s name and introduce yourself by name.</p>\n<p>Ask the customer to explain what happened so you know the real problem. Actively listen.</p>\n<p>Agree that there is a problem. Don’t defend, deny, or explain why the problem happened — it’ll just sound like you’re making excuses. Besides, those are all offstage issues that the customer doesn’t really care about.</p>\n<p>Apologize . . . several times, if need be.</p>\n<p>Tell the customer that you’re going to act immediately to fix the problem.</p>\n<p><br></p>\n<p><br></p>\n<p>Step 3: Understand the problem</p>\n<p>Avoid these three common errors:</p>\n<p><br></p>\n<p>Focusing on the facts: understanding a problem is as much about understanding and acknowledging the emotions that have resulted from the problem as it is about understanding the cold, hard facts of the situation.</p>\n<p><br></p>\n<p>Assuming your view of the situation is correct: You must at least consider the possibility that the customer’s view is, in fact, the correct one.</p>\n<p><br></p>\n<p>Playing the blame game: Apologize, take ownership of the problem, and then move to understand the real issues.</p>\n<p><br></p>\n<p><br></p>\n<p>Step 4: Allow time for venting</p>\n<p>Let the customer speak. Give him a chance to vent — that is, to safely discharge his anger and/or frustration. Don’t rush him, and don’t jump in to defend yourself. Be patient.</p>\n<p><br></p>\n<p>Step 5: Get to common ground</p>\n<p>Most major negotiations are merely a series of small sub-agreements and even smaller yeses. When interacting with an incensed customer — or when engaged in any high-stakes conversation — your job is to listen for any small “yes” that can move the conversation forward. The idea is to quickly reach some small agreements.</p>\n", "tags": ["intent_id:DEFAULT", "intent_name:DEFAULT", "modality:DEFAULT", "ring_group:DEFAULT", "knowledge_segment:DEFAULT", "parameter_id:DEFAULT", "parameter_name:DEFAULT", "entity_id:DEFAULT", "entity_synonym_name:DEFAULT"]}], "content_type": "INTENT_CARD", "language": "en", "source": {"system": "internal", "ring_groups": [], "name": "guide", "id": "guide-id", "type": "guide"}, "space": {"id": "CARD_DEFAULT_SPACE_ID", "title": "CARD_DEFAULT_SPACE_TITLE"}, "url": "https://637b683d24e55622d4899257./atlas/apps/guide/intent_card/1f5f628e-ce77-403f-980a-a4c00893b7ef"}, "updated_at": "2022-11-21T12:01:10.475475194Z"}, {"id": "5b5eea96-5120-423f-a878-9130a5ee4d61-guide-id", "score": 0.03809808, "title": "Dealing with angry customers: 5 steps for call center agents", "source": {"components": [{"stripped_content": "Try the steps below to improve the situation with the customer.\nStep 1: Listen.\nLet the caller vent his frustration. Sometimes, after a caller is given the chance to express displeasure, they will apologize and allow you to solve the problem with no further anger.\nTake notes while you listen.\nStep 2: Remain calm.\nUse an even tone while you speak. Remember that the customer isn’t angry at you. Put yourself in their shoes.\nStep 3: Repeat information.\nMake them feel as if you are on their side. Apologize about the problem, convey empathy and then summarize their main points.\nStep 4: Avoid the hold button.\nHold time will add to the caller’s frustration, escalating the situation.\nStep 5: Make the caller happy.\nBe sure to not only tell your customer what their options are, but also how soon each option can be implemented.\nAsk the caller if there is anything else you can help them with.", "stripped_content_table": "", "content": "<p>Try the steps below to improve the situation with the customer</p>\n<p>Step 1: Listen.</p>\n<p>Let the caller vent his frustration. Sometimes, after a caller is given the chance to express displeasure, they will apologize and allow you to solve the problem with no further anger.</p>\n<p>Take notes while you listen.</p>\n<p>Step 2: <PERSON><PERSON>in calm.</p>\n<p>Use an even tone while you speak. Remember that the customer isn’t angry at you. Put yourself in their shoes.</p>\n<p>Step 3: Repeat information.</p>\n<p>Make them feel as if you are on their side. Apologize about the problem, convey empathy and then summarize their main points.</p>\n<p>Step 4: Avoid the hold button.</p>\n<p>Hold time will add to the caller’s frustration, escalating the situation.</p>\n<p>Step 5: Make the caller happy.</p>\n<p>Be sure to not only tell your customer what their options are, but also how soon each option can be implemented.</p>\n<p>Ask the caller if there is anything else you can help them with.</p>\n", "tags": ["intent_id:DEFAULT", "intent_name:DEFAULT", "modality:DEFAULT", "ring_group:DEFAULT", "knowledge_segment:DEFAULT", "parameter_id:DEFAULT", "parameter_name:DEFAULT", "entity_id:DEFAULT", "entity_synonym_name:DEFAULT"]}], "content_type": "INTENT_CARD", "language": "en", "source": {"system": "internal", "ring_groups": [], "name": "guide", "id": "guide-id", "type": "guide"}, "space": {"id": "CARD_DEFAULT_SPACE_ID", "title": "CARD_DEFAULT_SPACE_TITLE"}, "url": "https://637b683d24e55622d4899257./atlas/apps/guide/intent_card/5b5eea96-5120-423f-a878-9130a5ee4d61"}, "updated_at": "2022-11-21T12:01:10.344344382Z"}]}