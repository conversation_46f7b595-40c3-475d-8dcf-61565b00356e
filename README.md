# Playwright Trace Analyzer MCP

A Machine Code Processing (MCP) tool for analyzing Playwright trace files directly from within Cursor or other AI coding environments.

## Overview

This MCP allows you to:

1. Analyze Playwright trace.zip files without running tests
2. Extract and view detailed trace information with step-by-step execution
3. View network logs with intelligent filtering to remove bloat
4. Extract and view screenshots from trace files
5. Apply smart filtering to reduce trace file sizes by 80-95%

## Installation

```bash
npm install playwright-trace-analyzer --save-dev
```

## Features

- **Direct trace.zip analysis**: Simply provide the path to any trace.zip file
- **Smart filtering**: Automatically removes analytics, third-party services, and verbose metadata
- **Network log analysis**: Detailed network request/response information
- **Screenshot extraction**: View all screenshots captured during test execution
- **No test execution required**: Pure analysis tool, no need to run tests

## Setup

1. Configure the MCP in your `.cursor/mcp.json` file (or equivalent file for Claude Desktop or Claude Code):

```json
{
  "playwright-trace-analyzer": {
    "command": "npx",
    "args": [
      "playwright-trace-analyzer"
    ]
  }
}
```

## Tools

### analyze-trace (推荐)

- **Description**: Perform a comprehensive analysis of a trace.zip file, including trace execution, network logs, and screenshots. This is the recommended tool for complete trace analysis.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file (e.g. '/path/to/trace.zip')
- **Output**: Complete analysis report with errors, actions, network failures, and screenshots summary

### get-trace

- **Description**: Gets the trace from a trace.zip file. This includes step-by-step playwright test execution info along with console logs. By default returns a filtered version that removes bloated data like DOM snapshots while preserving essential debugging information.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file (e.g. '/path/to/trace.zip')
  - `raw` (boolean, optional, default: false): Return raw unfiltered trace including all DOM snapshots and verbose data
- **Output**: Detailed trace information showing each step of test execution and console logs (filtered by default)

### get-network-log

- **Description**: Gets browser network logs from a trace.zip file. By default returns a filtered version that removes analytics, third-party services, and verbose metadata while preserving essential debugging information.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file (e.g. '/path/to/trace.zip')
  - `raw` (boolean, optional, default: false): Return raw unfiltered network log including all analytics, third-party services, and verbose metadata
- **Output**: Network requests and responses (filtered by default for 80%+ size reduction, focused on localhost application traffic)

### get-screenshots

- **Description**: Gets all available screenshots from a trace.zip file. Useful for debugging.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file (e.g. '/path/to/trace.zip')
- **Output**: All screenshots captured during test execution with their names

### get-raw-trace-paginated

- **Description**: Get raw trace content in pages to avoid size limits. Use this when you need the complete unfiltered trace data.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file
  - `page` (number, optional, default: 1): Page number
  - `pageSize` (number, optional, default: 50): Number of entries per page
- **Output**: Paginated raw trace content with navigation info

### get-raw-network-paginated

- **Description**: Get raw network content in pages to avoid size limits. Use this when you need the complete unfiltered network data.
- **Parameters**:
  - `traceZipPath` (string, required): The full path to the trace.zip file
  - `page` (number, optional, default: 1): Page number
  - `pageSize` (number, optional, default: 20): Number of requests per page
- **Output**: Paginated raw network content with navigation info

## Usage Examples

1. **Analyze a trace file**:
   ```
   Use the get-trace tool with traceZipPath: "/path/to/your/trace.zip"
   ```

2. **Get network logs**:
   ```
   Use the get-network-log tool with traceZipPath: "/path/to/your/trace.zip"
   ```

3. **Extract screenshots**:
   ```
   Use the get-screenshots tool with traceZipPath: "/path/to/your/trace.zip"
   ```

## Benefits

- **No test execution overhead**: Analyze existing trace files without re-running tests
- **Intelligent filtering**: Automatically removes noise while preserving important debugging information
- **Size reduction**: Filtered traces are 80-95% smaller than raw traces
- **Comprehensive analysis**: View execution steps, network activity, and visual screenshots all in one tool
