{"version":7,"type":"context-options","origin":"testRunner","browserName":"","options":{},"platform":"linux","wallTime":1748234378271,"monotonicTime":460.447,"sdkLanguage":"javascript"}
{"type":"before","callId":"hook@1","startTime":462.839,"class":"Test","method":"step","apiName":"Before Hooks","params":{},"stack":[]}
{"type":"before","callId":"hook@2","parentId":"hook@1","startTime":463.588,"class":"Test","method":"step","apiName":"beforeEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":36,"column":6}]}
{"type":"stdout","timestamp":468.67,"text":"==== 【po_automationca】Running test Smoke-DNCL Test with worker pid 550 on chromium=====\n"}
{"type":"after","callId":"hook@2","endTime":468.961,"attachments":[]}
{"type":"before","callId":"fixture@3","parentId":"hook@1","startTime":469.971,"class":"Test","method":"step","apiName":"fixture: browser","params":{},"stack":[]}
{"type":"before","callId":"pw:api@4","parentId":"fixture@3","startTime":471.724,"class":"Test","method":"step","apiName":"browserType.launch","params":{"handleSIGINT":"false","slowMo":"100","args":"[--disable-infobars, --disable-site-isolation-trials, --disable-web-security, --disable-popup-blocking, --disable-prompt-on-repost, --use-fake-ui-for-media-stream, --use-fake-device-for-media-stream, --use-file-for-fake-audio-capture, --suppress-message-center-popups, --enable-notifications, --flag-switches-begin, --enable-features=DownloadBubble, --flag-switches-end]","headless":"true","channel":"chromium","tracesDir":"/test-results/.playwright-artifacts-1/traces","ignoreDefaultArgs":"undefined","ignoreAllDefaultArgs":"false","env":"undefined"},"stack":[]}
{"type":"after","callId":"pw:api@4","endTime":603.715,"attachments":[]}
{"type":"after","callId":"fixture@3","endTime":603.778,"attachments":[]}
{"type":"before","callId":"fixture@5","parentId":"hook@1","startTime":604.59,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"before","callId":"pw:api@6","parentId":"fixture@5","startTime":606.957,"class":"Test","method":"step","apiName":"browser.newContext","params":{"acceptDownloads":"accept","bypassCSP":"false","colorScheme":"light","deviceScaleFactor":"1","hasTouch":"false","ignoreHTTPSErrors":"false","isMobile":"false","javaScriptEnabled":"true","locale":"en-US","offline":"false","permissions":"[microphone, notifications, camera]","storageState":"Object","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36","viewport":"Object","baseURL":"https://po-automationca.mytalkdeskca.com","serviceWorkers":"allow","recordVideo":"Object","noDefaultViewport":"false","extraHTTPHeaders":"undefined","recordHar":"undefined","reducedMotion":"undefined","forcedColors":"undefined","clientCertificates":"undefined"},"stack":[]}
{"type":"after","callId":"pw:api@6","endTime":1313.041,"attachments":[]}
{"type":"after","callId":"fixture@5","endTime":1319.217,"attachments":[]}
{"type":"before","callId":"fixture@7","parentId":"hook@1","startTime":1319.66,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"before","callId":"pw:api@8","parentId":"fixture@7","startTime":1321.184,"class":"Test","method":"step","apiName":"browserContext.newPage","params":{},"stack":[]}
{"type":"after","callId":"pw:api@8","endTime":1358.839,"attachments":[]}
{"type":"after","callId":"fixture@7","endTime":1358.993,"attachments":[]}
{"type":"after","callId":"hook@1","endTime":1359.045,"attachments":[]}
{"type":"before","callId":"test.step@9","startTime":1371.754,"class":"Test","method":"step","apiName":"Smoke-RCX-1304: Upload a Do Not Call List","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":389,"column":16}]}
{"type":"before","callId":"test.step@10","parentId":"test.step@9","startTime":1372.816,"class":"Test","method":"step","apiName":"Step1-Click on the Dialer APP.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":407,"column":20}]}
{"type":"before","callId":"pw:api@11","parentId":"test.step@10","startTime":1400.503,"class":"Test","method":"step","apiName":"page.goto","params":{"url":"/atlas/apps/outbound-dialer","waitUntil":"load"},"stack":[{"file":"/pages/basePage.js","line":228,"column":23,"function":"DialerPage.retry_goto"}]}
{"type":"before","callId":"pw:api@12","parentId":"test.step@10","startTime":1402.666,"class":"Test","method":"step","apiName":"page.waitForNavigation","params":{"info":"Object"},"stack":[{"file":"/pages/basePage.js","line":229,"column":29,"function":"DialerPage.retry_goto"}]}
{"type":"after","callId":"pw:api@12","endTime":2280.959,"attachments":[]}
{"type":"before","callId":"pw:api@13","parentId":"test.step@10","startTime":2282.281,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@11","endTime":2388.256,"attachments":[]}
{"type":"after","callId":"pw:api@13","endTime":6090.534,"attachments":[]}
{"type":"before","callId":"pw:api@14","parentId":"test.step@10","startTime":6093.49,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":283,"column":37,"function":"DialerPage.waitForError_new"}]}
{"type":"after","callId":"pw:api@14","endTime":6377.869,"attachments":[]}
{"type":"before","callId":"pw:api@15","parentId":"test.step@10","startTime":6380.296,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@15","endTime":9465.865,"attachments":[]}
{"type":"before","callId":"pw:api@16","parentId":"test.step@10","startTime":9468.458,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@16","endTime":12493.933,"attachments":[]}
{"type":"before","callId":"pw:api@17","parentId":"test.step@10","startTime":12495.673,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":283,"column":37,"function":"DialerPage.waitForError_new"}]}
{"type":"after","callId":"pw:api@17","endTime":12514.698,"attachments":[]}
{"type":"before","callId":"pw:api@18","parentId":"test.step@10","startTime":12516.091,"class":"Test","method":"step","apiName":"page.evaluate","params":{"expression":"() => {\n      console.log(\"========START EXECUTE JQUERY=============\");\n      // let toastRoot = document.querySelector('div#toast');\n      const toastRoot = document.querySelector('div[data-testid=\"toaster\"]');\n      let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;\n      let mutationObserver = new MutationObserver(mutations => {\n        const toast = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid]');\n        const toast_content = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid] p').textContent;\n        console.log(toast);\n        if (toast_content.includes('Conversations is open in another tab or device')) {\n          console.log(\"=====find the conversation conflict toast======\");\n          toast.remove();\n        } else {\n          console.log(\"=======No need to fix this toast=======\");\n        }\n      });\n      mutationObserver.observe(toastRoot, {\n        childList: true,\n        //\n        // attributes: true, //\n        // characterData: true, //\n        subtree: true //\n        // attributesFilter: ['class', 'style'], //\n        // attributesOldValue: true, //\n        // characterDataOldValue: true //\n      });\n      console.log(\"========START EXECUTE JQUERY=============\");\n    }","isFunction":"true","arg":"Object"},"stack":[{"file":"/pages/basePage.js","line":486,"column":25,"function":"DialerPage.hiddenConflictToast"}]}
{"type":"after","callId":"pw:api@18","endTime":12528.322,"attachments":[]}
{"type":"before","callId":"expect@19","parentId":"test.step@10","startTime":12530.248,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":329,"column":38,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@19","endTime":12547.158,"attachments":[]}
{"type":"before","callId":"pw:api@20","parentId":"test.step@10","startTime":12548.453,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"header.dock-drawer-component-module__header h4","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":414,"column":31,"function":"DialerPage.getPageTitle"}]}
{"type":"after","callId":"pw:api@20","endTime":12563.878,"attachments":[]}
{"type":"before","callId":"expect@21","parentId":"test.step@10","startTime":12564.73,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Dialer"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":330,"column":43,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@21","endTime":12565.246,"attachments":[]}
{"type":"before","callId":"pw:api@22","parentId":"test.step@10","startTime":12566.305,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> header h1","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":292,"column":47,"function":"DialerPage.waitForVisibleLocator"}]}
{"type":"after","callId":"pw:api@22","endTime":12590.329,"attachments":[]}
{"type":"before","callId":"pw:api@23","parentId":"test.step@10","startTime":12591.257,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":387,"column":44,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@23","endTime":12602.238,"attachments":[]}
{"type":"stdout","timestamp":12602.78,"text":"get Second Area Panel again\n"}
{"type":"before","callId":"pw:api@24","parentId":"test.step@10","startTime":12603.922,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div._pendo-step-container-size","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":389,"column":34,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@24","endTime":12617.003,"attachments":[]}
{"type":"stdout","timestamp":12617.067,"text":"the recommandAdv is hidden\n"}
{"type":"before","callId":"pw:api@25","parentId":"test.step@10","startTime":12618.127,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":396,"column":42,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@25","endTime":12814.545,"attachments":[]}
{"type":"before","callId":"pw:api@26","parentId":"test.step@10","startTime":12815.803,"class":"Test","method":"step","apiName":"locator.blur","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":397,"column":42,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@26","endTime":72931.358,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n","stack":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n\n    at DialerPage.waitForFrameLoad (/pages/outbound_dialer/dialerPage.js:397:42)\n    at DialerPage.waitForCampaignPage (/pages/outbound_dialer/dialerPage.js:331:3)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:411:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:407:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:389:5"}}
{"type":"after","callId":"test.step@10","endTime":72931.505,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n","stack":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n\n    at DialerPage.waitForFrameLoad (/pages/outbound_dialer/dialerPage.js:397:42)\n    at DialerPage.waitForCampaignPage (/pages/outbound_dialer/dialerPage.js:331:3)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:411:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:407:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:389:5"}}
{"type":"after","callId":"test.step@9","endTime":72931.562,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n","stack":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n\n    at DialerPage.waitForFrameLoad (/pages/outbound_dialer/dialerPage.js:397:42)\n    at DialerPage.waitForCampaignPage (/pages/outbound_dialer/dialerPage.js:331:3)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:411:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:407:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:389:5"}}
{"type":"error","message":"TimeoutError: locator.blur: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button.secondary-area-module__active')\u001b[22m\n","stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":397,"column":42,"function":"DialerPage.waitForFrameLoad"},{"file":"/pages/outbound_dialer/dialerPage.js","line":331,"column":3,"function":"DialerPage.waitForCampaignPage"},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":411,"column":13},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":407,"column":9},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":389,"column":5}]}
{"type":"before","callId":"hook@27","startTime":72932.11,"class":"Test","method":"step","apiName":"After Hooks","params":{},"stack":[]}
{"type":"before","callId":"pw:api@28","parentId":"hook@27","startTime":72933.725,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mode":"only-on-failure","fullPage":"true","timeout":"5000","path":"/test-results/smoke-test_campaign_record_flow_clone-Smoke-DNCL-Test-Chrome-ca-prod/test-failed-1.png","caret":"initial","mask":"undefined","type":"png"},"stack":[]}
{"type":"after","callId":"pw:api@28","endTime":73032.089,"attachments":[]}
{"type":"before","callId":"attach@29","parentId":"hook@27","startTime":73032.973,"class":"Test","method":"step","apiName":"attach \"screenshot\"","params":{},"stack":[]}
{"type":"after","callId":"attach@29","endTime":73033.06,"attachments":[{"name":"screenshot","contentType":"image/png","sha1":"2aae1606dc1df77bf13e18bae5703b9c81bea75a"}]}
{"type":"before","callId":"hook@30","parentId":"hook@27","startTime":73033.49,"class":"Test","method":"step","apiName":"afterEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":63,"column":6}]}
{"type":"stdout","timestamp":73033.807,"text":"==== 【po-automationca】【failed】Finished test Smoke-DNCL Test on chromium with status failed=====\n"}
{"type":"after","callId":"hook@30","endTime":73033.858,"attachments":[]}
{"type":"before","callId":"fixture@31","parentId":"hook@27","startTime":73034.355,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"after","callId":"fixture@31","endTime":73034.5,"attachments":[]}
{"type":"before","callId":"fixture@32","parentId":"hook@27","startTime":73034.714,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"after","callId":"fixture@32","endTime":73034.764,"attachments":[]}
{"type":"before","callId":"pw:api@33","parentId":"hook@27","startTime":73546.063,"class":"Test","method":"step","apiName":"video.saveAs","params":{"path":"/test-results/smoke-test_campaign_record_flow_clone-Smoke-DNCL-Test-Chrome-ca-prod/video.webm"},"stack":[]}
{"type":"after","callId":"pw:api@33","endTime":73547.316,"attachments":[]}
{"type":"before","callId":"attach@34","parentId":"hook@27","startTime":73547.746,"class":"Test","method":"step","apiName":"attach \"video\"","params":{},"stack":[]}
{"type":"after","callId":"attach@34","endTime":73547.789,"attachments":[{"name":"video","contentType":"video/webm","sha1":"43d43e622d3a89352f37aa5dd29d4107f7cf0cf1"}]}
{"type":"after","callId":"hook@27","endTime":73548.7,"attachments":[]}
{"type":"before","callId":"hook@35","startTime":73548.924,"class":"Test","method":"step","apiName":"Worker Cleanup","params":{},"stack":[]}
{"type":"before","callId":"fixture@36","parentId":"hook@35","startTime":73549.293,"class":"Test","method":"step","apiName":"fixture: browser","params":{},"stack":[]}
{"type":"after","callId":"fixture@36","endTime":73589.58,"attachments":[]}
{"type":"after","callId":"hook@35","endTime":73589.909,"attachments":[]}