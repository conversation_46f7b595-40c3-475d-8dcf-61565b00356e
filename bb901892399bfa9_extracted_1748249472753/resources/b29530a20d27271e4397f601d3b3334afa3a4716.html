<!doctype html><html lang="en"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no"/><script src="../configs.js"></script><script>const AZORES_LIB_ENV_KEY = '__AZORES_LIB__'
      const CONVERSATION_APP_ENV_KEY = '__CONVERSATION_APP__'
      window.env = {
        [AZORES_LIB_ENV_KEY]: {"RELEASE_STAGE":"production","CLIENT_VERSION":"@conversation-app/runtime@3.37.0","LIB_DEVELOPMENT":false,"USE_CTI_SERVICE":true,"LIB_LOGGER_ENABLED":true,"LOG_LEVEL":"Info","APP_NAME":"conversation-app/runtime","PLATFORM":"browser","ORGANIZATION":"Talkdesk","DISABLED_FEATURES":["OFFER_RINGING","OMNICHANNEL","SCHEDULE_CALLBACK","ADD_RECORD_TO_DNCL","FETCH_AGENT_PHONES"],"ACCEPT_INCOMING_WHILE_BUSY":true},
        [CONVERSATION_APP_ENV_KEY]: {"ENVIRONMENT":"production","LOG_LEVEL":"Info","CLIENT_VERSION":"3.37.0","ATLAS_SDK_VERSION":"0.46.1","CDN_BASE_URL":"https://stg-cdn-talkdesk.talkdeskdev.com","TAB_READY_TIMEOUT":30000,"AUTOPLAY_CHECK_INTERVAL":1000}
      }

      if(window.Configs){
        console.info('Conversation App - Adding env configs')
        function addCdnEnvKeyToEnvKey(app, cdnEnvKey){
            const envKey = cdnEnvKey.replace(app, '')
            window.env[app][envKey] = window.Configs[cdnEnvKey]
        }

        Object.keys(window.Configs).forEach((cdnEnvKey) => {
            if(cdnEnvKey.startsWith(AZORES_LIB_ENV_KEY)){
              addCdnEnvKeyToEnvKey(AZORES_LIB_ENV_KEY, cdnEnvKey)
            }else if(cdnEnvKey.startsWith(CONVERSATION_APP_ENV_KEY)){
              addCdnEnvKeyToEnvKey(CONVERSATION_APP_ENV_KEY, cdnEnvKey)
            }
        })
      }

      var buildEnvs = {"ENVIRONMENT":"production","CLIENT_VERSION":"3.37.0","BUILD_TIME":"2025-05-19T09:30:09.248Z","GIT_COMMIT":"conversation-app_runtime_3.37.0"}
      console.info('Conversation App Runtime - v'+ buildEnvs.CLIENT_VERSION +' - '+ buildEnvs.GIT_COMMIT +' - '+ buildEnvs.BUILD_TIME)</script><title>Conversation App</title><script defer="defer" src="main.17805132c5224d076ed8.js"></script></head><body style="margin: 0"><div id="app" style="height: 100%"/></body></html>