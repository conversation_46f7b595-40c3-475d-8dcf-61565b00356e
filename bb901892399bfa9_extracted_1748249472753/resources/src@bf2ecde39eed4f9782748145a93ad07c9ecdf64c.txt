// const {attachment, step } = require("allure-decorators")
// const {allure} = require("allure-js-commons")
// const AllureRuntime = require("allure-mocha/runtime")
// const AllureRuntime = require("allure-mocha/runtime")
// const {allure} = require("allure-mocha/dist/MochaAllureReporter")
const {allureStep} = require("../../api/deco")
const {BasePage} = require("../basePage");
const {expect} = require("@playwright/test");

exports.DialerPage = class DialerPage extends BasePage{

	constructor(page,test_ff) {
		super(page);
		// this.page = page
		this.test_ff=test_ff
		this.campID_old='https:(.*?)\/campaigns\/\\d{1,9}(.*?)'
		this.campID_new = 'https:(.*?)\/campaigns\/[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}(.*?)'
		this.campDetail_pattern = test_ff.PO_NEW_CAMP_UUID?this.campID_new:this.campID_old

		this.pageTitle = page.locator("header.dock-drawer-component-module__header h4")
		this.campaignsTab = page.locator("[data-testid=\"dock-button-outbound-dialer-campaigns\"]")
		this.listsTab = page.locator("[data-testid=\"dock-button-outbound-dialer-lists\"]")
		this.configurationsTab = page.locator("[data-testid=\"dock-button-outbound-dialer-configurations\"]")
		// this.recommandAdv = page.locator("div._pendo-step-container-size")
		// this.closeRecommandAdvButton = this.recommandAdv.locator("button._pendo-close-guide")
		this.agentAssistPanel = page.locator("div#secondary-area")
		this.closeAssistButton = this.agentAssistPanel.locator("[data-testid=\"secondary-area-header\"] button:not([data-testid])")
		this.dialerFrame = page.frameLocator("iframe[data-testid=\"frame-outbound-dialer\"]")

		this.emptyBody = this.dialerFrame.locator("div#root:not(:has(div))")
		this.commonTable = this.dialerFrame.locator("table")
		this.commonTableHeader = this.commonTable.locator("thead")
		this.commonTableBody = this.commonTable.locator("tbody")
		this.noResultFoundComments = this.dialerFrame.locator(`h2:text-matches("^No (.*?) found$")`)
		// this.noResultFoundComments = this.dialerFrame.locator("h2:text(\"No campaigns found\")")
		this.lessThanThreeComments = this.dialerFrame.locator("p:text(\"Type at least 3 characters\")")
		this.noCampaignsYetComments = this.dialerFrame.locator("h2:text(\"No campaigns yet\")")
		this.commonPanel = this.dialerFrame.locator("div#panels-layout-content-id")
		// this.timerToaster = this.page.locator("div[data-testid=\"toaster\"]>div:has(div.co-message__timer)")


		/*
		Campaigns List Page Locator
		*/

		//Header
		this.campTitle = this.dialerFrame.locator("header h1")
		this.dialerPagePermissionBlock=this.dialerFrame.locator("div#root header+div h2")
		this.updateRecord = this.dialerFrame.locator("div.co-grid__column.co-grid__column--min:has-text(\"Last updated\")")
		this.refreshBtn = this.dialerFrame.locator("div:text(\"Refresh\")")
		this.createCampaignBtn = this.dialerFrame.locator("div button[data-pendo-campaignlist-header-createbutton]")
		//TopToolbar
		this.topToolbar = this.dialerFrame.locator("div.co-toolbar")
		this.totalListNum = this.topToolbar.locator("div h5")
		this.topSearchInput = this.topToolbar.locator("input")
		this.clearSearchkeywordButton = this.topToolbar.locator("input+div>i")
		this.topFilterButton = this.topToolbar.locator("button")
		//FilterToolbar
		this.filterToolBar = this.dialerFrame.locator("header+div.co-toolbar+div.co-toolbar")
		this.filterClearAllButton = this.filterToolBar.locator("button:text(\"Clear all\")")
		//PaginationBar
		this.navBar = this.dialerFrame.locator("div[data-testid=\"pagination-toolbar\"]")
		this.navPreviousButton = this.navBar.locator("ul>li>a[aria-label=\"Previous\"]")
		this.navNextButton = this.navBar.locator("ul>li>a[aria-label=\"Next\"]")
		//Campaigns List Filter Panel
		this.campaignFilterPanel = this.dialerFrame.locator("#CAMPAIGNS_SIDE_PANEL")
		this.filterOptionSelectorList = this.dialerFrame.locator("div[data-co-name=\"Popup\"] ul li")
		this.filterApplyButton = this.campaignFilterPanel.locator("button[data-testid=\"applyButton\"]")
		this.filterCancelButton = this.campaignFilterPanel.locator("button[data-testid=\"clearButton\"]")
		this.closePanelButton = this.campaignFilterPanel.locator("#CAMPAIGNS_SIDE_PANEL header button")




		/*
		Common Dialog Locator
		*/
		this.commonDialog = this.dialerFrame.locator("div.co-modal__dialog")
		this.closeDialogButton = this.commonDialog.locator("footer+button.co--dismiss")
		this.cancelBtn = this.commonDialog.locator("button[data-testid=\"cancelButton\"]" )



		//Create Campaigns Dialog Locator
		this.createCampaignDialog = this.commonDialog
		this.createCampaignDialogTitle = this.createCampaignDialog.locator("header h2.co-heading")
		this.campaignNameInput = this.createCampaignDialog.locator("#name-form-field-id")
		this.campaignNameInput_feedback =  this.createCampaignDialog.locator("input#name-form-field-id+div.co-form__feedback")
		this.campaignTeamLabel = this.createCampaignDialog.locator("form.co-form label[data-co-name=\"Label\"]")
		this.campaignTeamDropdown = this.createCampaignDialog.locator("form.co-form>div[data-co-name=\"Box\"] div[data-co-name=\"FakeInput\"]")
		this.confirmBtn = this.createCampaignDialog.locator("button[data-testid=\"confirmButton\"]" )



		//Start Campaign Dialog Locator
		this.startCampainDialog = this.commonDialog
		this.startCampainDialogTitle = this.startCampainDialog.locator("h2")
		this.startCampainDialogConfirmStartButton = this.startCampainDialog.locator("button[data-testid=\"submitButton\"]")

		//Delete Campaign Dialog Locator
		this.deleteCampainDialog = this.commonDialog
		this.deleteCampainDialogTitle = this.deleteCampainDialog.locator("h3")
		this.deleteCampainDialogConfirmDeleteButton = this.deleteCampainDialog.locator("button[aria-label=\"Delete\"]")
		this.deleteCampaignDialogCloseCancelButton = this.deleteCampainDialog.locator("button[aria-label=\"Cancel\"]")


		/*
		Create Campaigns Step
		1) Mode Setting - Configure the dialing mode properties of this campaign
		2) Dailing strategy settings - Configure the dialing strategy for this campaign
		3) Agents settings - Assign agents to work on the campaign
		4) List settings - Define the audience of this campaign
		*/

		//common locator
		this.backToCampaignsListButton = this.dialerFrame.locator("a.co-header__link--back")
		this.campaignsStatus = this.dialerFrame.locator("header.co-header div.co-heading__pre-title>span")
		this.stepNavBar = this.dialerFrame.locator("nav.co-nav>ul")
		this.modeNavTab = this.stepNavBar.locator("li:nth-child(1)")
		this.dialingStrategyNavTab = this.stepNavBar.locator("li:nth-child(2)")
		this.AgentNavTab = this.stepNavBar.locator("li:nth-child(3)")
		this.ListsNavTab = this.stepNavBar.locator("li:nth-child(4)")
		this.reportNavTab = this.stepNavBar.locator("li:nth-child(5)")

		this.defaultPriorityLevel = 5
		this.discardBtn = this.dialerFrame.locator("[data-testid=\"discardButton\"]")
		this.previousBtn = this.dialerFrame.locator("[data-testid=\"previousButton\"]")
		this.nextBtn = this.dialerFrame.locator("[data-testid=\"nextButton\"]")
		this.createBtn = this.dialerFrame.locator("[data-testid=\"submitButton\"]")
		this.discardCreateCampaignDialog = this.dialerFrame.locator("div.co-modal__dialog")
		this.discardCreateCampaignDialogContent = this.discardCreateCampaignDialog.locator("h3.co-heading")
		this.discardCreateCampaignDiscardBtn = this.discardCreateCampaignDialog.locator("button[data-testid=\"discard-button\"]" )
		this.discardCreateCampaignCancelBtn = this.discardCreateCampaignDialog.locator("button[data-testid=\"cancel-button\"]" )


		//Dialing Mode settings
		this.campainPrioritylist = this.dialerFrame.locator("div[id=\"priority-id\"]")
		this.campaigns_setting_title = this.dialerFrame.locator("div.co-grid__column.co-grid__column--min>h2.co-heading")

		this.dialingModeSection = this.dialerFrame.locator("div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"])")
		this.dialingModeOption = this.dialingModeSection.locator("div#dialDropdown")
		this.dialingModeList = this.dialingModeOption.locator("ul")

		this.dialingRatioSection = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxdialingratioinput]")
		this.dialingRatioInput = this.dialerFrame.locator("#max-dialing-ratio-id-input")
		this.dialingRatioInput_feedback = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxdialingratioinput]>div.co-form__feedback")

		this.abandonmentRateSection = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxabandrateinput]")
		this.abandonmentRateInput = this.dialerFrame.locator("#max-abandonment-rate-id-input")
		this.abandonmentRateInput_feedback = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxabandrateinput]>div.co-form__feedback")

		this.abandonmentTimeoutSection = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-abandtimeoutinput]")
		this.abandonmentTimeoutInput = this.dialerFrame.locator("#abandonment-timeout-id-input")
		this.abandonmentTimeoutInput_feedback = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-abandtimeoutinput]>div.co-form__feedback")

		this.maxRingtimeSection = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxringtimeinput]")
		this.maxRingtimeInput = this.dialerFrame.locator("#max-ring-time-id-input")
		this.maxRingtimeInput_feedback = this.dialerFrame.locator("div[data-pendo-campaignform-modetab-maxringtimeinput]>div.co-form__feedback")

		this.answerMachineDetectSection = this.dialerFrame.locator("input[data-pendo-campaignform-modetab-amdinput]")
		this.answerMachineDetectEnableSection = this.dialerFrame.locator("input[data-pendo-campaignform-modetab-amdinput]:checked")



		//Dailing strategy settings
		this.callIDslink = this.dialerFrame.locator("div.co-section__content:has(label[for=\"caller-id-chip-group\"]) a[href=\"#\"]>span")
		this.callerIDsTable = this.commonPanel.locator("table")
		this.callerIDsSelectButton = this.commonPanel.locator("button>*:text(\"Select\")")

		this.strategyTypeSection = this.dialerFrame.locator("div.co-section__content:has(label[for=\"dialing_strategy-id\"])")
		this.strategyTypeOption = this.strategyTypeSection.locator("div#dialing_strategy-id")
		this.strategyTypeList = this.strategyTypeOption.locator("ul")

		this.connectionTimeoutSection = this.dialerFrame.locator("div.co-section__content:has(label[for=\"labeled-number-input-Connection timeout\"])")
		this.connectionTimeoutInput = this.connectionTimeoutSection.locator("input")
		this.connectionTimeoutInput_feedback = this.connectionTimeoutSection.locator("div>div>div:nth-child(3) div>span[data-co-name=\"Text\"]")

		this.maxSkipsPerRecordSection = this.dialerFrame.locator("div.co-section__content:has(label[for=\"labeled-number-input-Maximum skips per record\"])")
		this.maxSkipsPerRecordInput = this.maxSkipsPerRecordSection.locator("input")
		this.maxSkipsPerRecordInput_feedback = this.maxSkipsPerRecordSection.locator("div>div>div:nth-child(3) div>span[data-co-name=\"Text\"]")




		this.callHoursLable = this.dialerFrame.locator("div>label:not([for]):has(abbr)")
		this.callHourHit = this.dialerFrame.locator("div.co-form__hint")
		this.callFrom = this.dialerFrame.locator("//div[text()=\"to\"]/preceding-sibling::div")
		this.callTo = this.dialerFrame.locator("//div[text()=\"to\"]/following-sibling::div[position()=1]")
		this.callFromHoursInput_old = this.dialerFrame.locator("div.sc-bdVaJa.cxEcVj input")
		this.callFromHoursInput_new = this.callFrom.locator("[data-testid=\"timeInput\"] [data-testid=\"expandTimeOption\"]")
		this.callFromHoursInput = test_ff.PO_NEW_CALLING_HOUR?this.callFromHoursInput_new:this.callFromHoursInput_old
		this.FromHours_hour_old = this.dialerFrame.locator("#timepicker-in-popup-id-From-hours input")
		this.FromHours_hour_new = this.callFrom.locator("[data-testid=\"hourInput\"]")
		this.FromHours_hour = test_ff.PO_NEW_CALLING_HOUR?this.FromHours_hour_new:this.FromHours_hour_old
		this.FromHours_minute_old = this.dialerFrame.locator("#timepicker-in-popup-id-From-minutes input")
		this.FromHours_minute_new = this.callFrom.locator("[data-testid=\"minutesInput\"]")
		this.FromHours_minute = test_ff.PO_NEW_CALLING_HOUR?this.FromHours_minute_new:this.FromHours_minute_old
		this.callFrom_meridian_old = this.dialerFrame.locator("[for=\"timepicker-in-popup-id-From-meridian\"]")
		this.callFrom_meridian_new = this.callFrom.locator("[data-testid=\"test-meridian\"]")
		this.callFrom_meridian = test_ff.PO_NEW_CALLING_HOUR?this.callFrom_meridian_new:this.callFrom_meridian_old

		this.callToHoursInput_old = this.dialerFrame.locator("div.sc-bdVaJa.gpIkRm input")
		this.callToHoursInput_new = this.callTo.locator("[data-testid=\"timeInput\"] [data-testid=\"expandTimeOption\"]")
		this.callToHoursInput = test_ff.PO_NEW_CALLING_HOUR?this.callToHoursInput_new:this.callToHoursInput_old
		this.ToHours_hour_old = this.dialerFrame.locator("#timepicker-in-popup-id-To-hours input")
		this.ToHours_hour_new = this.callTo.locator("[data-testid=\"hourInput\"]")
		this.ToHours_hour = test_ff.PO_NEW_CALLING_HOUR?this.ToHours_hour_new:this.ToHours_hour_old
		this.ToHours_minute_old = this.dialerFrame.locator("#timepicker-in-popup-id-To-minutes input")
		this.ToHours_minute_new = this.callTo.locator("[data-testid=\"minutesInput\"]")
		this.ToHours_minute = test_ff.PO_NEW_CALLING_HOUR?this.ToHours_minute_new:this.ToHours_minute_old
		this.callTo_meridian_new = this.callTo.locator("[data-testid=\"test-meridian\"]")
		this.callTo_meridian_old = this.dialerFrame.locator("[for=\"timepicker-in-popup-id-To-meridian\"]")
		this.callTo_meridian = test_ff.PO_NEW_CALLING_HOUR?this.callTo_meridian_new:this.callTo_meridian_old

		// this.callHours_feedback = this.dialerFrame.locator("div.co-form__field.co--error:has(div.sc-bdVaJa.cxEcVj)+div>div.co-form__feedback")
		this.callHours_feedback_old = this.dialerFrame.locator("div.co-form__field.co--error>div.co-form__feedback")
		this.callHours_feedback_new = this.dialerFrame.locator("div.co-form__field section.co-section+div").first()
		this.callHours_feedback = test_ff.PO_NEW_CALLING_HOUR?this.callHours_feedback_new:this.callHours_feedback_old
		this.callHours_wrongtime_text_old = " Please select valid calling hours with at least one weekday and a time frame from 12:00 AM to 11:59 PM"
		this.callHours_wrongtime_text_new = "Please choose an end time later than the start time"
		this.callHours_wrongtime_text = test_ff.PO_NEW_CALLING_HOUR?this.callHours_wrongtime_text_new:this.callHours_wrongtime_text_old



		this.globalMaxAttemptsSection_old = this.dialerFrame.locator("//label[@for='max-attempts-per-record-id']/parent::div")
		this.globalMaxAttemptsSection_new = this.dialerFrame.locator("table:has(#max-attempts-per-record-id-group):nth-child(1)")
		this.globalMaxAttemptsSection = test_ff.PO_NEW_RETRIES_UI_ENABLED?this.globalMaxAttemptsSection_new:this.globalMaxAttemptsSection_old
		this.globalMaxAttemptsInput = this.dialerFrame.locator("#max-attempts-per-record-id-input")
		this.globalMaxAttemptsInput_feedback = this.globalMaxAttemptsSection.locator("div.co-form__feedback")

		this.retryPeriodSection_old = this.dialerFrame.locator("//label[@for='retry-period-id']/parent::div")
		this.retryPeriodSection_new = this.dialerFrame.locator("table:has(#retry-period-id-group):nth-child(1)")
		this.retryPeriodSection = test_ff.PO_NEW_RETRIES_UI_ENABLED?this.retryPeriodSection_new:this.retryPeriodSection_old
		this.retryPeriodInput = this.dialerFrame.locator("#retry-period-id-input")
		this.retryPeriodTypeOption = this.dialerFrame.locator("#retry-period-id-dropdown")

		this.retryPeriodTypeList_old = this.retryPeriodTypeOption.locator("div.co-dropdown__menu ul")
		this.retryPeriodTypeList_new = this.dialerFrame.locator("div[data-transition=\"entered\"][data-co-name=\"Popup\"] ul")
		this.retryPeriodTypeList = test_ff.PO_NEW_RETRIES_UI_ENABLED?this.retryPeriodTypeList_new:this.retryPeriodTypeList_old
		this.retryPeriodInput_feedback = this.retryPeriodSection.locator("div.co-form__feedback")

		this.recordChainingStrategyCheck = this.dialerFrame.locator("input#record-chain-custom-toggle")


		this.callDispositionLink = this.dialerFrame.locator("p.co--microcopy>a")
		this.systemDispositionsType_Busy = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-busy]")
		this.systemDispositionsType_NoAnswer = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-noanswer]")
		this.systemDispositionsType_Abandoned = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-abandonment]")
		this.systemDispositionsType_HangupBeforeConnection = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-hangupbeforeconnection]")
		this.systemDispositionsType_AnswerMachineDetection = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-answeringmachinedetection]")
		this.systemDispositionsType_InvalidNumber = this.dialerFrame.locator("li[data-pendo-campaignform-dialingstrategy-sd-invalidnumber]")
		this.radioFinalButton = this.dialerFrame.locator("input[data-testid=\"radio-final\"]")
		this.radioRetryButton = this.dialerFrame.locator("input[data-testid=\"radio-retry\"]")
		this.systemDispositionsApplyButton = this.dialerFrame.locator("[data-testid=\"disposition-configurator-box\"] button:has(span:text(\"Apply\"))")
		this.systemDispositionsClearButton = this.dialerFrame.locator("[data-testid=\"disposition-configurator-box\"] button:has(span:text(\"Clear\"))")


		//Agents settings
		this.tableToolBar = this.dialerFrame.locator("div.co-toolbar:not([data-testid])")
		this.selectedAgentCount = this.tableToolBar.locator("div:has(h5)+div+div:not(:has(div))")
		this.agentTotalCount = this.tableToolBar.locator("h5.co-heading")

		this.agentSearchInput = this.dialerFrame.locator("input[data-co-name=\"Input\"]")
		this.agentSearchInputClearButton = this.dialerFrame.locator("input[data-co-name=\"Input\"]+div")
		this.agentSearchInputPop = this.dialerFrame.locator("div[data-transition=\"entered\"]>div>p")
		this.agentSearchError = this.dialerFrame.locator("div.co-empty-state h2")
		this.tableOptionBtn = this.dialerFrame.locator("//*[contains(text(),\"Table options\")]")
		this.filterBtn = this.dialerFrame.locator("//*[contains(text(),\"Filters\")]")
		this.agentListToolBar = this.dialerFrame.locator("div.co-toolbar+div.co-toolbar")
		this.clearFilterSettingsButton = this.agentListToolBar.locator("button>span.co--truncate")

		this.ringGroupbox = this.dialerFrame.locator("div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]")
		this.ringGroupSearchInput = this.ringGroupbox.locator("input[type=\"search\"]")
		this.ringGroupSelection = this.ringGroupbox.locator("div.co-dropdown__menu ul.co-list")
		this.panelApplyBtn = this.commonPanel.locator("button[data-testid=\"applyButton\"]")

		this.panelCancelBtn_old = this.commonPanel.locator("button[data-testid=\"clearButton\"]")
		this.panelCancelBtn_new = this.commonPanel.locator("button[data-testid=\"cancelButton\"]")
		this.panelCancelBtn = test_ff.PO_NEW_PANEL?this.panelCancelBtn_new:this.panelCancelBtn_old
		this.panelCloseBtn_old = this.commonPanel.locator("[aria-label=\"close\"]")
		this.panelCloseBtn_new = this.commonPanel.locator("[data-testid=\"closeButton\"]")
		this.panelCloseBtn = test_ff.PO_NEW_PANEL?this.panelCloseBtn_new:this.panelCloseBtn_old


		// Define the audience of this campaign
        this.recordsListAddButton = this.dialerFrame.locator("div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")")
        this.selectedRecordlistTable = this.dialerFrame.locator("div.co-section__content:has(h5:text(\"Record lists\")) table")
		this.selectedRecordlistTableHeader = this.selectedRecordlistTable.locator("thead")
		this.selectedRecordlistTableBody = this.selectedRecordlistTable.locator("tbody")

		this.recordsListTable = this.commonPanel.locator("table")
		this.removeRecordslistDialog = this.dialerFrame.locator("div.co-modal__dialog")
		this.confirmRemoveRecordsListButton = this.removeRecordslistDialog.locator("[data-pendo-campaignform-lists-removerlistbutton-confirm]")
		this.recordsListPanelToolbar_old = this.commonPanel.locator("header+div.co-toolbar")
		this.recordsListPanelToolbar_new = this.commonPanel.locator("header+div>div.co-toolbar")
		this.recordsListPanelToolbar = test_ff.PO_NEW_PANEL?this.recordsListPanelToolbar_new:this.recordsListPanelToolbar_old
		this.recordsListTotalNum = this.recordsListPanelToolbar.locator("div>div:nth-child(1) h5")
		this.recordsListSelectedNum = this.recordsListPanelToolbar.locator("div>div:nth-child(2)")
		// this.recordsListSelectedNum_old = this.commonPanel.locator("header+div>div>div:nth-child(2)")
		// this.recordsListSelectedNum_new = this.commonPanel.locator("header+div>div>div>div:nth-child(2)")
		// this.recordsListSelectedNum = test_ff.PO_NEW_PANEL?this.recordsListSelectedNum_new:this.recordsListSelectedNum_old
        this.recordsListSearchInput = this.commonPanel.locator("input[placeholder=\"Search by list name\"]")
        this.recordsListApplyButton = this.commonPanel.locator("button>*:text(\"Apply\")")
        this.recordsListCloseButton_old = this.commonPanel.locator("header button[aria-label=\"close\"]")
        this.recordsListCloseButton_new = this.commonPanel.locator("header button[data-testid=\"closeButton\"]")
		this.recordsListCloseButton = test_ff.PO_NEW_PANEL?this.recordsListCloseButton_new:this.recordsListCloseButton_old
        this.recordsListCancelButton = this.commonPanel.locator("[data-testid=\"cancelButton\"]")

		this.dncListAddButton = this.dialerFrame.locator("div.co-section__content:has(h5:text(\"Do not call lists\")) button>span:text(\"Add DNC lists\")")
		this.selectedDNClistTable = this.dialerFrame.locator("div.co-section__content:has(h5:text(\"Do not call lists\")) table")
		this.selectedDNClistTableHeader = this.selectedDNClistTable.locator("thead")
		this.selectedDNClistTableBody = this.selectedDNClistTable.locator("tbody")

		// Reporting - Analyze how your campaign is performing
		this.openCampaignDashboardButton = this.dialerFrame.locator("button[data-testid=\"openCampaignDashboardButton\"]")
		this.openCallsReportButton = this.dialerFrame.locator("button[data-testid=\"openCallsReportButton\"]")



	}

	/*
	Dialer Campaigns Page general method
	*/

	// @allureStep
	async waitForCampaignPage(){
		await this.retry_goto("outbound-dialer")
		await expect(await this.pageTitle).toBeVisible()
		await expect(await this.getPageTitle()).toEqual("Dialer")
		await this.waitForFrameLoad()
		await this.waitAndClearToast()
	}

	async waitForFrameLoad(timeout=10){
		const locator_list = [
			this.campTitle,
			this.recommandAdv,
			this.secondAreaButtonActived,
			this.errorPageTitle,
			this.frameloading,
			this.emptyBody,

		]
		while(timeout>0){
			const visibleLocator = await this.waitForVisibleLocator(locator_list)
			if(visibleLocator){
				// console.log("visibleLocator is "+visibleLocator)
				if(visibleLocator===this.secondAreaButtonActived){
					console.log("get Second Area Panel!")
					// await this.wait_for_timeout(3)
					await this.secondAreaButtonActived.click()
					await this.secondAreaButtonActived.blur()
					await this.wait_for_timeout(1)
					await this.waitForFrameLoad(timeout)
				}
				else if(visibleLocator===this.frameloading){
					console.log("get frameloading!")
					await this.wait_for_timeout(3)
					await this.waitForFrameLoad(timeout)
				}
				else if(visibleLocator===this.errorPageTitle){
					console.log("get error500!")
					await this.errorRfreshButton.click()
					await this.wait_for_timeout(3)
					await this.waitForFrameLoad(eval(timeout-1))
				}
				else if(visibleLocator===this.emptyBody){
					console.log("get empty page!")
					await this.page.reload()
					await this.wait_for_timeout(3)
					await this.waitForFrameLoad(eval(timeout-1))
				}
				// else if(visibleLocator===this.agentAssistPanel){
				// 	console.log("get Agent Assist Panel")
				// 	await this.closeAssistButton.click()
				// 	await this.waitForFrameLoad(timeout)
				// }
				else if(visibleLocator===this.campTitle){
					// if(await this.recommandAdv.isVisible()){
					// 	console.log("get recommandAdv again")
					// 	await this.closeRecommandAdvButton.click()
					// }else{
					// 	console.log("the recommandAdv is hidden")
					// }

					if(await this.secondAreaButtonActived.isVisible()){
						console.log("get Second Area Panel again")
						if(await this.recommandAdv.isVisible()){
							console.log("get recommandAdv again")
							// await this.closeRecommandAdvButton.click()
							await this.closePendoDialog()
						}else{
							console.log("the recommandAdv is hidden")
						}
						await this.secondAreaButtonActived.click()
						await this.secondAreaButtonActived.blur()
					}else{
						console.log("the Second Area Panel is hidden")
					}
					console.log("The frame content load successfully.")
					return;
				}
			}else{
				console.log("reload page to refresh")
				await this.page.reload()
				timeout-=1
			}
		}

	}

	async getPageTitle(){
		return await this.pageTitle.textContent()
		await this.pageTitle.selectText()
	}

	async navToRecordsList(){

        let re = new RegExp('https:(.*?)\/record-lists\\?')
        const [response] = await Promise.all([
            this.page.waitForResponse(response=>re.test(response.url())&& response.request().method() === "GET" && response.status()===200),
			await this.listsTab.click()
        ])
        const resp_json = await response.json()
        const total = resp_json.total
        if(total>0){
            await this.wait_for_locator(this.commonTable)
            await this.wait_for_animated(this.commonTable)
            return total
        }


    }



	/*
	Dialer Campaigns List method
	* 1) campaigns list page info
	* 2) check pagination method
	* 3) check search campaigns method
	* 4) duplicate campaign method
	* 5) start campaign method
	* 6) pause campaign method
	* 7) resume campaign method
	* 8) delete campaign method
	*/


	// 1) check campaigns list page info
	async checkForCampainsListPerPage(){
		let re = new RegExp('https:(.*?)\/campaigns\\?')
		const [request] = await Promise.all([
			this.page.waitForRequest(request => re.test(request.url()) && request.method() === "GET"),
			this.refreshBtn.click()
		])
		const api_item = request.url().split("?")[1].split("&")[0].split("=")[1]
		expect(api_item).toEqual("10")
	}

	async checkForCampainsListToal(total){
		let re = new RegExp('https:(.*?)\/campaigns\\?')
		const [response] = await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			this.refreshBtn.click()
		])
		const resp_json = await response.json()
		expect(total).toEqual(resp_json.total.toString())
	}

	async checkForCampainsListOrder(){
		let re = new RegExp('https:(.*?)\/campaigns\\?')
		const [response] = await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			this.refreshBtn.click()
		])
		const resp_json = await response.json()
		// console.log("resp_json is "+resp_json)
		const camplist = resp_json._embedded.campaigns
		// console.log(camplist.length)
		for(let cam in camplist){
			if(parseInt(cam) === camplist.length - 1){
				break
			}
			let create_at_time_previous = camplist[cam]["created_at"]
			let create_at_time_next = camplist[parseInt(cam)+1]["created_at"]
			let create_timestamp_previous = Date.parse(create_at_time_previous)
			let create_timestamp_next = Date.parse(create_at_time_next)
			expect(create_timestamp_previous).toBeGreaterThanOrEqual(create_timestamp_next)
		}
	}

	async getCampaignName(TableItem){
		return await TableItem.locator("[href]>span").textContent()
	}

	async getCampaignsCountFromList(){
		if(await this.commonTableBody.isVisible()){
			await this.wait_for_animated(this.commonTable)
		}
		const statistic = await this.totalListNum.textContent()
		return statistic.split(" campaigns")[0]
	}

	async getFirstRowItem(columnName){
        const firstRowItem = await this.getTableItem(this.commonTable,"1")
        const nameColumnIndex = await this.getTableColumnIndex(this.commonTableHeader,columnName)
        // console.log("nameColumnIndex is "+nameColumnIndex)
        return await this.getTabelCellByIndex(this.commonTableHeader, firstRowItem, nameColumnIndex)
    }

	async clickCampaignName(){

		let re = new RegExp(this.campDetail_pattern);
		// let re = new RegExp('https:(.*?)\/campaigns\/\\d{1,9}$')
		await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.status() === 200 && response.request().method() === "GET"),
			await this.clickTabelCellByIndex(1, 1, this.commonTable, this.commonTableHeader, "a")
		])
	}


	// 2) check pagination method
	async checkPagination(perPage=10,total=20,maxItem=9){

		const myroute = /\/campaigns\?/
		let campaignsArray = []
		const fake_campaign = {
									"action_required": false,
									"created_at": "2022-10-21T08:03:47.668Z",
									"dialing_mode": "PREVIEW",
									"external_id": "",
									"id": "",
									"name": "metoto_test",
									priority: 10,
									run_once: false,
									status: "ready",
									teams: []
								  }
		for(let index=0;index<perPage;index+=1){
			campaignsArray.push(fake_campaign)
		}
		const navPageItemCount = Math.ceil(total/perPage)
		await this.page.route(myroute, (route => {
				route.fulfill({
					status: 200,
					contentType: 'application/json',
					body: JSON.stringify(
					{"_links":{"self":{}},
							"count":perPage,
							"per_page":perPage,
							"page":1,
							"total":total,
							"total_pages": navPageItemCount,
							"_embedded":{"campaigns":campaignsArray}
					})
				});
			}))
		await this.refreshBtn.click()
		if(total<=perPage){
			await expect(this.navBar).not.toBeVisible()
			console.error("Please set the total > perPage to check pagination feature")
			await this.page.unroute(myroute)
			return;
		}

		// console.log("navPageItemCount is "+navPageItemCount.toString())
		let navPageLocator;
		if(navPageItemCount<=maxItem){

			for(let index=2; index<=navPageItemCount+1; index+=1){
				navPageLocator = this.navBar.locator("ul>li:nth-child("+index.toString()+")")
				await expect(navPageLocator).toBeVisible()
				await expect(await navPageLocator.textContent()).toEqual((index-1).toString())
			}
		}else{
			for(let index=2; index<=maxItem-1; index+=1){
				navPageLocator = this.navBar.locator("ul>li:nth-child("+index.toString()+")")
				await expect(navPageLocator).toBeVisible()
				await expect(await navPageLocator.textContent()).toEqual((index-1).toString())
		}
			let lastNavPageLocator = this.navBar.locator("ul>li:nth-child("+(maxItem+1).toString()+")")
			await expect(lastNavPageLocator).toBeVisible()
			await expect(await lastNavPageLocator.textContent()).toEqual(navPageItemCount.toString())
		}

		await this.page.unroute(myroute)

	}

	async checkNavButtonBehavior(perPage=10,total=20, maxItem=9){
		const myroute_page1 = /\/campaigns\?items=10&page=1/
		const myroute_page2 = /\/campaigns\?items=10&page=2/

		let campaignsArray = []
		const fake_campaign = {
									"action_required": false,
									"created_at": "2022-10-21T08:03:47.668Z",
									"dialing_mode": "PREVIEW",
									"external_id": "",
									"id": "",
									"name": "metoto_test",
									priority: 10,
									run_once: false,
									status: "ready",
									teams: []
								  }
		for(let index=0;index<perPage;index+=1){
			campaignsArray.push(fake_campaign)
		}
		const navPageItemCount = Math.ceil(total/perPage)
		await this.page.route(myroute_page2, (route) => {
			route.fulfill({
					status: 200,
					contentType: 'application/json',
					body: JSON.stringify(
					{"_links":{"self":{}},
							"count":perPage,
							"per_page":perPage,
							"page":2,
							"total":total,
							"total_pages": navPageItemCount,
							"_embedded":{"campaigns":campaignsArray}
					})
				});
			})
		await this.page.route(myroute_page1, (route) => {
			route.fulfill({
					status: 200,
					contentType: 'application/json',
					body: JSON.stringify(
					{"_links":{"self":{}},
							"count":perPage,
							"per_page":perPage,
							"page":1,
							"total":total,
							"total_pages": navPageItemCount,
							"_embedded":{"campaigns":campaignsArray}
					})
				});
			})

		await this.refreshBtn.click()
		if(total<=perPage){
			await expect(this.navBar).not.toBeVisible()
			console.error("Please set the total > perPage to check pagination feature")
			await this.page.unroute(myroute_page1)
			await this.page.unroute(myroute_page2)
			return;
		}
		await this.checkNavNextButton()
		await this.checkNavPreviousButton()

		await this.page.unroute(myroute_page1)
		await this.page.unroute(myroute_page2)
	}

	async checkNavNextButton(){
		if(await this.navNextButton.isEnabled({timeout:3000})){
			let re = new RegExp('https:(.*?)\/campaigns\\?');
				const [request] = await Promise.all([
				  this.page.waitForRequest(request => re.test(request.url()) && request.method() === "GET"),
				  await this.navNextButton.click()
				]);
				const api_page = request.url().split("?")[1].split("&")[1].split("=")[1]
				await expect("2").toEqual(api_page)
		}else{
			console.error("The next button is disable!")
		}
	}

	async checkNavPreviousButton(){
		if(await this.navPreviousButton.isEnabled({timeout:3000})){
			let re = new RegExp('https:(.*?)\/campaigns\\?');
			const [request] = await Promise.all([
				  this.page.waitForRequest(request => re.test(request.url()) && request.method() === "GET",{timeout:30*1000}),
				  await this.navPreviousButton.click()
			]);
			const api_page = request.url().split("?")[1].split("&")[1].split("=")[1]
			await expect("1").toEqual(api_page)
			// await this.navPreviousButton.click()
		}else{
			console.error("The previous button is disable!")
		}

	}

	async checkCampaignsListColumn(){
		const rows = await this.commonTable.locator("tbody>tr").count()
		// const rows = await this.getListSize(this.commonTable)
		for(let index=1;index<=rows;index+=1){
			// console.log("=========="+index.toString()+"==========")
			const priorityValue = await this.getTabelCellValue("Priority",index, this.commonTable,this.commonTableHeader)
			expect(parseInt(priorityValue)).toBeLessThanOrEqual(10)
			expect(parseInt(priorityValue)).toBeGreaterThanOrEqual(1)
			const dialingMode = await this.getTabelCellValue("Dialing mode",index, this.commonTable,this.commonTableHeader)
			const status = await this.getTabelCellValue("Status",index, this.commonTable,this.commonTableHeader)
			let dialingMode_re = new RegExp('Preview|Predictive')
			let status_re = new RegExp('Ready|Paused|Running|Incomplete')
			expect(dialingMode).toMatch(dialingMode_re)
			expect(status).toMatch(status_re)
			// console.log("priorityValue=="+priorityValue.toString())
			// console.log("dialingMode=="+dialingMode)
			// console.log("status=="+status)
		}

	}



	// 3) check search campaigns method
	async searchCampaigns(seachKeyword,searchMode="Fuzzy"){
		let realKeyword = seachKeyword
		if(searchMode === "Fuzzy"){
			realKeyword = seachKeyword.substring(0,1+(seachKeyword.length)/2)
		}
		let re = new RegExp('https:(.*?)\/campaigns\\?');
		const [response] = await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200,{timeout:30*1000}),
			await this.inputSearchCampaignsName(realKeyword)
		])
		const resp_json = await response.json()
		if(resp_json.total>0){
			await this.wait_for_locator(this.commonTable)
			await this.wait_for_animated(this.commonTable)
		}
		await this.wait_for_timeout(2)
		return realKeyword
	}

	async inputSearchCampaignsName(searchName){
		await this.topSearchInput.fill(searchName)
	}

	async checkSearchResultByName(keywords){
		const searchResultName = await this.getTabelCellValue("Name","1",this.commonTable,this.commonTableHeader)
		expect(searchResultName).toContain(keywords)
	}

	async checkNoResultFound(){
		await expect(this.noResultFoundComments).toBeVisible()
	}

	async checkLessThanThreeError(){
		await expect(this.lessThanThreeComments).toBeVisible()
	}

	async checkNoCampaignsYet(){
		const myroute = /\/campaigns\?/
		let campaignsArray = []
		await this.page.route(myroute, (route => {
				route.fulfill({
					status: 200,
					contentType: 'application/json',
					body: JSON.stringify(
					{"_links":{"self":""},
							"count":10,
							"per_page":10,
							"page":1,
							"total":0,
							"total_pages": 1,
							"_embedded":{"campaigns":campaignsArray}
					})
				});
			}))
		await Promise.all([
			this.refreshBtn.click(),
			expect(this.topSearchInput).not.toBeEnabled(),
			expect(this.noCampaignsYetComments).toBeVisible()
		])
		await this.page.unroute(myroute)

	}



	// 4) duplicate campaign method
	async clickDupCampaignButton(campaignName){
		const campaignNum = await this.getListSize(this.commonTable)
		for(let num=1;num<=campaignNum;num++){
			const camName = await this.commonTable.locator("//tbody/tr["+num+"]/td/a/span").textContent()
			if(camName===campaignName){
				await this.commonTable.locator("//tbody/tr["+num+"]").locator("button[data-pendo-campaignlist-duplicatebutton]").click()
				await this.createCampaignDialog
				// await this.campaignNameInput.fill("Dup_"+campaignName)
				// await this.confirmBtn.click()
				break
			}
		}
	}

	// 5) start campaign method
	async startCampaignFromList(campaign_name){
		await this.clickStartCampaignButton(campaign_name)
		await expect(this.startCampainDialog).toBeVisible()
		await expect(await this.startCampainDialogTitle.textContent()).toContain("Start campaign")

		let re = new RegExp('https:(.*?)\/campaigns\\?')
		await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			await this.waitAndClearToast(),
			await this.startCampainDialogConfirmStartButton.click()
		])
		// await this.wait_for_toast()
		await this.check_toast_title("Campaign started running")
	}

	async clickStartCampaignButton(campaignName){
		const campaignNum = await this.getListSize(this.commonTable)
		for(let num=1;num<=campaignNum;num++){
			const camName = await this.commonTable.locator("//tbody/tr["+num+"]/td/a/span").textContent()
			if(camName===campaignName){
				await this.commonTable.locator("//tbody/tr["+num+"]").locator("button[data-pendo-campaignlist-startbutton]").click()
				await this.createCampaignDialog

				break
			}
		}

	}

	// 6) pause campaign method
	async pauseCampaignFromList(campaign_name){
		await this.closeAllToast()
		await this.clickPauseCampaignButton(campaign_name)
		// await this.wait_for_toast()
		await this.wait_for_timeout(1)
		// await this.check_toast_title("Campaign paused")
	}

	async clickPauseCampaignButton(campaignName){
		const campaignNum = await this.getListSize(this.commonTable)
		for(let num=1;num<=campaignNum;num++){
			const camName = await this.commonTable.locator("//tbody/tr["+num+"]/td/a/span").textContent()
			if(camName===campaignName){
				await this.commonTable.locator("//tbody/tr["+num+"]").locator("button[data-pendo-campaignlist-pausebutton]").click()
				await this.createCampaignDialog
				break
			}
		}
	}

	// 7) resume campaign method
	async resumeCampaignFromList(campaign_name){
		await this.clickStartCampaignButton(campaign_name)
		await expect(this.startCampainDialog).toBeVisible()
		await expect(await this.startCampainDialogTitle.textContent()).toContain("Start campaign")

		let re = new RegExp('https:(.*?)\/campaigns\\?')
		await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			await this.waitAndClearToast(),
			await this.startCampainDialogConfirmStartButton.click()
		])
		// await this.wait_for_toast()
		await this.check_toast_title("Campaign started running")
	}

	// 8) delete campaign method
	async deleteCampaignFromList(campaign_name){
		await this.clickDeleteCampaignButton(campaign_name)
		await expect(this.deleteCampainDialog).toBeVisible()
		await expect(await this.deleteCampainDialogTitle.textContent()).toContain("Delete campaign")

		let re = new RegExp('https:(.*?)\/campaigns\\?')
		await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			await this.waitAndClearToast(),
			await this.deleteCampainDialogConfirmDeleteButton.click()
		])
		// await this.wait_for_toast()
		await this.check_toast_title("Campaign deleted")
	}

	async clickDeleteCampaignButton(campaignName){
		const campaignNum = await this.getListSize(this.commonTable)
		for(let num=1;num<=campaignNum;num++){
			const camName = await this.commonTable.locator("//tbody/tr["+num+"]/td/a/span").textContent()
			if(camName===campaignName){
				await this.commonTable.locator("//tbody/tr["+num+"]").locator("button[aria-label=\"delete_outline\"]").click()
				await this.deleteCampainDialog
				break
			}
		}
	}
	
	async cancelDeleteCampaignDialog(){
		await this.deleteCampaignDialogCloseCancelButton.click()
	}

	async checkDeleteButtonStatus(campaignName,epStatus){
		console.log(`campaign name is ${campaignName}, epStatus is ${epStatus}`)
		const campaignNum = await this.getListSize(this.commonTable)
		for(let num=1;num<=campaignNum;num++){
			const camName = await this.commonTable.locator("//tbody/tr["+num+"]/td/a/span").textContent()
			if(camName===campaignName){
				if(epStatus==="enable"){
					await expect(this.commonTable.locator("//tbody/tr["+num+"]").locator("button[aria-label=\"delete_outline\"]")).toBeEnabled()
				
				}else if(epStatus==="disable"){
					await expect(this.commonTable.locator("//tbody/tr["+num+"]").locator("button[aria-label=\"delete_outline\"]")).toBeDisabled()
				
				}else{
					throw new Error(`epStatus: ${epStatus} is not correct, please input enable or disable status!`)
				}
				// await this.deleteCampainDialog
				break
			}
		}
		// throw new Error("Not found the campaign name in the campaign list at current page!")
	}



	/*
	Create Campaign Method
	0) general
	1）create campaign dialog
	2) dialing mode settings
	3) dailing strategy settings
	4) agents settings
	5) RL & DNC
	*/

	// 0) general
	async getSettingTitle(){
		return await this.campaigns_setting_title.textContent()
	}

	async navToAgent(){
		const [response]= await Promise.all([
			this.page.waitForResponse(response => response.url().includes("graph/users") && response.status() === 200),
			await this.AgentNavTab.click()
		])
		const resp_json = await response.json()
		const total = resp_json.total
		if(total){
			await this.wait_for_locator(this.commonTable)
            await this.wait_for_animated(this.commonTable)
		}
		return total
	}

	async navToLists(){
		await Promise.all([
			this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
			await this.ListsNavTab.click()
		])
	}

	async getFeedback(feedback_locator){
		return await feedback_locator.textContent()
	}

	async getListSize(table_locator){
		return await table_locator.locator("tbody>tr").count()
	}

	async clickDiscardCreateCampaign(){
		await this.discardCreateCampaignDiscardBtn.click()
	}

	async saveAndNext(){
		await this.nextBtn.click()
	}

	async saveAndSubmit(){
		await this.createBtn.click()
	}


	// 1）create campaign dialog
	async createCampaign(campaign_name=""){
		await this.clickCreateCampaign()
		await Promise.all([
			this.page.waitForResponse(response => response.url().includes(campaign_name) && response.status()===404 && response.request().method()==="HEAD" ),
			await this.inputCampaignName(campaign_name)
		])
		await this.confirmCreateCampaign()
	}

	async clickCreateCampaign(){
		await this.createCampaignBtn.click()
	}

	async inputCampaignName(camName){
		await this.campaignNameInput.fill(camName)
	}

	async confirmCreateCampaign(){
		await this.confirmBtn.click()
	}

	async closeCreateCampaignDialog(){
		await this.closeModalDialog()
	}

	async cancelCreateCampaignDialog(){
		await this.cancelBtn.click()
	}

	async checkDuplicateNameError(){
		const errMessage = await this.campaignNameInput_feedback.textContent()
		expect(errMessage).toContain("This campaign name is already taken")
	}

	async checkCampaignTeamOtionPermission(permission){
		await this.createCampaignBtn.click()
		if(permission==="enable"){
			await expect(this.campaignTeamLabel).toBeVisible()
			await expect(this.campaignTeamDropdown).toBeVisible()
		}else{
			await expect(this.campaignTeamLabel).toBeHidden()
			await expect(this.campaignTeamDropdown).toBeHidden()
		}
		await this.cancelCreateCampaignDialog()
	}


	// 2) dialing mode settings
	async checkCampaignDiscard(campaign_name){
		let re = new RegExp('https:(.*?)\/campaigns\\?');
		await Promise.all([
			this.page.waitForResponse(response => re.test(response.url()) && response.request().method() === "GET" && response.status()===200),
			await this.inputSearchCampaignsName(campaign_name)
		])
		// await this.searchCampaigns(campaign_name,"Exact")
		await this.checkNoResultFound()
		// clear the search result
		await this.clearSearchkeywordButton.click()
	}

	async setCampaignPriority(level="8"){
		const level_str = (parseInt(level)-1).toString()
		await this.dialerFrame.locator("label[for=\"priority-id-"+level_str+"\"]").click()
	}

	async checkPriorityValue(level="8"){
		// const level_str = eval(level-1)
		for(let index=0;index<parseInt(level)-1;index+=1){
			let priorityItem = await this.dialerFrame.locator("label[for=\"priority-id-"+index.toString()+"\"]")
			if(index<parseInt(level)){
				expect(await priorityItem.getAttribute("class")).toContain("co--bg-green-500")
			}
			else{
				expect(await priorityItem.getAttribute("class")).not.toContain("co--bg-green-500")
			}


		}

	}

	async setDialingMode(mode="Predictive dialing"){
		await this.dialingModeOption.click()
		await this.dialingModeList.locator("span.co-list__item-content:not(.co-list__item-content--minimal):text(\""+mode+"\")").click()
	}

	async setDialingRadio(call_number="2"){
		await this.dialingRatioInput.fill(call_number)
		await this.wait_for_timeout(1)
	}

	async setAbandonmentRate(rate_number="10"){
		await this.abandonmentRateInput.fill(rate_number)
		await this.wait_for_timeout(1)
	}

	async setAbandonmentTimeout(timeout="30"){
		await this.abandonmentTimeoutInput.fill(timeout)
		await this.wait_for_timeout(1)
	}

	async setMaxRingtime(ringtime="60"){
		await this.maxRingtimeInput.fill(ringtime)
		await this.wait_for_timeout(1)
	}

	async enableMachineDetection(){
		await this.answerMachineDetectEnableInput.click()
	}

	async disableMachineDetection(){
		await this.answerMachineDetectDisableInput.click()
	}

	async setStrategyType(type="Automatic"){
		await this.strategyTypeOption.click()
		await this.strategyTypeList.locator("span.co-list__item-content:not(.co-list__item-content--minimal):text(\""+type+"\")").click()
	}

	async setConnectionTimeout(connectionTimeout="30"){
		await this.connectionTimeoutInput.fill(connectionTimeout)
		await this.wait_for_timeout(1)
	}

	async setMaxSkipTime(skipTime="10"){
		await this.maxSkipsPerRecordInput.fill(skipTime)
		await this.wait_for_timeout(1)
	}


	//3) dailing strategy settings
	async selectCallIDs(index="1"){
		const [response] = await Promise.all([
			this.page.waitForResponse(response => response.url().includes("phone-details/numbers") && response.status() === 200 && response.request().method() === "GET"),
			await this.clickCallIDsLink()
		])
		const resp_json = await response.json()
		const total = resp_json.total
		if(total>0){
			await this.wait_for_locator(this.callerIDsTable)
            await this.wait_for_animated(this.callerIDsTable,"2")
		}else{
			console.log("there is no call ids！")
			return false;
		}
		// await this.clickCallIDsLink()
		await this.selectCallIDsByIndex(index)
		await this.waitAndClearToast()
		await this.clickConfirmSelectButton()
	}

	async clickCallIDsLink(){
		await this.callIDslink.click()
	}

	async selectCallIDsByIndex(index){
		const table_locator = this.callerIDsTable
		const table_header_locator = table_locator.locator("thead")
		 await this.wait_for_timeout(3)
		await this.clickTabelCellByIndex(1,index,table_locator,table_header_locator,"div>input")
	}

	async clickConfirmSelectButton(){
		await this.callerIDsSelectButton.click()
		// await this.callerIDsSelectButton.click({position:{x:35,y:25}})
	}

	async setCallingDays(dayList){
		for(let day in dayList){
			await this.dialerFrame.locator(":text-is('"+dayList[day]+"')").click()
		}
	}

	async blur(){
		await this.callHoursLable.click()
	}

	async setCallingFromHour(){
		await this.callFromHoursInput.click()
	}

	async setFromHour(hour="07"){
		await this.FromHours_hour.fill(hour)
	}

	async setFromMinute(minute="30"){
		await this.FromHours_minute.fill(minute)
	}

	async setToHour(hour="11"){
		await this.ToHours_hour.fill(hour)
	}

	async setToMinute(minute="00"){
		await this.ToHours_minute.fill(minute)
	}

	async setCallingToHour(){
		await this.callToHoursInput.click()
	}

	async setFromMeridian(meridian){
		const frmoMeridianText = await this.callFrom_meridian.textContent()
		if(frmoMeridianText===meridian){
			console.log("no need to change from meridian setting")
		}else{
			await this.callFrom_meridian.click()
		}
	}

	async setToMeridian(meridian){
		const toMeridianText = await this.callTo_meridian.textContent()
		if(toMeridianText===meridian){
			console.log("no need to change from meridian setting")
		}else{
			await this.callTo_meridian.click()
		}
	}

	async setGlobalMaxAttempts(attempt="3"){
		await this.globalMaxAttemptsInput.fill(attempt)
		await this.wait_for_timeout(1)
	}

	async checkGlobalMaxAttemptsError(){
		const globalMaxAttempts_comments = await this.getFeedback(this.globalMaxAttemptsInput_feedback)
		if(this.test_ff.PO_NEW_RETRIES_UI_ENABLED){
			console.log("1_current PO_NEW_RETRIES_UI_ENABLED is true")
			await expect(globalMaxAttempts_comments).toContain("Enter a number between 1 and 100")
		}
		else{
			console.log(`2_current PO_NEW_RETRIES_UI_ENABLED is ${this.test_ff.PO_NEW_RETRIES_UI_ENABLED}`)
			await expect(globalMaxAttempts_comments).toContain("Please insert a value between 1 and 100, without decimal places")
			await expect(this.globalMaxAttemptsSection).toHaveClass(/co--error/)
		}

		// const globalMaxAttempts_comments = await dialerPage.getFeedback(dialerPage.globalMaxAttemptsInput_feedback)
		// await expect(globalMaxAttempts_comments).toContain("Please insert a value between 1 and 100, without decimal places")
	}

	async setRetryPeriod(value="2",type="minutes"){
		await this.retryPeriodTypeOption.click()
		if(type==="minutes"){
			await this.retryPeriodTypeList.locator("li *:text(\"minute(s)\")").click()
		}
		else if(type==="seconds"){
			await this.retryPeriodTypeList.locator("li *:text(\"second(s)\")").click()
		}
		else if(type==="hours"){
			await this.retryPeriodTypeList.locator("li *:text(\"hour(s)\")").click()
		}
		else if(type==="days"){
			await this.retryPeriodTypeList.locator("li *:text(\"day(s)\")").click()
		}
		await this.retryPeriodInput.fill(value)
		await this.wait_for_timeout(1)
	}

	async checkRetryPeriodError(type="minutes"){
		let retryPeriod_comments = await this.getFeedback(this.retryPeriodInput_feedback)
		if(this.test_ff.PO_NEW_RETRIES_UI_ENABLED){
			if(type==="days"){
				await expect(retryPeriod_comments).toContain("Entering a number between 1 and 30, without decimal places")
			}else if(type==="hours"){
				await expect(retryPeriod_comments).toContain("Entering a number between 1 and 720, without decimal places")
			}else if(type==="minutes"){
				await expect(retryPeriod_comments).toContain("Entering a number between 1 and 43200, without decimal places")
			}else if(type==="seconds"){
				await expect(retryPeriod_comments).toContain("Entering a number between 1 and 2592000, without decimal places")
			}
		}else{
			if(type==="days"){
				await expect(retryPeriod_comments).toContain("Please insert a value between 1 and 30, without decimal places")
			}else if(type==="hours"){
				await expect(retryPeriod_comments).toContain("Please insert a value between 1 and 720, without decimal places")
			}else if(type==="minutes"){
				await expect(retryPeriod_comments).toContain("Please insert a value between 1 and 43200, without decimal places")
			}else if(type==="seconds"){
				await expect(retryPeriod_comments).toContain("Please insert a value between 1 and 2592000, without decimal places")
			}
			// await expect(retryPeriod_comments).toContain("Please insert a value between 1 and 2592000, without decimal places")
			await expect( await this.retryPeriodSection).toHaveClass(/co--error/)
		}

	}

	async enableRecordChainingStrategy(){
		if (!await this.recordChainingStrategyCheck.isChecked()){
			await this.recordChainingStrategyCheck.click()
		}
		await expect(this.recordChainingStrategyCheck).toBeChecked()
	}

	async disableRecordChainingStrategy(){
		if (await this.recordChainingStrategyCheck.isChecked()){
			await this.recordChainingStrategyCheck.click()
		}
		await expect(this.recordChainingStrategyCheck).not.toBeChecked()
	}

	async setSystemDispositions(type="Busy",recordState="Retry"){
		let selectedDisposition;
		if(type==="Busy"){
			await this.systemDispositionsType_Busy.click()
			selectedDisposition = this.systemDispositionsType_Busy
		}
		else if(type==="NoAnswer"){
			await this.systemDispositionsType_Busy.click()
			selectedDisposition = this.systemDispositionsType_Busy
		}
		else if(type==="Abandoned"){
			await this.systemDispositionsType_Abandoned.click()
			selectedDisposition = this.systemDispositionsType_Abandoned
		}
		else if(type==="HangBeforeConnection"){
			await this.systemDispositionsType_HangupBeforeConnection.click()
			selectedDisposition = this.systemDispositionsType_HangupBeforeConnection
		}
		else if(type==="AnswerMachineDetection"){
			await this.systemDispositionsType_AnswerMachineDetection.click()
			selectedDisposition = this.systemDispositionsType_AnswerMachineDetection
		}
		else if(type==="InvalidNumber"){
			await this.systemDispositionsType_InvalidNumber.click()
			selectedDisposition = this.systemDispositionsType_InvalidNumber
		}

		if(recordState==="Retry"){
			await this.radioRetryButton.click()
		}
		else{
			await this.radioFinalButton.click()
		}
		await this.systemDispositionsApplyButton.click()
		const busyDisposition = await selectedDisposition.locator("p.co-list__item-description").textContent()
		expect(busyDisposition).toEqual(recordState)
	}

	async transiteToAgentStep(){
		await Promise.all([
			this.page.waitForResponse(response => response.url().includes("graph/users") && response.status() === 200),
			await this.saveAndNext()
		])
	}


	// 4) agents settings
	async setTableOptions(item_value=""){
		await this.tableOptionBtn.click()
		let re = new RegExp('https:(.*?)\/graph/users')
		const [request] = await Promise.all([
					this.page.waitForRequest(request => re.test(request.url())),
					await this.dialerFrame.locator("[data-co-name=\"Popup\"]").locator("//span[text()=\""+item_value+"\"]").click()
				])
		let api_tablePages;
		if(request.method()==="GET"){
			api_tablePages = request.url().split("?")[1].split("&")[1].split("=")[1]
		}else{
			api_tablePages = request.postDataJSON().per_page
		}
		// const api_tablePages = request.postDataJSON().per_page
		// const api_tablePages = request.url().split("?")[1].split("&")[1].split("=")[1]
		expect(api_tablePages.toString()).toEqual(item_value)
		await this.tableOptionBtn.click()
	}

	async selectRingGroups(ringGroups=[""],type=""){
		await Promise.all([
			this.page.waitForResponse(response => response.url().includes("ring-groups") && response.status() === 200),
			await this.filterBtn.click()
		])
		await this.wait_for_timeout(2)
		await this.ringGroupbox.click()
		if(type==="all"){
			await this.ringGroupSelection.locator("//*[text()=\"Select All\"]").click()
			await this.panelApplyBtn.click()
		}
		else{
			let re = new RegExp('https:(.*?)\/graph/users')
			for(let rg in ringGroups) {
				const [response] = await Promise.all([
					this.page.waitForResponse(response => response.url().includes("name="+ringGroups[rg]) && response.status()===200),
					await this.ringGroupSearchInput.fill(ringGroups[rg])
				])
				const resp_json = response.json()
				const total = resp_json.total
				if(total===0){
					throw new Error(`there is no " ${ringGroups[rg]} " ring-group in this ternant`)
				}
				await this.ringGroupSelection.locator("//*[text()=\"" + ringGroups[rg] + "\"]").click()
			}
			const [request] = await Promise.all([
					this.page.waitForRequest(request => re.test(request.url())),
					await this.panelApplyBtn.click()
				])

			const api_filterSetting = request.method()==="GET"?request.url().split("?")[1].split("&")[2].split("=")[1]:request.postDataJSON().filter
			// const api_filterSetting = request.postDataJSON().filter
			for(let rg in ringGroups){
				expect(api_filterSetting).toContain(ringGroups[rg])
			}
		}



	}

	async clickClearFilterSettings(){
		await this.clearFilterSettingsButton.click()
	}

	async clickFilterButton(){
		await this.filterBtn.click()
	}

	async cancelFilterPanel(){
		await this.panelCancelBtn.click()
	}

	async clickAssignmentDetails(tableItem){
		await tableItem.locator("button").click()
	}

	async close_assignmentDetailPanel(){
		await this.panelCloseBtn.click()
	}

	async selectAgents(agents_list){
		const agentNumber = agents_list.length
		for(let num=0;num<agentNumber;num++){
			const [response] = await Promise.all([
				this.page.waitForResponse(response => response.url().includes("graph/users") && response.status() === 200),
				await this.agentSearchInput.fill(agents_list[num])
			])
			// await this.agentSearchInput.fill(agents_list[num])
			// const resp = await this.waitForAgentList()
			const resp_json = await response.json()
			const agentTotal = resp_json._total
			if (agentTotal===0) {
				console.error("The agent can not be found in agent list")
				continue;
				}
			const agent_table = this.commonTable
			const agent_table_header = this.commonTable.locator("thead")
			await this.wait_for_timeout(2)
			await this.clickTabelCellByIndex(1, "1", agent_table, agent_table_header)
			await this.wait_for_timeout(1)
			}

	}

	async selectAgentsByRows(rows=["1"]){
		let selectedAgentlist = []
		const agent_table = this.commonTable
		const agent_table_header =this.commonTable.locator("thead")
		for(let row in rows){
			// await this.wait_for_timeout(2)
			const tableItem = await this.getTableItem(agent_table, rows[row])
			const agentName = await this.getTabelCellByIndex(agent_table_header, tableItem, 2)
			const selectedAgentName = await agentName.textContent()
			selectedAgentlist.push(selectedAgentName)
			const tableCell = await this.getTabelCellByIndex(agent_table_header, tableItem, 1)
			const tableCell_checkbox = tableCell.locator("input")
			if(await tableCell_checkbox.isChecked()){
				continue
			}
			else{
				await tableCell.click()
			}
			// await this.clickTabelCellByIndex(1, rows[row], agent_table, agent_table_header)
			await this.wait_for_timeout(1)
		}
		return selectedAgentlist

	}

	async checkSelectedAgentCount(selectedAgentNum){
		const num_str = await this.selectedAgentCount.textContent()
		expect(parseInt(num_str.split(" selected")[0])).toBeGreaterThanOrEqual(selectedAgentNum)
		// expect(num_str.split(" selected")[0]).toBeGreaterThanOrEqual(selectedAgentNum.toString())
	}

	async getAgentName(tableItem){
		return await tableItem.locator("td>span[data-testid=\"text-wrapper\"]").textContent()
	}

	async clickAgentNameColumn(){
		const index = await this.getTableColumnIndex(this.commonTableHeader,"Name")
		const nameHeader = this.commonTableHeader.locator("tr>th:nth-child("+index.toString()+")")
		await nameHeader.click()
	}

	async searchAgent(agentName){
		const [response] = await Promise.all([
			this.page.waitForResponse(response => response.url().includes("graph/users") && response.status() === 200),
			await this.agentSearchInput.fill(agentName)
		])
		const resp_json = await response.json()
        const total = resp_json.total
        if(total>0){
            await this.wait_for_locator(this.commonTable)
            await this.wait_for_animated(this.commonTable)
        }
		return total
	}

	async clearAgentSearch(){
		await this.agentSearchInputClearButton.click()
	}


	// 5) RL & DNC
	async addRecordLists(rows,recordsListName=""){
		const[response]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("record-lists?status") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddRecordsListButton()
		])
		let resp_json=await response.json()
		let total = resp_json.total
		if(total>0){
			await this.wait_for_locator(this.recordsListTable)
			await this.wait_for_animated(this.recordsListTable)
			let selectRecordList=[];
			await expect(this.commonPanel).toBeVisible()
			for(let row in rows){
				let selectRecordName = await this.clickRecordsListByIndex(rows[row],recordsListName,"input")
				selectRecordList.push(selectRecordName)
			}
			await this.waitAndClearToast()
			await this.clickRecordsListApplyButton()
			return selectRecordList
		}else{
			console.error("There is no records-list available to bind with campaign.")
			throw new Error("There is no records-list available to bind with campaign.")
		}

		// await this.page.waitForResponse(response => response.url().includes("record-lists") && response.status() === 200 && response.request().method()==="GET")
		// await this.wait_for_animated(this.recordsListTable)
		// let selectRecordList=[];
		// expect(this.commonPanel).toBeVisible()
		// for(let row in rows){
		// 	let selectRecordName = await this.clickRecordsListByIndex(rows[row],recordsListName)
		// 	selectRecordList.push(selectRecordName)
		// }
        // await this.clickRecordsListApplyButton()
		// return selectRecordList
	}

	async getSelectedRecordList(){
		const selectedRL = this.selectedRecordlistTable.locator("tbody>tr")
		const selectedCount = selectedRL.count()
		let selectedRecordList=[]
		for(let row=1;row<=selectedCount;row+=1){
			const rowItem = await this.getTableItem(this.selectedRecordlistTable, row)
			const name = await this.getTabelCell(this.selectedRecordlistTableHeader, rowItem, "Name")
			selectedRecordList.push(name)
		}
		return selectedRecordList
	}

	async getSelectedDNCList(){
		const selectedDNCL = this.selectedDNClistTable.locator("tbody>tr")
		const selectedCount = selectedDNCL.count()
		let selectedDNCList=[]
		for(let row=1;row<=selectedCount;row+=1){
			const rowItem = await this.getTableItem(this.selectedDNClistTable, row)
			const name = await this.getTabelCell(this.selectedDNClistTableHeader, rowItem, "Name")
			selectedDNCList.push(name)
		}
		return selectedDNCList
	}

	async checkCampaignRecordlists(includeRL=[], notIncludeRL=[]){
		/*
		1) check the current record lists num from recordlists panel toolbar is correct
		2) check the current record list include the rl with titles in includeRL set
		3) check the current record list NOT include the rl with titles in notIncludeRL set
		*/
		const[response_origin]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("record-lists?status") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddRecordsListButton()
		])
		let resp_json=await response_origin.json()
		let api_total = resp_json.total

		//wait for record list load completed
		await this.wait_for_animated(this.commonTable,2)

		const showTotal = await this.recordsListTotalNum.textContent()
		let showNum = parseInt(showTotal.split(" list")[0])
		console.log(`showNum is ${showNum}, api_total is ${api_total}`)
		if(showNum!==api_total){
			console.error("the records-list total show incorrectly.")
			throw new Error("the records-list total show incorrectly.")
		}
		if(includeRL.length>0){
			for(let i=0;i<includeRL.length;i++){
				const [response_search_include] = await Promise.all([
					this.page.waitForResponse(response => response.url().includes("record-lists?status") && response.status() === 200 && response.request().method()==="GET"),
					await this.recordsListSearchInput.fill(includeRL[i])
				])
				let resp_json=await response_search_include.json()
				let total = resp_json.total
				await expect(total).toBeGreaterThanOrEqual(1)
			}
		}
		if(notIncludeRL.length>0){
			for(let i=0;i<notIncludeRL.length;i++){
				const [response_search_notInclude] = await Promise.all([
					this.page.waitForResponse(response => response.url().includes("record-lists?status") && response.status() === 200 && response.request().method()==="GET"),
					await this.recordsListSearchInput.fill(notIncludeRL[i])
				])
				let resp_json=await response_search_notInclude.json()
				let total = resp_json.total
				await expect(total).toEqual(0)
			}
		}

		await this.recordsListCloseButton.click()
	}

	async checkCampaignDNClists(includeDNCL=[], notIncludeDNCL=[]){
		/*
		1) check the current record lists num from recordlists panel toolbar is correct
		2) check the current record list include the rl with titles in includeRL set
		3) check the current record list NOT include the rl with titles in notIncludeRL set
		*/
		const[response_origin]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddDNCListButton()
		])
		let resp_json=await response_origin.json()
		let api_total = resp_json.total

		//wait for do-not-call list load completed
		await this.wait_for_animated(this.commonTable,2)

		const showTotal = await this.recordsListTotalNum.textContent()
		let showNum = parseInt(showTotal.split(" list")[0])
		console.log(`showNum is ${showNum}, api_total is ${api_total}`)
		if(showNum!==api_total){
			console.error("the do-not-call-list total show incorrectly.")
			throw new Error("the do-not-call-list total show incorrectly.")
		}
		if(includeDNCL.length>0){
			for(let i=0;i<includeDNCL.length;i++){
				const [response_search_include] = await Promise.all([
					this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
					await this.recordsListSearchInput.fill(includeDNCL[i])
				])
				let resp_json=await response_search_include.json()
				let total = resp_json.total
				await expect(total).toBeGreaterThanOrEqual(1)
			}
		}
		if(notIncludeDNCL.length>0){
			for(let i=0;i<notIncludeDNCL.length;i++){
				const [response_search_notInclude] = await Promise.all([
					this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
					await this.recordsListSearchInput.fill(notIncludeDNCL[i])
				])
				let resp_json=await response_search_notInclude.json()
				let total = resp_json.total
				await expect(total).toEqual(0)
			}
		}

		await this.recordsListCloseButton.click()
	}

	async getFirstRowItem(columnName){
        const firstRowItem = await this.getTableItem(this.commonTable,"1")
        const nameColumnIndex = await this.getTableColumnIndex(this.commonTableHeader,columnName)
        // console.log("nameColumnIndex is "+nameColumnIndex)
        return await this.getTabelCellByIndex(this.commonTableHeader, firstRowItem, nameColumnIndex)
    }

	async checkShowRecordList(num=0,includeRL=[]){
		const[response]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("record-lists?status") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddRecordsListButton()
		])
		let resp_json=await response.json()
		let total = resp_json.total
		console.log(total)
		console.log(num)
		if(num===-1) {
			//check all the includeRL is include in the list
			await expect(total).toBeGreaterThan(resp_json._embedded.record_lists.length)
			for(let i=0;i<includeRL.length;i++){
				let listTitle = resp_json._embedded.record_lists[i].name
				await expect(includeRL).toContain(listTitle)
			}
		}
		else if(total!==num){
			console.error("the records-list total show incorrectly.")
			throw new Error("the records-list total show incorrectly.")
		}
		else if(total>0){
			for(let i=0;i<total;i++){
				let listTitle = resp_json._embedded.record_lists[i].name
				await expect(includeRL).toContain(listTitle)
			}
		}
		else{
			console.error("There is no records-list available to bind with campaign.")
			throw new Error("There is no records-list available to bind with campaign.")
		}
	}

	async checkShowDNCList(num=0,includeRL=[]){
		const[response]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddDNCListButton()
		])
		let resp_json=await response.json()
		let total = resp_json.total
		console.log(total)
		console.log(num)
		if(num===-1){
			await expect(total).toBeGreaterThan(resp_json._embedded.do_not_call_lists.length)
			for(let i=0;i<num;i++){
				let listTitle = resp_json._embedded.do_not_call_lists[i].name
				await expect(includeRL).toContain(listTitle)
			}
		}
		else if(total!==num){
			console.error("the do-not-call-list total show incorrectly.")
			throw new Error("the do-not-call-list total show incorrectly.")
		}
		else if(total>0){
			for(let i=0;i<total;i++){
				let listTitle = resp_json._embedded.do_not_call_lists[i].name
				await expect(includeRL).toContain(listTitle)
			}
		}else{
			console.error("There is no do-not-call-list available to bind with campaign.")
			throw new Error("There is no do-not-call-list available to bind with campaign.")
		}
	}

	async checkSelectedRecordList(selectRecordList){
		const recordListSize = await this.getListSize(this.selectedRecordlistTable)
		for(let index=1;index<=recordListSize;index+=1){
			const tableItem = await this.getTableItem(this.selectedRecordlistTable,index)
			const selectAgent = await this.getTabelCellByIndex(this.selectedRecordlistTableHeader,tableItem,1)
			const selectName = 	await selectAgent.textContent()
			await expect(selectRecordList).toContain(selectName)
			// expect(await this.getTabelCellByIndex(this.selectedRecordlistTableHeader,tableItem,1).textContent()).toContain(recordListSize[index])
		}
	}

	async removeSelectedRecordListByIndex(removeIndexs=[1],all=""){

		let removeRecordsList = []
		removeIndexs.sort((a,b)=>b-a);
		for(let index of removeIndexs){
			const tableItem = await this.getTableItem(this.selectedRecordlistTable,index.toString())
			const removeItem = await this.getTabelCellByIndex(this.selectedRecordlistTableHeader,tableItem,1)
			const removeItemName = await removeItem.textContent()
			const binButton = tableItem.locator("td>button[aria-label=\"trash\"]")
			await binButton.click()
			await this.confirmRemoveRecordsListButton.click()
			removeRecordsList.push(removeItemName)
		}
		if(all===""){
			const recordListSize_after =await this.getListSize(this.selectedRecordlistTable)
			for(let index_after in recordListSize_after){
				const tableItem = await this.getTableItem(this.selectedRecordlistTable,index_after+1)
				const RecordListName = await this.getTabelCellByIndex(this.selectedRecordlistTableHeader,tableItem,1).textContent()
				expect(removeRecordsList).not.toContain(RecordListName)
			}
		}else{
			await expect(this.selectedRecordlistTable).toBeHidden()
		}



	}

	async clickAddRecordsListButton(){
		await this.recordsListAddButton.click()
	}

	async closeRLPanel(){
		await this.recordsListCloseButton.click()
	}

	async closeDNCLPanel(){
		await this.recordsListCloseButton.click()
	}

	async cancelRLPanel(){
		await this.recordsListCancelButton.click()
	}

	async clickRecordsListByIndex(index,recordsListName="",clickLocator=""){
		if(recordsListName !==""){

			const[response] = await Promise.all([
				this.page.waitForResponse(response => response.url().includes("record-lists") && response.status() === 200 && response.request().method()==="GET"),
				await this.recordsListSearchInput.fill(recordsListName)
			])
			const resp_json = await response.json()
			const total = resp_json.total
			if(total>0){
				await this.wait_for_locator(this.recordsListTable)
				await this.wait_for_animated(this.recordsListTable)
			}
			// await this.recordsListSearchInput.fill(recordsListName)
		}
		const table_locator = this.recordsListTable
		const table_header_locator = table_locator.locator("thead")
		// await this.page.waitForResponse(response => response.url().includes("record-lists") && response.status() === 200 && response.request().method()==="GET")
		await this.clickTabelCellByIndex(1,index,table_locator,table_header_locator,clickLocator)
		const tableItem = await this.getTableItem(table_locator,index)
		const record = await this.getTabelCellByIndex(table_header_locator,tableItem,2)
		return await record.textContent()

	}

	async chooseRecordsListByIndex(indexList=["1"],recordsListName=""){
		const[response]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("record-lists") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddRecordsListButton()
		])
		let resp_json=await response.json()
		let total = resp_json.total
		if(total>0){
			if(recordsListName !==""){
				await this.recordsListSearchInput.fill(recordsListName)
			}
			const table_locator = this.recordsListTable
			const table_header_locator = table_locator.locator("thead")
			let isChange = false
			for(let idx in indexList){
				const rowItem = await this.getTableItem(table_locator, indexList[idx])
				const records_check = await this.getTabelCellByIndex(table_header_locator, rowItem, 1)
				const records_checkInput = records_check.locator("div>input")
				if(!await records_checkInput.isChecked()){
					await records_checkInput.click()
					isChange = true
				}
			}
			if(isChange){
				await this.clickRecordsListApplyButton()
			}else{
				console.log("all RLs you choose have already selected before")
				await this.cancelRLPanel()
			}

		}
	}

	async clickRecordsListApplyButton(){
		await this.waitAndClearToast()
		await this.recordsListApplyButton.click()
	}

	async clickAddDNCListButton(){
		await this.dncListAddButton.click()
	}

	async addDNCLists(row,dncListName){
		const[response]=await Promise.all([
			this.page.waitForResponse(response => response.url().includes("do-not-call-lists") && response.status() === 200 && response.request().method()==="GET"),
			await this.clickAddDNCListButton()
		])
		const resp_json = await response.json()
		const total = resp_json.total
		if(total>0){
			await this.wait_for_locator(this.recordsListTable)
			await this.wait_for_animated(this.recordsListTable)
			await expect(this.commonPanel).toBeVisible()
			const selectDNCName = await this.clickRecordsListByIndex(row,dncListName,"input")
			await this.waitAndClearToast()
			await this.clickRecordsListApplyButton()
			return selectDNCName
		}else{
			console.error("there is no dnc list available to bing the campaign")
			throw new Error("there is no dnc list available to bing the campaign")
		}
	}

	async checkSelectedDNCLists(selectDNCName){

		const tableItem = await this.getTableItem(this.selectedDNClistTable,1)
		const selectedDNCName = await this.getTabelCellByIndex(this.selectedDNClistTableHeader,tableItem,1)
		expect(await selectedDNCName.textContent()).toEqual(selectDNCName)

	}

	async removeSelectedDNClist(){
		const tableItem = await this.getTableItem(this.selectedDNClistTable,1)
		const binButton = tableItem.locator("td>button[aria-label=\"trash\"]")
		await binButton.click()
		await this.confirmRemoveRecordsListButton.click()
		expect(this.selectedDNClistTable).toBeHidden()
	}

}
