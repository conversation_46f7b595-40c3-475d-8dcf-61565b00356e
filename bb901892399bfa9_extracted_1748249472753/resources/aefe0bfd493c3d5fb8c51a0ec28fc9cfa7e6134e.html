<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>Talkdesk</title>
    
    <script type="text/javascript" src="/atlas/ruxitagentjs_ICA7NVfqrux_10313250422105919.js" data-dtconfig="app=ea7c4b59f27d43eb|cuc=m0yti8a3|ssc=1|mel=100000|expw=1|featureHash=ICA7NVfqrux|dpvc=1|lastModification=1747636524915|tp=500,50,0|rdnt=1|uxrgce=1|agentUri=/atlas/ruxitagentjs_ICA7NVfqrux_10313250422105919.js|reportUrl=/atlas/rb_bf56956dwl|rid=RID_-1331467439|rpid=479793954|domain=mytalkdeskca.com"></script><link rel="apple-touch-icon" sizes="57x57" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/favicon-16x16.png">
    <link rel="shortcut icon" type="image/x-icon" href="https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/4.0.0/talkdesk/brand/main_brand/favicon/favicon.ico"/>


    <script type="application/javascript" nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss=">
        const atlasLoadStartTime = performance.now();
    </script>
    
    <script defer src="https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js" type="application/javascript" onerror="window.__INVALID_RUNTIME__ = true" nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss="></script>
    <meta name="_csrf" content="54cc2072-c746-4b69-9739-6b831daab983" />
</head>
<body>


    <style nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss=">
        .workspace-splashscreen-wrapper {
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            position: fixed;
            background-color: #FFF;
            overflow: hidden;
            transition: .5s opacity ease;
            z-index: 2;
        }

        .workspace-splashscreen {
            max-width: 100%;
            max-height: 100%;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .splashscreen-fade-out {
            opacity: 0;
        }

        @media (display-mode: window-controls-overlay) {
            body:has(.workspace-splashscreen-wrapper)::before {
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: env(titlebar-area-height, 40px) !important;
                position: fixed;
                z-index: 10;
                background-color: #28025a;
                transition: .5s opacity ease;
                transition-delay: .5s;
            }

            body:has(.workspace-splashscreen-wrapper.splashscreen-fade-out)::before {
                opacity: 0;
            }
        }

        .no-support-splashscreen .workspace-splashscreen-wrapper{
            background-image: url('https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/brand/main_brand/logo/talkdesk_logo.svg');
            background-repeat: no-repeat;
            background-size: 200px;
            background-position: center;
        }
    </style>

    <div class="workspace-splashscreen-wrapper">
        <canvas class="workspace-splashscreen" id="lottie" height="1080" width="1920"></canvas>
    </div>

    <script type="application/javascript" nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss=">
        window.LOTTIE_WORKER_DIR = "\/atlas\/" + 'js/lottie_worker.min.js'
        window.SUPPORT_SPLASH_SCREEN = document.createElement('canvas').transferControlToOffscreen !== undefined
        window.document.body.classList.add(SUPPORT_SPLASH_SCREEN ? 'support-splashscreen' : 'no-support-splashscreen')
    </script>
    <script src="/atlas/js/animationData.js" type="text/javascript" async nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss="></script>


<div id="atlas-runtime" style="visibility: hidden;"></div>

<script type="application/javascript" nonce="kb+WzQV04X7TCUOti3ztdx1NHIs0bnUow+R3Y0LcnC9kI2XCLpfxsQiUa6MoeyoLX3dbFaEJyCBeXkYBAEFVbk8+A3TTUcMULT8EZ3hoDHY6g5S+Yfo3uPmBggfvtXOChv2yi16rbbtVkiBk1vTWpjkHhc/ZhFy6pp7FzJbeEss=">
    /*<![CDATA[*/
    (function() {
        const NO_IDLE_POLICY_MESSAGE = 'Access to the feature "idle-detection" is disallowed by permissions policy'

        function onLoad (cb) {
            if (document.readyState === 'complete') {
                cb()
            } else {
                window.addEventListener('load', cb)
            }
        }

        async function redirectOnError(error) {
            if (error) {
                reportError(error)
            }

            try {
                await flushMetrics()
            } catch (error) {
                // Just in case it happens to us and we're preserving the logs
                // in the console after the redirect happens
                console.error('There was an issue flushing the metrics', error)
            }

            window.location.href = "\/atlas\/" + 'error'
        }

        function removeSplashScreen() {
            const splashScreenEl = document.querySelector('.workspace-splashscreen-wrapper')

            if (splashScreenEl) {
                splashScreenEl.remove()
            }
        }

        function dispatchEvent(eventName, value, customParams = {}) {
            if (window.ws_monitor !== undefined && window.ws_monitor.dispatch !== undefined) {
                window.ws_monitor.dispatch(eventName, {
                    value,
                    ...customParams,
                    eventOrigin: 'atlas',
                    userId: "637b6d301f05cb5c9508b7dc",
                    tenantId: "po-automationca",
                    engineType: "web",
                    ...(navigator.connection && {
                        connectionType: navigator.connection.effectiveType,
                    }),
                })
            } else {
                console.warn('No monitoring instance configured.')
                console.info(eventName, { value, ...customParams })
            }
        }

        function measureMetric(type, { name, value }, options) {
            if (window.ws_monitor !== undefined && window.ws_monitor.measure !== undefined) {
                window.ws_monitor.measure(type, { name, value }, options)
            } else {
                console.warn('No monitoring instance configured.')
                console.info(type, { name, value }, options)
            }
        }

        function flushMetrics() {
            if (window.ws_monitor !== undefined && window.ws_monitor.flush !== undefined) {
                return window.ws_monitor.flush()
            } else {
                console.warn('No monitoring instance configured.')
                return Promise.resolve()
            }
        }

        function reportError(error, options = {}) {
            if (window.ws_monitor !== undefined && window.ws_monitor.report !== undefined) {
                window.ws_monitor.report(error, {
                    ...options,
                    userId: "637b6d301f05cb5c9508b7dc",
                    tenantId: "po-automationca",
                    engineType: "web"
                })
            } else {
                console.warn('No monitoring instance configured.')
                console.error(error)
            }
        }

        function loadFallback() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script')

                script.src = "https:\/\/prd-cdn-talkdesk.talkdesk.com\/atlas\/latest\/runtime\/main.js"
                script.type = 'application/javascript'
                script.onload = resolve
                script.onerror = reject

                document.body.appendChild(script)
            })
        }

        document.addEventListener('DOMContentLoaded', async function () {
            const root = document.getElementById('atlas-runtime')

            /**
             * We don't have ways to differentiate between WEB and PWA modes
             * through atlas-backend engineType assumption.
             * We assume is a PWA when the runtime is running in 'standalone' or
             * 'window-controls-overlay' display-modes.
             */
            function isRunningInPWA() {
                return (
                    window.matchMedia('(display-mode: standalone)').matches ||
                    window.matchMedia('(display-mode: window-controls-overlay)').matches
                )
            }

            function inIframe() {
                try {
                    return window.self !== window.top
                } catch (e) {
                    return true
                }
            }

            async function hasIdleDetectionPermission() {
              try {
                const controller = new AbortController()
                const idleDetector = new IdleDetector()

                await idleDetector
                  .start({ threshold: 60_000, signal: controller.signal })
                  .then(() => controller.abort())

                return 'granted'
              } catch (error) {
                const bypassRequestPermission = true
                console.log(bypassRequestPermission)

                if(true && inIframe() && error.message.includes(NO_IDLE_POLICY_MESSAGE)) {
                    reportError(new Error('Iframe does not have permission to use idle detection.'))
                    return 'bypass'
                }

                return 'denied'
              }
            }

            function hasIdleDetection() {
                return false && window.IdleDetector
            }

            function start() {
                const runtimeEnv = {"menuUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/atlas-apps-configuration\/latest\/menu.definitions.json","customMenuBaseUrl":"\/custom-menus","contextPath":"\/atlas","environment":"production","talkdeskApiUrl":"https:\/\/api.talkdeskappca.com","talkdeskRegion":"td-ca-1","providerRegion":"ca-central-1","accountAffinity":"ca-central-1","logoutSuccessUrl":"https:\/\/account.talkdeskid.com\/select\/atlas","features":{"ATLAS_polyglot_publish_by_account":false,"ATLAS_restrict_security_policy":true,"ATLAS_enable_rtm_retry_and_notification":true,"ATLAS_desktop_system_info":true,"ATLAS_search_enabled":true,"ATLAS_enable_embedding_settings":true,"ATLAS_secondary_area":true,"ATLAS_workspace_pwa":false,"ATLAS_automated_notifications_show_escalate_section":false,"ATLAS_my_apps":true,"ATLAS_allow_edge_engine":true,"MARKETPLACE_uninstall":false,"ATLAS_theme_version":"2.0.1","ATLAS_change_status_improvement":true,"ATLAS_NO_RETRY_AUTHENTICATION":true,"ATLAS_evaluate_menus":true,"ATLAS_runtime_version":"0.165.1_atlas-runtime","ATLAS_auto_relaunch_apps":true,"ATLAS_enable_oauth2_token_v2_endpoint":false,"MARKETPLACE_hide_explore_button":false,"ATLAS_auto_update_interval":"3600000","ATLAS_polyglot_access_by_account":false,"ATLAS_skip_engine_validation":false,"ATLAS_dock_logo":false,"ATLAS_title_bar_V2":true,"ATLAS_marketplace_ui_version":"0.54.2","ATLAS_write_cassandra_session":true,"ATLAS_parallelize_startup":true,"ATLAS_render_jsx":true,"ATLAS_notifications_use_billing_account":true,"ATLAS_remove_tid_from_query":true,"ATLAS_delay_degraded_message":true,"ATLAS_enable_static_account_info":true,"ATLAS_enable_bring_to_front":true,"ATLAS_desktop_download_version":"2.2.0","MARKETPLACE_hide_tc":false,"ATLAS_title_bar":false,"ATLAS_protocol_launch_fix":true,"ATLAS_splashscreen":true,"ATLAS_classic_admin_access":true,"MARKETPLACE_hide_pricing":false,"ATLAS_use_cassandra_session":true,"ATLAS_runtime_use_desktop_native_controls":false,"ATLAS_bypass_request_permission":true,"ATLAS_frameless":false,"ATLAS_menu_policy":true,"ATLAS_desktop_max_cache_size":false,"ATLAS_runtime_log_level":"Info","ATLAS_desktop_version":"2.2.0","ATLAS_enable_shortcuts":true,"ATLAS_polyglot_publish":false,"ATLAS_marketplace_ui":true,"ATLAS_session_recorder_enable":false,"ATLAS_polyglot_access":false,"ATLAS_RETRY_AUTHENTICATION":true,"ATLAS_notification":true,"ATLAS_save_active_accounts":true,"ATLAS_confirm_navigation_popup":true,"ATLAS_use_account_profile":false},"marketplaceUIBundleUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/marketplace-ui\/v0.54.2\/bundle.js","atlasRuntimeTranslationsUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/i18n\/live\/atlas\/{{lng}}\/latest\/{{ns}}.json","atlasMenuTranslationsUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/i18n\/live\/atlas\/{{lng}}\/latest\/{{ns}}.json","i18nResourcesBaseUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/i18n\/live","heartbeatInterval":600000,"userId":"637b6d301f05cb5c9508b7dc","tenant":"po-automationca","tenantId":"637b683d24e55622d4899257","sid":"2a8fd594edc649b388f34a2001dadffb","platformTid":"48ef8496-04bf-4113-adea-c0e629eab4a4","engineType":"web","engineVersion":"131.0.6778.33","engineExtension":"unknown","logentriesApiKey":"","logentriesRegion":"","featuresPath":"\/features","desktopPerformanceTrackingInterval":120000,"appsApiCacheTTL":*********,"sessionRecorderKey":"ud6cop\/workspace-cevfn","pendoKey":"f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","myAppsPath":"\/my-apps","appsMenuPath":"\/apps-menu","evaluateMenusPath":"\/workspace\/menus\/evaluate","newRelicAccountId":"1596347","newRelicBeacon":"bam.nr-data.net","newRelicErrorBeacon":"bam.nr-data.net","newRelicLicenseKey":"79c239e9b3cb499a6e7155583776383fFFFFNRAL","newRelicBrowserLicenseKey":"NRBR-4d4fe69bacc81363ba8","newRelicTrustKey":"3391111","newRelicUrl":"https:\/\/otlp.nr-data.net:4317","originTrialToken":"A4bbGtUEDepBlLv\/j4DbKJCXLKRVsiG6vtUfJMBXUxKwgNSf9XhkgZsIhB51vVC2\/GcLqzUVtITyfDjOmGMdEgUAAAB1eyJvcmlnaW4iOiJodHRwczovL3RhbGtkZXNraWRjYS5jb206NDQzIiwiZmVhdHVyZSI6IlRwY2QiLCJleHBpcnkiOjE3MzUzNDM5OTksImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","loggerCollectorUrl":"https:\/\/api.talkdeskappca.com\/logger\/v1\/traces","loggerDynatraceUrl":"https:\/\/talkdesk-prod-ca-1.live.dynatrace.com\/api\/v2\/otlp","loggerDynatraceToken":"dt0c01.B46I2I3VEIHOINAUAVCUGUBG.JCEVI5PAK7XJ7I7IZCJLQSOWZSS6DQLUWBUAUMT4Y25HUFIRNV2ENW3AV7DN4AL3","otlpBaseUrl":"https:\/\/talkdesk-prod-ca-1.live.dynatrace.com\/api\/v2\/otlp","otlpToken":"dt0c01.B46I2I3VEIHOINAUAVCUGUBG.JCEVI5PAK7XJ7I7IZCJLQSOWZSS6DQLUWBUAUMT4Y25HUFIRNV2ENW3AV7DN4AL3","otlpBatchDelay":5000,"otlpIgnoreUrls":["eu\\.js\\.logs\\.insight\\.rapid7\\.com","r\\.lr-in\\.com","r\\.intake-lr\\.com","eventgw\\.twilio\\.com"],"otlpPropagateHeaderUrls":["api\\.talkdeskappca\\.com"],"otlpMetricsInterval":10000,"talkdeskEnv":"prd","cobaltThemeJsonUrl":"https:\/\/prd-cdn-talkdesk.talkdesk.com\/cobalt\/theme-light\/2.0.1\/styles.json"}
                const bcChannel = new BroadcastChannel('atlas')

                if(hasIdleDetectionPermission() === 'bypass') {
                    delete runtimeEnv.inactivityTimeout
                }

                if (window.TalkdeskAtlas !== undefined) {
                    TalkdeskAtlas.start(root, {
                        engineType: isRunningInPWA() ? 'pwa' : "web",
                        env: runtimeEnv,
                        onLoad: onLoad
                    })
                        .then(() => {
                            const atlasBootTimeElapsed = performance.now() - atlasLoadStartTime

                            dispatchEvent('AtlasBoot#runtimeSuccess', atlasBootTimeElapsed)

                            measureMetric(
                                'gauge',
                                { name: 'Boot Success Time', value: atlasBootTimeElapsed },
                                { unit: 'millisecond' }
                            )
                            measureMetric('count', { name: 'Boot Success', value: 1 })

                            bcChannel.postMessage({ type: 'atlas:loaded' })
                        })
                        .then(removeSplashScreen)
                        .then(() => { root.style.visibility = 'visible' })
                        .catch((error) => {
                            const atlasErrorTimeElapsed = performance.now() - atlasLoadStartTime

                            dispatchEvent('AtlasBoot#Error', atlasErrorTimeElapsed, { errorName: error.name, errorMessage: error.message })

                            measureMetric('count', { name: 'Boot Error', value: 1 })

                            redirectOnError(new Error('AtlasBoot#Error: Atlas runtime could not be started.'))
                        });
                } else {
                    redirectOnError(new Error('Atlas runtime could not be loaded.'))
                }
            }

            if(hasIdleDetection() && (await hasIdleDetectionPermission() === 'denied')) {
                const redirect = "https:\/\/po-automationca.mytalkdeskca.com\/atlas\/request-idle-permission.html?redirect_to=%2Fatlas%2Fapps%2Foutbound-dialer"
                window.location.replace(redirect)
                return
            }

            if (window.__INVALID_RUNTIME__) {
                const error = new Error('There is something wrong with the runtime URL.')

                loadFallback()
                    .then(start)
                    .finally(() => {
                        reportError(error, { bundleUrl: "https:\/\/prd-cdn-talkdesk.talkdesk.com\/atlas\/0.176.12_atlas-runtime\/runtime\/main.js" })

                        measureMetric('count', { name: 'Fallback Loaded', value: 1 })
                    })
            } else {
                start()
            }
        })
    })()
    /*]]>*/
</script>
</body>
</html>
