{"visitor_info.keep": "Keep", "contact.item.contact_pop": "Contact pop", "visitor_info.problem_with_loading_contacts": "There was a problem loading contacts!", "cards.disposition.no_dispositions_placeholder": "No dispositions available", "conversation_info.ring_groups_other": "{{count}} Queues", "visitor_info.no_results_found_title": "No results found", "contact.item.integrations.default_tooltip": "Show contact details", "contact.dropdown.error": "Could not load contacts list.", "dispositions.dropdown.loading": "Loading", "visitor_info.invalid_number": "Invalid number", "conversation_info.ring_groups": "{{count}} Queue", "dispositions.dropdown.character_limit": "Type at least 3 characters", "components.card.failure.title": "Could not load the card", "contact.item.company": "Company", "create_contact.keep": "Keep", "visitor_info.number": "Number", "components.card.empty_state.title": "There are no contents available for this card", "contact.item.integrations.default_name": "Contacts", "create_contact.merge_confirm": "<PERSON><PERSON> confirm", "contact.dropdown.loading": "Loading", "cards.quality.title": "Call quality", "visitor_info.check_your_network_connection": "Check your network connection", "conversation_info.ring_groups_one": "{{count}} Queue", "create_contact.emails": "Emails", "create_contact.merge": "<PERSON><PERSON>", "create_contact.create": "Create", "visitor_info.oops": "Oops!", "contact.item.number_plural": "Numbers", "visitor_info.associated_successfully": "Contact associated successfully", "cards.context.title": "Context", "cards.quality.dropdown.load_more": "Load more elements...", "visitor_info.email": "Email", "contact.item.email": "Email", "contact.item.email_plural": "Emails", "create_contact.confirm": "Confirm", "visitor_info.no_internet_connection": "No internet connection", "cards.transfer.email.title": "Transferred <PERSON>", "visitor_info.merge": "Merge with existing contact", "contact.item.fax_number_other": "Fax numbers", "create_contact.clear": "Clear", "contact.item.fax_number": "Fax number", "conversation_info.ring_groups_plural": "{{count}} Queues", "transfer.from": "From", "contact.item.number_other": "Numbers", "visitor_info.associate.contacts_list_title": "the most associated contact(s)", "visitor_info.associate": "Associate", "contact.item.integrations.open_in_integration": "Open in {{name}}", "cards.relate_to.description": "Choose the ticket this call is related to", "cards.transfer.sms.title": "Transferred SMS", "create_contact.create_confirm": "Create confirm", "cards.disposition.placeholder": "Add a disposition...", "contact.dropdown.placeholder_multiple_email_contact": "Multiple contacts for this email...", "contact.dropdown.no_results_found": "No results found", "contact.dropdown.error_retry": "Retry", "cards.conversation_info.title": "Queue(s)", "dispositions.dropdown.load_more": "Load more elements...", "create_contact.existing_contacts": "Existing contact(s)", "cards.quality.dropdown.error_load_more": "Error loading more feedback. Try again.", "cards.notes.placeholder": "Add your notes about this conversation", "visitor_info.refresh": "Refresh", "contact.dropdown.refine_search": "{{items}} more contacts. Use the search to refine further.", "contact.item.fax_number_plural": "Fax numbers", "contact.status.deleted": "Deleted", "cards.conversation_details.interaction_id.title": "Interaction ID", "create_contact.back": "Back", "visitor_info.invalid_email": "Invalid email", "contact.dropdown.placeholder_known_contact": "Please select...", "create_contact.create_contact_confirm_tips": "Duplicate information included.", "visitor_info.name": "Name", "cards.notes.title": "Notes", "visitor_info.save": "Save", "cards.transfer.digital.title": "Transferred conversation", "create_contact.potential_duplicate_contacts_found": "Potential duplicate contacts found (matches number or email). Select one to merge.", "create_contact.name_will_replace_exist_name": "This name will replace the exist contact’s name if you keep this field.", "visitor_info.no_associated_contacts_found_title": "No associated contacts found", "dispositions.dropdown.error_load_more": "Error loading more dispositions. Try again.", "cards.notes.max_length_warning": "Maximum system character limit reached", "contact.dropdown.error_load_more": "Error loading more items. Try again.", "cards.transfer.live_chat.title": "Transferred <PERSON>", "cards.contact.title": "Contact details", "create_contact.create_contact": "Create contact", "contact.dropdown.search_all_contacts": "Search all contacts", "cards.relate_to.title": "Relate To", "visitor_info.search_placeholder": "Search by name, phone, email or company", "dispositions.dropdown.search_hint": "Type at least 3 characters to start searching", "visitor_info.associated_failure": "Contact associated failure", "create_contact.cancel": "Cancel", "contact.item.integrations.default_integration": "Open in {{name}} (default)", "create_contact.merge_contact_confirm_tips": "Review the contact before merging. This is a permanent action and cannot be reversed.", "contact.item.number_one": "Number", "cards.quality.dropdown.error_retry": "Retry", "cards.conversation_details.ring_groups.title": "Queue(s)", "create_contact.title_number": "NUMBER", "contact.dropdown.character_limit": "Type at least 3 characters", "dispositions.dropdown.error": "Could not load.", "dispositions.dropdown.error_retry": "Retry", "visitor_info.no_associated_contacts_found_content": "Search by name, number, email or company to find a contact to associate.", "visitor_info.no_results_found_content": "We couldn’t find any contact that match your search criteria", "cards.conversation_details.ring_groups.popup_title": "{{count}} Queues", "cards.conversation_details.title": "Conversation details", "contact.item.fax_number_one": "Fax number", "cards.disposition.title": "Disposition", "create_contact.contact_details": "Contact details", "contact.item.email_one": "Email", "cards.quality.dropdown.error": "Could not load.", "cards.conversation_details.waiting_time.title": "IVR + Queue time", "contact.item.email_other": "Emails", "components.card.required": "Required", "contact.dropdown.back_to_contact": "Back to {{value}}", "components.card.failure.message": "There was a problem while trying to load this card's contents.", "dispositions.dropdown.search_placeholder": "Search...", "contact.dropdown.load_more": "Load more elements...", "contact.dropdown.search_placeholder": "Search in {{value}}", "create_contact.edit_the_information_before_creating": "Edit the information before creating", "cards.quality.dropdown.loading": "Loading", "cards.quality.text": "What was wrong?", "components.card.failure.retry": "Retry", "cards.quality.dropdown.placeholder": "Please select...", "cards.transfer.title": "Transferred call", "cards.notifications.actions.create_contact.failure.title": "Contact created failure", "contact.item.number": "Number", "visitor_info.hint": "Make sure to validate the information by selecting which fields you want to keep before saving", "test.polyglot": "Testing Polyglot Sync - Tabs", "contact.dropdown.placeholder_multiple_contact": "Multiple contacts for this number...", "cards.notifications.error_persist_contact_administrator": "Please try again. If this problem persists, please contact your administrator", "visitor_info.associate_contact": "Associate contact", "visitor_info.phone": "Number", "contact.dropdown.placeholder_search_all_contacts": "Search all contacts...", "cards.notifications.actions.merge_with_existing_contact.failure.title": "Merge with existing contact failure"}