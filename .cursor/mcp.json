{"mcpServers": {"test-lcl-stdio": {"command": "npx", "args": ["tsx", "/Users/<USER>/code/mcps/playwright-test-runner-and-debugger/src/server.ts", "--project-root", "/Users/<USER>/code/mcps/playwright-test-runner-and-debugger", "--playwright-config", "playwright.config.ts", "--playwright-executable", "node_modules/.bin/playwright"]}, "testing-from-npm-registry": {"command": "npx", "args": ["-y", "@perandrestromhaug/playwright-test-runner-and-debugger", "--project-root", "/Users/<USER>/code/mcps/playwright-test-runner-and-debugger", "--playwright-config", "playwright.config.ts", "--playwright-executable", "node_modules/.bin/playwright"]}}}