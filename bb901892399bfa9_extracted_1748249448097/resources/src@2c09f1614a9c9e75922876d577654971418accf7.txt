// @ts-check
const {test, expect,beforeEachWorker,chromium, firefox, webkit} = require("@playwright/test")
const {DialerPage} = require("../../pages/outbound_dialer/dialerPage")
const {RecordListPage} = require("../../pages/outbound_dialer/recordlistPage")
const {TestCampaign} = require("../../test-data/testCampaign")
const {us_qa_FeatureFlag,us_prod_FeatureFlag,ca_prod_FeatureFlag,eu_prod_FeatureFlag} = require("../../test-data/testConversation")
const fs = require("fs");
const sharedJson = "./test-data/shared.json"
const {pauseTestCampaign,bindDNCList,addDNCListWithoutCSV,pauseCampaign,bindRecordsList,getRecordsList,getCallerList,getTokenFromStorage,getCampaignList,createCampaign,startCampaign,deleteCaselist,createView,checkViewList,getCallbackListFromContact,getCallbackStatusFromCallbackID} = require("../../api/apiService")



async function getBrowserVersions() {
    const browserVersions = {};
  
    const chromiumBrowser = await chromium.launch();
    browserVersions.chromium = await chromiumBrowser.version();
    await chromiumBrowser.close();
  
    const firefoxBrowser = await firefox.launch();
    browserVersions.firefox = await firefoxBrowser.version();
    await firefoxBrowser.close();
  
    const webkitBrowser = await webkit.launch();
    browserVersions.webkit = await webkitBrowser.version();
    await webkitBrowser.close();
  
    return browserVersions;
}

// test.beforeAll(async () => {
//     const browserVersions = await getBrowserVersions();
//     console.log('Browser Versions:', browserVersions);
// });

test.beforeEach(async ({}, testInfo) => {
    const accountName = testInfo.project.use.accountName.replaceAll("-","_")
	console.log (`==== 【${accountName}】Running test ${testInfo.title} with worker pid ${process.pid} on ${testInfo.project.use.channel}=====`)
	if(testInfo.title.includes("Smoke-RL Test")){
		if(fs.existsSync(sharedJson)){
			let data = fs.readFileSync(sharedJson,"utf-8");
			let jsonObject = JSON.parse(data);
			if(jsonObject.hasOwnProperty(accountName)){
				eval(`jsonObject.${accountName}={}`)
				fs.writeFileSync(sharedJson,JSON.stringify(jsonObject));
			}else{return}
		/*
		delete shareJson file
		fs.unlink(sharedJson,(err)=>{
      		if(err){throw err};
			console.log("share file is deleted.")
    	 })
		 */
  	    }else{
			let data = {
			[accountName]:{}
		};
  	    	fs.writeFileSync(sharedJson,JSON.stringify(data));
		}
	}
})

test.afterEach(async ({}, testInfo) => {
    const accountName = testInfo.project.use.accountName
    console.log (`==== 【${accountName}】【${testInfo.status}】Finished test ${testInfo.title} on ${testInfo.project.use.channel} with status ${testInfo.status}=====`)
})

// test.afterAll(async({},testInfo)=>{
// 	console.log(`====End Script  pause all campaigns from metoto test=====`)
// 	const endpoint = testInfo.config.projects[0].use.baseServer
//     try{
//         await pauseTestCampaign(endpoint,"metoto")
//     }expect(e){
//         console.log(e.toString())
//     }
//
// })

test.describe.configure({mode: 'parallel'});

let campaign_name;
let selectedRecordList = [];
let selectedDNCList = [];

let preview_campaign_name;
let record_list_name = "";
let dnc_list_name = "";


const testData = new TestCampaign().campaignInfo


const RLSourceBaseDir = "./test-data/records-list/"
const RLFileName = "RL.csv"
const RLFileName_multiPhone = "RL_multi.csv"
const RLFileName_search = "RL_search.csv"
const RLFileName_order = "RL_order.csv"
const RLFilePath = RLSourceBaseDir + RLFileName
const RLFilePath_multiNumber = RLSourceBaseDir + RLFileName_multiPhone
const RLFilePath_search = RLSourceBaseDir+RLFileName_search
const RLFilePath_order = RLSourceBaseDir+RLFileName_order


const dncSourceBaseDir = "./test-data/dnc-list/"
const dncFileName = "dnc_list.csv"
const dncFileName_multiPhone = "multi_dnc_list.csv"
const dncFilePath = dncSourceBaseDir + dncFileName
const dncFilePath_multiNumber = dncSourceBaseDir + dncFileName_multiPhone




test.use({storageState: "./user_localStorage/storage-state.json"})
test("Smoke-RL Test", async ({page,baseURL,storageState},testInfo) => {
    test.setTimeout(360000)
    const testRegion = testInfo.project.use.account[0].local
    const testEnv = testInfo.project.use.account[0].env
    const ff_info = eval(testRegion+"_"+testEnv+"_FeatureFlag")
    const test_ff = new ff_info().ff
    const accountName = testInfo.project.use.accountName.replaceAll("-","_")
    const browserType = testInfo.project.use.channel
    const endpoint = testInfo.project.use.baseServer
	const baseClient = testInfo.project.use.baseClient
    const dialerPage = new DialerPage(page,test_ff)
    const recordlistPage = new RecordListPage(page,test_ff)

    await test.step("Smoke-RCX-1302: Upload a Record List", async () => {
        await test.step("Step1-Click on the Dialer APP.", async () => {
            /*
            Should open Dialer APP showing by default Campaign page.
            */
            console.log("Smoke-RCX-1302-Step1-Click on the Dialer APP.")
            await dialerPage.waitForCampaignPage()
            await dialerPage.saveImage()
            // await dialerPage.hiddenMessageToast()
        })

        await test.step("Step2-Click on the Lists option on the side bar.", async () => {
            /*
            Should show the Record Lists page.
            */
            console.log("Smoke-RCX-1302-Step2-Click on the Lists option on the side bar.")
            await dialerPage.dialerListsNav.click()
            await recordlistPage.wait_for_locator(recordlistPage.dialerPageTitle)
            await expect(await recordlistPage.dialerPageTitle.textContent()).toEqual("Lists")
            await recordlistPage.saveImage()
        })

        await test.step("Step8-Click on \"Upload file\" button.", async () => {
            /*
            Only should be possible to click when: list name is filled and a csv file was uploaded.
            New Record List should appears in the RL page..
            */
            console.log("Smoke-RCX-1302-Step8-Click on \"Upload file\" button.")
            await recordlistPage.clickAddRecordlistButton()
            record_list_name = await recordlistPage.inputRecordsListName("metoto_records_list")

            /*
            share the record_list_name
             */
            let jsonStr = fs.readFileSync(sharedJson,"utf-8");
			const jsonObj = JSON.parse(jsonStr);
			recordlistPage.editJsonObj(jsonObj,`${accountName}.record_list_name`,record_list_name)
			fs.writeFileSync(sharedJson, JSON.stringify(jsonObj));


            await recordlistPage.uploadRecordsCSVFile(RLFilePath)
            await recordlistPage.submitAddRecordsList()
            // await recordlistPage.wait_for_toast()
            await recordlistPage.check_toast_title("List uploaded successfully")
            // add a refresh behavior to make sure the new upload list display
            await recordlistPage.refreshList()
            // await recordlistPage.wait_for_animated(recordlistPage.commonTable)
            const firstName = await recordlistPage.getFirstRowItem("Name")
            await expect(await firstName.textContent()).toEqual(record_list_name)
            await recordlistPage.saveImage()

        })
    })
    await test.step("Smoke-RCX-1305: Check Record Lists ", async () => {

        let api_total;
        let multi_records_list_name;
        let search_record_list_name="";
		let order_record_list_name="";

        await test.step("Step1-Click on Lists side panel", async () => {

            /*
            Should appears record list page showing record lists of the first page.
            */
            await dialerPage.waitForCampaignPage()
            const total = await dialerPage.navToRecordsList()
            api_total = total.toString()
        })

        await test.step("Step2-Check information in the record list page", async () => {
            /*
            Lists as header
            Records tab with underline and Do not call tab
            Total of record lists
            The following column headers: Name, Number of records, campaigns, Created at, Status.
            Last column without header should have available three buttons: API (FF protected), download (FF protected) and bin button.
            10 record lists per page.
            Page numbers, next and previous button at the bottom of the page.
            RL should appears in the following order:
            -Invalid Record Lists (independently of creation date)
            -Valid Record Lists by creation date
            -Corrupted or Incomplete Record List by creation date.
            */
            await recordlistPage.wait_for_locator(recordlistPage.dialerPageTitle)
            await expect(await recordlistPage.dialerPageTitle.textContent()).toEqual("Lists")
            await recordlistPage.waitAndClearToast()
            await recordlistPage.wait_for_animated(recordlistPage.commonTable)
            const record_num = await recordlistPage.getListNum()
            await expect(record_num).toEqual(api_total)
            await recordlistPage.saveImage()
        })

        await test.step("Step3-In column name clicking on the name of a RL.", async () => {
            /*
            Only should be possible to click on the List Name of a RL whose status is validated and to see their details.
            Columns are the same that in the csv file.
            Priority if not defined in csv file, appears as 5 by default .
            Also a column named: Created at, with the date of insertion of Record in a Record List (corresponding to timezone of computer agent).
            Should be possible to search by phone number
            When after to see the details of a RL and clicking to go back to the RL page, should be returned to the page of RL..
            */
            let recordsInfo = await recordlistPage.openRecordsDetail(record_list_name)
            let firstPhoneNumber = recordsInfo.phone
            await recordlistPage.checkColumnDisplay(["Phone number", "First name", "Last name", "Time zone", "Last updated", "Priority"])
            await recordlistPage.searchRecord(firstPhoneNumber)
            const searchPhoneResult = await recordlistPage.getFirstRowItem("Phone number")
            await expect(await searchPhoneResult.textContent()).toContain(firstPhoneNumber)
            await recordlistPage.backToRecordsList()
            await expect(await recordlistPage.dialerPageTitle.textContent()).toEqual("Lists")
            await recordlistPage.saveImage()
        })

        await test.step("Step4-Click again in one RL name and delete one record from record list", async () => {
            /*
            Record should be deleted with success and information about number of records should be updated correctly.
            */
            multi_records_list_name = await recordlistPage.createRL("metoto_multi_records_list", RLFilePath_multiNumber)
            // await recordlistPage.wait_for_toast(recordlistPage.pageToast)
            await recordlistPage.check_toast_title("List uploaded successfully")
            let recordsInfo = await recordlistPage.openRecordsDetail(multi_records_list_name)
            let firstPhoneNumber = recordsInfo.phone
            await recordlistPage.deleteRecordPhoneNumber(firstPhoneNumber)
            await recordlistPage.saveImage()
        })

        await test.step("Step9-Click on List API button", async () => {
            /*
            Click on List API button
            To see a modal open with the following information: List API as header, ID of record list.
            To copy the ID of the RL.
            */
            await dialerPage.navToRecordsList()
            await recordlistPage.clickListAPIButton()
            await expect(recordlistPage.listAPIDialog).toBeVisible()
            await recordlistPage.copyListAPINumber()
            await recordlistPage.closeListAPIDialog()
            await recordlistPage.saveImage()
        })

        await test.step("Step10-Click on Download button - FF = OFF until product decision",async ()=>{
			/*
			Should be possible to download any csv file..
			*/
			console.log(`======1305-Step10======`)
            if(!test_ff.PO_LIST_DOWNLOAD){
                console.log(`the download button feature is not enable`)
            }else{
                await recordlistPage.downloadRLByName(multi_records_list_name)
                await recordlistPage.clickNotificationButton()
                await expect(recordlistPage.notificationPanel).toBeVisible()
                await recordlistPage.checkNotificationFromTodayList(1,multi_records_list_name)
                const [ download ] = await Promise.all([
                   recordlistPage.page.waitForEvent('download'),
                   await recordlistPage.clickNotificationFromTodayList("1")
                ]);
                const path = await download.path();
                await expect(path).toBeTruthy()
                console.log(path)
            }
            await recordlistPage.saveImage()
		})

        await test.step("Step11-Click on bin button", async () => {
            /*
            A modal asking if wants to delete RL should appears.
            Modal should have two button: Cancel and Remove and "X"
            When clicking on Cancel or "X" RL should not be deleted.
            If clicking on "Remove" RL should be deleted and not appears in RL page.
            Only delete RL not associated to one campaign.
            Should not be possible to delete a RL whose status is incomplete being associated or not to one campaing.
            */
            await dialerPage.navToRecordsList()
            await recordlistPage.deleteRecordsList(multi_records_list_name)
            await recordlistPage.saveImage()

        })

        await test.step("Step12-Search Record from records",async ()=>{
			/*
			Should be possible to move between pages.
			*/
			console.log(`======1305-Step12======`)
			await dialerPage.waitForCampaignPage()
			await dialerPage.navToRecordsList()
			search_record_list_name = await recordlistPage.createRL("metoto_search_list",RLFilePath_search)
			await recordlistPage.check_toast_title("List uploaded successfully")
			if(test_ff.PO_RECORD_FILTER){
				//wait for the list validated
				await getTokenFromStorage(baseURL,storageState)
				let resp_getRecordslist = await getRecordsList(endpoint,"ACCEPTED",search_record_list_name)
				let retry=30;
				while(resp_getRecordslist.count==0 && retry>0 ){
					await recordlistPage.wait_for_timeout(1)
					retry-=1
					resp_getRecordslist = await getRecordsList(endpoint,"ACCEPTED",search_record_list_name)
				}

				await recordlistPage.openRecordsDetail(search_record_list_name)

				//search with firstname
				const search_firstName="Ema"
				const resp_firstName = await recordlistPage.searchRecord(search_firstName)
				let count = resp_firstName.count
				await recordlistPage.checkSearchResult(search_firstName,count)

				//search with lastname
				const search_lastName="Schultz"
				const resp_lastName = await recordlistPage.searchRecord(search_lastName)
				count = resp_lastName.count
				await recordlistPage.checkSearchResult(search_lastName,count)

				//search with phoneNumber
				const search_phoneNumber="0506"
				const resp_phoneNumber = await recordlistPage.searchRecord(search_phoneNumber)
				count = resp_phoneNumber.count
				await recordlistPage.checkSearchResult(search_phoneNumber,count)
			}else{
				console.log("current account does not support new record search feature")
			}
            await recordlistPage.saveImage()
		})

        await test.step("Step12-1-Filter Record from records",async ()=>{
			/*
			Filter Record from records.
			*/
			console.log(`======1305-Step12-1======`)
			if(test_ff.PO_RECORD_FILTER){
				//filter with timezone
				await recordlistPage.clearRecordSearch()
				await recordlistPage.clickRecordsFilterButton()
				await recordlistPage.clickRecordsFilterOption("Time zone")
				await recordlistPage.selectTimezones(["Africa/Monrovia","Asia/Shanghai","Sao_Paulo"])
				let summary = await recordlistPage.applyRecordsDetailFilter()
				let filter_count = summary.count
				await recordlistPage.checkFilterResult(filter_count,"timezone",["Africa/Monrovia","Asia/Shanghai","America/Sao_Paulo"])
				await recordlistPage.hoverFilterTag("timezone")

				await recordlistPage.clearAllRecordFilter()

			}else{
				console.log("current account does not support new record filter feature")
			}
            await recordlistPage.saveImage()


		})

    })
})

test("Smoke-DNCL Test", async ({page},testInfo) => {
    test.setTimeout(360000)
    const testRegion = testInfo.project.use.account[0].local
    const testEnv = testInfo.project.use.account[0].env
    const ff_info = eval(testRegion+"_"+testEnv+"_FeatureFlag")
    const test_ff = new ff_info().ff
    const dialerPage = new DialerPage(page,test_ff)
    const recordlistPage = new RecordListPage(page,test_ff)
    const dnclistPage = new RecordListPage(page,test_ff)

    await test.step("Smoke-RCX-1304: Upload a Do Not Call List", async () => {
        /*
        Check that it is possible to Upload a valid Do Not Call List and that this are covered by this test:
        List side bar should show List page.
        In List page and Do Not Call tab should exists a "Upload DNC List" button.
        When clicking on the "Upload DNC List" button should be possible to:
                   1. Upload do not call list
                   2. "Choose a csv file to upload" option
                   3. "Cancel" button
                   4. "Upload file" button
                   5. "Download list template" button.
        When filling the List name field with a name that already exists an error should be shown.
        When clicking on "choose a csv file to upload" should be possible upload only a csv.
        When clicking on cancel button, DNCL creation should be stopped.
        When clicking on "Upload file" button, DNCL should be uploaded.
        When clicking on "Download list template" button should be possible to download a csv file.
        */

        await test.step("Step1-Click on the Dialer APP.", async () => {
            /*
            Should open Dialer APP showing by default Campaign page.
            */
            await dialerPage.waitForCampaignPage()
        })

        await test.step("Step2-Click on the Lists option on the side bar.", async () => {
            /*
            Should show the Record Lists page.
            */
            await dialerPage.navToRecordsList()
            await recordlistPage.wait_for_locator(recordlistPage.dialerPageTitle)
            await expect(await recordlistPage.dialerPageTitle.textContent()).toEqual("Lists")
            await recordlistPage.saveImage()
        })

        await test.step("Step3-Click on Do Not Call tab and in the Upload Record List button.", async () => {
            /*
            Should open a modal that should have:
            1. Upload do not call list
            2. "Choose a csv file to upload" option
            3. "Cancel" button
            4. "Upload file" button
            5. "Download list template" button.
            6. An "X" to close the modal.
            */
            await recordlistPage.clickDNCTab()
            await dnclistPage.clickAddRecordlistButton()
            await dnclistPage.waitAndClearToast()
            await expect(await dnclistPage.modalDialogTitle.textContent()).toEqual("Upload Do Not Call list")
            await expect(await dnclistPage.chooseFileText.textContent()).toEqual("Choose a CSV file")
            await dnclistPage.saveImage()


        })

        await test.step("Step6-Fill the List Name with a unique DNCL name.", async () => {
            /*
            Any error message should appears..
            */
            dnc_list_name = await dnclistPage.inputRecordsListName("metoto_DNC_test_list")
            await dnclistPage.saveImage()

        })

        await test.step("Step7-Click on \"choose a csv file to upload\".", async () => {
            /*
            Choose a csv to be uploaded.
            Only should be possible to upload csv files.
            File uploaded should appears in modal with corresponding name, type of file, size and "x".
            Should be possible to click on "x" and removing this csv file.
            */
            await dnclistPage.uploadRecordsCSVFile(dncFilePath)
            await expect(await dnclistPage.csvFileName.textContent()).toEqual(dncFileName)
            await expect(dnclistPage.csvFileSize).toBeTruthy
            await dnclistPage.deleteCSVFile()
            await expect(dnclistPage.csvFileField).toBeHidden()
            await dnclistPage.saveImage()
        })

        await test.step("Step8-Click on \"Upload file\" button.", async () => {
            /*
            Only should be possible to click when: list name is filled and a csv file was uploaded.
            New DNC List should appears in the DNCL page.
            */
            await dnclistPage.uploadRecordsCSVFile(dncFilePath)
            await dnclistPage.submitAddDNCList()
            // await dnclistPage.wait_for_toast(recordlistPage.pageToast)
            await dnclistPage.check_toast_title("Do not call list upload complete")
            await dnclistPage.wait_for_animated(dnclistPage.commonTable)
            const firstDNCName = await dnclistPage.getFirstRowItem("Name")
            await expect(await firstDNCName.textContent()).toEqual(dnc_list_name)
            await dnclistPage.saveImage()

        })

    })
    await test.step("Smoke-RCX-1306: Do Not Call Lists ", async () => {

        let api_total;
        let multi_dnc_list_name;

        await test.step("Step1-Click on Campaign Manager APP", async () => {
            /*
            Should Open Campaign Manager APP, showing campaign page.
            */
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
        })

        await test.step("Step2-Click on Lists side panel and in the DNC tab", async () => {
            /*
            Should appears DNC list page showing DNC lists of the first page.
            */
            await dialerPage.navToRecordsList()

        })

        await test.step("Step3-Check information in the DNC list page.", async () => {
            /*
            Lists as header
            Records tab  and Do not call tab with underline.
            Total of DNCL lists
            The following column headers: Name, Number of entries, campaigns, Created at, Status.
            Last column without header should have available three buttons: API (FF protected), download (FF protected) and bin button.
            10 DNC lists per page.
            Page numbers, next and previous button at the bottom of the page.
            DNCL should appears in the following order:
            -Invalid DNC Lists (independently of creation date)
            -Valid DNC Lists by creation date
            -Corrupted or Incomplete DNC List by creation date.
            */
            await recordlistPage.wait_for_locator(recordlistPage.dialerPageTitle)
            await expect(await recordlistPage.dialerPageTitle.textContent()).toEqual("Lists")
            api_total = await recordlistPage.clickDNCTab()
            api_total = api_total.total
            const dnc_num = await dnclistPage.getListNum()
            await expect(dnc_num).toEqual(api_total.toString())
            await dnclistPage.saveImage()
        })

        await test.step("Step4-In column name clicking on the name of a DNCL.", async () => {
            /*
            Only should be possible to click on the List Name of a DNCL whose status is validated and to see their details. Columns are the same that in the csv file.
            Should be possible to search by exact phone number
            When after to see the details of a DNCL and clicking to go back to the DNCL page,
            should be returned to the page of DNCL.
            */
            multi_dnc_list_name = await dnclistPage.createDNCL("metoto_multi_dnc_list", dncFilePath_multiNumber)
            // await dnclistPage.wait_for_toast(dnclistPage.pageToast)
            await dnclistPage.check_toast_title("Do not call list upload complete")
            let firstPhoneNumber = await dnclistPage.openDNCDetail(multi_dnc_list_name)
            await dnclistPage.deleteDNCPhoneNumber(firstPhoneNumber)
            await dnclistPage.saveImage()

        })

        await test.step("Step11-Click on bin button", async () => {
            /*
            A modal asking if wants to delete DNCL should appears.
            Modal should have two button: Cancel and Remove and "X"
            When clicking on Cancel or "X" DNCL should not be deleted.
            If clicking on "Remove" DNCL should be deleted and not appears in DNCL page.
            Only delete DNCL not associated to one campaign.
            Should not be possible to delete a DNCL whose status is incomplete being associated or not to one campaing.
            */
            await dialerPage.navToRecordsList()
            await dnclistPage.clickDNCTab()
            await recordlistPage.deleteDNCList(multi_dnc_list_name)
            await dnclistPage.saveImage()

        })


    })
})

test("Smoke-Predicative Campaign Create/Start/Pause/Resume/View/Edit/Check/Dup", async ({page},testInfo) => {
    test.setTimeout(600000)
    const testRegion = testInfo.project.use.account[0].local
    const testEnv = testInfo.project.use.account[0].env
    const ff_info = eval(testRegion+"_"+testEnv+"_FeatureFlag")
    const test_ff = new ff_info().ff
    const accountName = testInfo.project.use.accountName.replaceAll("-","_")
    const browserType = testInfo.project.use.channel

    const dialerPage = new DialerPage(page,test_ff)
    const recordlistPage = new RecordListPage(page,test_ff)


    await test.step("Smoke-RCX-1308: Create Campaign", async () => {

        campaign_name = "metoto_test_" + Math.random().toString(36).substring(2, 10)

        await test.step("Step1-Click on the Campaign Manager APP", async () => {
            console.log(`======1308-Step1======`)
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
        })

        await test.step("Step5-Fill Campaign name with an unique name.", async () => {
            /*
            Should be possible to click on the "Next button" and move to the first step (Mode) when campaign status should appears equal to incomplete with the corresponding icon at the top of the page.
            Check that three buttons are available at the top of the page: discard, previous and next.
            Discard button should be available
            Previous button in this step should be disabled.
            Next button, should be disabled until all fields being filled with accepted values.
            */
            console.log(`======1308-Step5======`)
            await dialerPage.createCampaign(campaign_name)
            await expect(dialerPage.discardBtn).toBeEnabled()
            await expect(dialerPage.previousBtn).toBeVisible()
            await expect(dialerPage.previousBtn).not.toBeEnabled()
            await expect(dialerPage.nextBtn).toBeVisible()
            await expect(dialerPage.nextBtn).not.toBeEnabled()
            await dialerPage.saveImage()
        })

        await test.step("Step7-Change the default priority (5) to another value.", async () => {
            /*
            To choose any value between 1 and 10.
            When a value, e.g. 8 is selected, this meaning that all values before should also appears filled.
            */
            console.log(`======1308-Step7======`)
            await dialerPage.waitAndClearToast()
            await expect(await dialerPage.getSettingTitle()).toEqual("Mode")

            await dialerPage.setDialingMode("Predictive dialing")
            await expect(await dialerPage.campainPrioritylist).toBeVisible()
            await dialerPage.setCampaignPriority(testData.mode.maxPriority)
            await dialerPage.checkPriorityValue(testData.mode.maxPriority)
            await dialerPage.saveImage()
        })

        await test.step("Step9-Fill Max.Dialing Ratio with a valid value - between 1 and 3.", async () => {
            /*
            Value should be accepted.
            */
            console.log(`======1308-Step9======`)
            await dialerPage.setDialingRadio(testData.mode.dialingRadio)
            await expect(await dialerPage.dialingRatioInput_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step11-Fill Max.Abandonment Rate with a valid value - between 1 and 100. ", async () => {
            /*
            Value should be accepted. Also numbers with 2 decimal places can be considered.
            */
            console.log(`======1308-Step11======`)
            await dialerPage.setAbandonmentRate(testData.mode.abandonmentRate)
            await expect(await dialerPage.abandonmentRateSection).not.toHaveClass(/co--error/)
            await dialerPage.saveImage()

        })

        await test.step("Step12-Fill Abandonment Timeout with an valid value", async () => {
            console.log(`======1308-Step12======`)
            await dialerPage.setAbandonmentTimeout(testData.mode.abandonmentTimeout)
            await expect(await dialerPage.abandonmentTimeoutSection).not.toHaveClass(/co--error/)
            await dialerPage.saveImage()
        })

        await test.step("Step13-Fill Max. Ring Time with an valid value", async () => {
            console.log(`======1308-Step13======`)
            await dialerPage.setMaxRingtime(testData.mode.maxRingtime)
            await expect(await dialerPage.maxRingtimeSection).not.toHaveClass(/co--error/)
            await dialerPage.saveImage()
        })

        await test.step("Step15-click on the next button", async () => {
            /*
            Should be possible to click and moving to the second step (Dialing Strategy).
            */
            console.log(`======1308-Step15======`)
            await dialerPage.saveAndNext()
            await expect(await dialerPage.getSettingTitle()).toEqual("Dialing strategy")
            await dialerPage.saveImage()
        })

        await test.step("Step16-Select two caller_ids and click", async () => {
            /*
            Should be possible to select any caller_id from caller_ids available.
            Each caller_id should presents the corresponding flag.
            If more than 10 caller_ids exists, load more button, should appears, being possible to click in the button and to see the following caller_ids.
            */
            console.log(`======1308-Step16======`)
            await dialerPage.selectCallIDs(1)
            await dialerPage.saveImage()
        })

        await test.step("Step17-Select week days for calling hours", async () => {
            /*
            Should be possible to choose one or more week days.
            */
            console.log(`======1308-Step17======`)
            await dialerPage.setCallingDays(testData.dialingStategy.callWeekDays)
            await dialerPage.saveImage()
        })

        await test.step("Step19-Select valid hours to the campaign.", async () => {
            /*
            Should be possible to add calling hours rule with success.
            */
            console.log(`======1308-Step19======`)
            await dialerPage.setCallingFromHour()
            await dialerPage.setFromHour(testData.dialingStategy.fromHour)
            await dialerPage.setFromMinute()
            await dialerPage.setFromMeridian("AM")
            await dialerPage.blur()

            await dialerPage.setCallingToHour()
            await dialerPage.setToHour(testData.dialingStategy.toHour)
            await dialerPage.setToMinute()
            await dialerPage.setToMeridian("AM")
            await dialerPage.blur()
            await dialerPage.saveImage()
        })

        await test.step("Step21-Fill Global Max.Attempts with a valid value (e.g.3) - between 1 and 100.", async () => {
            /*
            Value should be accepted.
            */
            console.log(`======1308-Step21======`)
            await dialerPage.setGlobalMaxAttempts(testData.dialingStategy.maxAttempts)
            await expect(await dialerPage.globalMaxAttemptsInput_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step22-Fill Default Retry Period with valid value", async () => {
            console.log(`======1308-Step22======`)
            await dialerPage.setRetryPeriod(testData.dialingStategy.retryPeriod, testData.dialingStategy.retryPeriodType)
            await expect(await dialerPage.retryPeriodInput_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step23-In System Dispositions click on the link: admin area of your Talkdesk account.", async () => {
            /*
            Should be redirected to the corresponding talkdesk account page.
            */
            console.log(`======1308-Step23======`)
            if(!test_ff.PO_NEW_RETRIES_UI_ENABLED){
                // await dialerPage.callDispositionLink.click()
                const [newPage] = await Promise.all([
                    page.context().waitForEvent('page'),
                    await dialerPage.callDispositionLink.click()
                ])
                expect(newPage.url()).toContain("admin/numbers")
                await newPage.close()
                await dialerPage.active()
            }else{
                console.log(`the system disposition is remove in new UI version`)
            }
            await dialerPage.saveImage()
        })

        await test.step("Step26-Click on the next button", async () => {
            /*
            Should be moved to the step 3 or Agents page.
            */
            console.log(`======1308-Step26======`)
            await dialerPage.transiteToAgentStep()
            await expect(await dialerPage.getSettingTitle()).toEqual("Agents")
            await dialerPage.saveImage()
        })

        await test.step("Step29-Filter agents by their ring group", async () => {
            /*
            Should be possible to filter agents by ring group
            Should be possible to write and search by ring groups.
            Should be possible to click on select all, to select all ring groups
            Should be possible to select one or more ring groups.
            Two buttons should be available: Clear and Apply
            When clicking on Clear button, modal for filter should be closed.
            When clicking on Apply button, number with number of filters should appears, and also ring groups should appears in the header, with possibility to remove clicking on "X" or in the "clear all".
            */
            console.log(`======1308-Step29======`)
            await dialerPage.selectRingGroups(["agents"])
            await dialerPage.clickClearFilterSettings()
            await dialerPage.selectRingGroups(testData.agents.ringGrouplist)
            await dialerPage.clickClearFilterSettings()
            await dialerPage.selectRingGroups([""], "all")
            await dialerPage.clickClearFilterSettings()
            await dialerPage.clickFilterButton()
            await dialerPage.cancelFilterPanel()
            await expect(dialerPage.ringGroupbox).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step30-In Agents page select one agent or all agents using select all funtionality", async () => {
            /*
            Should be possible to select 1 or more agents or all agents on the page
            */
            console.log(`======1308-Step30======`)
            await dialerPage.selectAgents(testData.agents.agentlist)
            await dialerPage.saveImage()
        })

        await test.step("Step32-Click on the next button", async () => {
            /*
            Should be possible to move to step 4 or Lists
            */
            console.log(`======1308-Step32======`)
            await dialerPage.clickAssignmentDetails(await dialerPage.getTableItem(dialerPage.commonTable, "1"))
            await dialerPage.close_assignmentDetailPanel()
            const pageSize = await dialerPage.getListSize(dialerPage.commonTable)
            await expect(pageSize).toEqual(1)
            await dialerPage.saveAndNext()
            await expect(dialerPage.createBtn).toBeVisible()
            await dialerPage.saveImage()

        })

        await test.step("Step36-Select 2 record lists to associated to the campaign and click on the apply button", async () => {
            /*
            Lists selected should be appears in  list step page.
            Should be shown the RL name and Records in Lists.
            */
            console.log(`======1308-Step36======`)
            const selectRecordList = await dialerPage.addRecordLists(["1", "2", "3"], "")
            await dialerPage.checkSelectedRecordList(selectRecordList)
            await dialerPage.saveImage()
        })

        await test.step("Step37-Remove one of the RL clicking on the bin button", async () => {
            /*
            RL should be removed
            */
            console.log(`======1308-Step37======`)
            await dialerPage.removeSelectedRecordListByIndex([1, 2,3],"1")

            /*
				wait 60s if the record_list_name is still not set
				 */
			let data = fs.readFileSync(sharedJson,"utf-8");
			let jsonObject = JSON.parse(data);
			let wait=60
			while(jsonObject.record_list_name==="" && wait>0){
			  console.log("=======wait for 1 seconds=========");
			  await dialerPage.wait_for_timeout(1);
			  data = fs.readFileSync(sharedJson,"utf-8");
			  jsonObject = JSON.parse(data);
			  wait-=1;
			}
			record_list_name = eval(`jsonObject.${accountName}.record_list_name`)
			console.log(`the record_list_name now for ${accountName} on ${browserType} browser is ${record_list_name}`)
			if(record_list_name===""){
				throw new Error("The record_list_name still empty")
			}

            await dialerPage.addRecordLists(["1"],record_list_name)


            selectedRecordList = await dialerPage.getSelectedRecordList()
            selectedDNCList = await dialerPage.getSelectedDNCList()
            await dialerPage.saveImage()
        })

        await test.step("Step38-Click on the \"Assign DNC lists\" button", async () => {
            /*
            A modal should be open
            */
            console.log(`======1308-Step38======`)
            await dialerPage.clickAddDNCListButton()
            await expect(dialerPage.commonPanel).toBeVisible()
            await dialerPage.closeDNCLPanel()
            await expect(dialerPage.commonPanel).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step39-Select 1 DNCL to associated to the campaign", async () => {
            /*
            In modal it is possible to select one or more than one DNCL.
            When more than one DNCL is selected, Apply button should change to disabled.
            10 DNCL should appears
            If more 10 DNCL exists, should exists pages should appears and should be possible to select DNCL in different pages and move between pages.
            However if more than 1 DNCL is selected, add button should becomes disabled.
            Should be possible to select all DNCL and also to remove the selection made.
            Two buttons should be available: cancel and assign.
            DNCL selected should be appears in  list step page.
            Should be shown the DNCL name and Number of entries.
            */
            console.log(`======1308-Step39======`)
            const selectedDNCName = await dialerPage.addDNCLists("1")
            await dialerPage.checkSelectedDNCLists(selectedDNCName)
            await dialerPage.saveImage()
        })

        await test.step("Step42-Click on the Create Button", async () => {
            /*
            A new campaign should be created.
            New campaign should appears in the campaign page.
            Status of campaign should appears has validated if has agents selected and RL associated.
            Their status should appears as incomplete if no agents were selected and/or one RL is not associated.
            */
            console.log(`======1308-Step42======`)
            await dialerPage.saveAndSubmit()
            // await dialerPage.wait_for_toast()
            await dialerPage.check_toast_title("Campaign created")
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            await dialerPage.searchCampaigns(campaign_name,"Exact")
            const newCampaignName = await dialerPage.getCampaignName(await dialerPage.getTableItem(dialerPage.commonTable, "1"))
            await expect(newCampaignName).toEqual(campaign_name)

            // the record list binded successfully but need to wait several seconds and then campaign message can display.
            await dialerPage.wait_for_timeout(5)
            await dialerPage.navToRecordsList()
            const campaignsColumnIndex = await recordlistPage.getTableColumnIndex(recordlistPage.commonTableHeader, "Campaigns")
            await recordlistPage.searchRecords(record_list_name, "Exact", campaignsColumnIndex)
            const campColumn = await recordlistPage.getTableColumnIndex(recordlistPage.commonTableHeader,"Campaigns")
            await recordlistPage.wait_for_animated(recordlistPage.commonTable,campColumn)
            let records_campaignlist = await recordlistPage.getFirstRowCampaigns("Campaigns")
            let retry = 10
            while (records_campaignlist.length === 0 && retry > 0) {
                await recordlistPage.refreshList()
                await recordlistPage.wait_for_timeout(1)
                records_campaignlist = await recordlistPage.getFirstRowCampaigns("Campaigns")
                retry -= 1
            }
            await expect(records_campaignlist).toContain(campaign_name)
            await dialerPage.saveImage()

        })

        await test.step("Step43-Check the record list could be filtered by campaign", async () =>{
			/*
			The recordlist is filter by the new campaign.
			*/
			console.log(`======1308-Step43======`)
            if(!test_ff.PO_LIST_DOWNLOAD){
                console.log(`the Campaign filter feature is not enable`)
            }else{
                await dialerPage.navToRecordsList()
			    await recordlistPage.checkRecordsCampaign(campaign_name)
            }
            await dialerPage.saveImage()
		})

    })

    await test.step("Smoke-RCX-1310:Start Campaign", async () => {

        await test.step("Step1-Click on the start button ", async () => {
            /*
            Should be possible to click on snapshot button and to see information about record.
            */
            await dialerPage.waitForCampaignPage()
            await dialerPage.searchCampaigns(campaign_name, "Exact")
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus_before = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            await expect(campaignsStatus_before).toEqual("Ready")

            await dialerPage.startCampaignFromList(campaign_name)
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus_after = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            await expect(campaignsStatus_after).toEqual("Running")
            await dialerPage.saveImage()
        })
    })

    await test.step("Smoke-RCX-1311 Pause Campaign ", async () => {

        await test.step("Step1-Click on the Pause button", async () => {
            /*
            Campaign should stop running and their status should changed to paused.
             */
            await dialerPage.waitForCampaignPage()
            await dialerPage.searchCampaigns(campaign_name, "Exact")
            await dialerPage.pauseCampaignFromList(campaign_name)
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus_pause = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            await expect(campaignsStatus_pause).toEqual("Paused")
            await dialerPage.saveImage()
        })
    })

    await test.step("Smoke-RCX-1315 Resume Campaign ", async () => {

        await test.step("Step1-Click on the Resume button", async () => {
            /*
            Campaign should start running and their status should changed to running.
            */
            console.log("=============Step1============")
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
            await dialerPage.searchCampaigns(campaign_name, "Exact")
            await dialerPage.resumeCampaignFromList(campaign_name)
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus_after = await dialerPage.getFirstRowItem("Status")
            // const campaignsStatus_after =await dialerPage.getTabelCellValue("Status",1,dialerPage.commonTable,dialerPage.commonTableHeader)
            expect(await campaignsStatus_after.textContent()).toEqual("Running")
            await dialerPage.saveImage()
        })

        await test.step("Step2-Click on campaing name", async () => {
            /*
            In view mode everything should appears correctly filled.
            */
            console.log("=============Step2============")
            await dialerPage.clickTabelCellByIndex(1, 1, dialerPage.commonTable, dialerPage.commonTableHeader, "a")
            await dialerPage.saveImage()
        })

        await test.step("Step3-Move to agent pages", async () => {
            /*
            Sort agents by name (this feature was removed with RCX-1696)
            Move between pages
            Search agent by name (this feature was removed with RCX-1696)
            Filter by ring group (this feature was removed with RCX-1696)
            */
            console.log("=============Step2============")
            await dialerPage.AgentNavTab.click()
            await expect(dialerPage.commonTable).toBeVisible()
            await dialerPage.saveImage()
        })
    })

    await test.step("Smoke-RCX-1313 View a Campaign ", async () => {

        await test.step("Step1-Clicking on the Campaing Name", async () => {
            /*
            Should be possible to see campaign in view mode and should be open in Mode tab with the status of campaign equal to running and with the corresponding icon..
            */
            console.log("=============Step1============")
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
            await expect(await dialerPage.getPageTitle()).toEqual("Dialer")
            await dialerPage.searchCampaigns(campaign_name, "Exact")
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus_before = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            if (campaignsStatus_before === "Incomplete") {
                console.error("the campaign is not ready !")
                return
            }
            if (campaignsStatus_before !== "Running") {
                await dialerPage.startCampaignFromList(campaign_name)
            }
            const campaignsStatus_after = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            // await dialerPage.clickTabelCellByIndex(1,1,dialerPage.commonTable,dialerPage.commonTableHeader,"a")
            // await dialerPage.page.pause()

            await dialerPage.clickCampaignName()
            // let re = new RegExp('https:(.*?)\/campaigns\/[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$');
            // let re = new RegExp('https:(.*?)\/campaigns\/\\d{1,9}$')
            // await Promise.all([
            //     dialerPage.page.waitForResponse(response => re.test(response.url()) && response.status() === 200 && response.request().method() === "GET"),
            //     await dialerPage.clickTabelCellByIndex(1, 1, dialerPage.commonTable, dialerPage.commonTableHeader, "a")
            // ])
            await dialerPage.wait_for_locator(dialerPage.campaignsStatus)
            const statusTitle = await dialerPage.campaignsStatus.textContent()
            await expect(statusTitle).toContain(campaignsStatus_after)
            await dialerPage.saveImage()
        })

        await test.step("Step2-Check Mode tab", async () => {
            /*
            In Mode tab, should not be possible to change any fields.
            */
            console.log("=============Step2============")
            await expect(dialerPage.dialingRatioInput).toBeHidden()
            await expect(dialerPage.abandonmentRateInput).toBeHidden()
            await expect(dialerPage.abandonmentTimeoutInput).toBeHidden()
            await expect(dialerPage.maxRingtimeInput).toBeHidden()
            await expect(dialerPage.answerMachineDetectSection).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step3-Click on Dialing Strategy tab.", async () => {
            /*
            In Dispositions tab should be possible to click on:
            admin area of your Talkdesk account and to be redirected for the page..
            */
            console.log("=============Step3============")
            await dialerPage.dialingStrategyNavTab.click()
            await expect(dialerPage.callIDslink).toBeHidden()
            await expect(dialerPage.callFromHoursInput).toBeHidden()
            await expect(dialerPage.callToHoursInput).toBeHidden()
            await expect(dialerPage.globalMaxAttemptsInput).toBeHidden()
            await expect(dialerPage.retryPeriodInput).toBeHidden()

            if(!test_ff.PO_NEW_RETRIES_UI_ENABLED){
                await expect(dialerPage.callDispositionLink).toBeVisible()
                await expect(dialerPage.systemDispositionsType_Busy).toBeVisible()
                await dialerPage.systemDispositionsType_Busy.click()
                await expect(dialerPage.systemDispositionsApplyButton).toBeHidden()
                await expect(dialerPage.systemDispositionsClearButton).toBeHidden()
            }else{
                console.log(`the system disposition is remove in new UI version`)
            }
            await dialerPage.saveImage()

        })

        await test.step("Step4-Click on Agent tab.", async () => {
            /*
            All agents selected should appears
            Should be shown 10 agents per page.
            */
            console.log("=============Step4============")
            await dialerPage.navToAgent()
            // await dialerPage.AgentNavTab.click()
            // await dialerPage.waitForAgentList()
            // await dialerPage.wait_for_animated(dialerPage.commonTable)
            const aglist = testData.agents.agentlist
            for (let row in aglist) {
                const tableItem = await dialerPage.getTableItem(dialerPage.commonTable, parseInt(row) + 1)
                const agentName = await dialerPage.getTabelCellByIndex(dialerPage.commonTableHeader, tableItem, 1)
                await expect.soft(aglist).toContain(await agentName.textContent())
                console.log(await agentName.textContent())
            }
            const selectedAgentCount = await dialerPage.tableToolBar.textContent()
            // console.log(selectedAgentCount.split(" agents")[0])
            await expect(selectedAgentCount.split(" agents")[0]).toEqual(aglist.length.toString())
            await dialerPage.saveImage()

        })

        await test.step("Step5-Click on Lists tab.", async () => {
            /*
            In Lists tab should be possible to see RL and DNCL assign to the campaign.
            */
            console.log("=============Step5============")
            await dialerPage.ListsNavTab.click()
            await expect(dialerPage.selectedRecordlistTable).toBeVisible()
            await expect(dialerPage.selectedDNClistTable).toBeVisible()
            const selectedRecordList_view = await dialerPage.getSelectedRecordList()
            const selectedDNCList_view = await dialerPage.getSelectedDNCList()
            await expect.soft(selectedRecordList_view).toEqual(selectedRecordList)
            await expect.soft(selectedDNCList_view).toEqual(selectedDNCList)
            await dialerPage.saveImage()

        })

        await test.step("Step6-Click on Reporting tab.", async () => {
            /*
            In view mode should be available a Reporting tab with two clickable buttons:
            "See on Explorer" to Campaign Dashboard and Campaign Reports, that redirected for the corresponding pages.
            */
            console.log("=============Step6============")
            await dialerPage.reportNavTab.click()
            await expect(await dialerPage.getSettingTitle()).toEqual("Reporting")
            await expect(dialerPage.openCampaignDashboardButton).toBeEnabled()
            await expect(dialerPage.openCallsReportButton).toBeEnabled()
            await dialerPage.saveImage()
        })
    })

    await test.step("Smoke-RCX-1312 Edit Campaign", async () => {

        await test.step("Step1-Click on the campaign name that should to edit", async () => {
            /*
            Should be open Mode tab with the current status of campaign and their icon appearing on the top of the page
            and that should be equal to the campaign status on the campaign’s page.
             */
            console.log("=============Step1============")
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
            await dialerPage.searchCampaigns(campaign_name, "Exact")
            await dialerPage.pauseCampaignFromList(campaign_name)

            await dialerPage.clickTabelCellByIndex(1, 1, dialerPage.commonTable, dialerPage.commonTableHeader, "a")
            await dialerPage.waitAndClearToast()
            await dialerPage.saveImage()
        })

        await test.step("Step2-In Mode tab change Answering Machine Detection to Enabled", async () => {
            /*
            Should be possible to change this value and moving to Dialing Strategy tab.
            */
            console.log("=============Step2============")
            if (await dialerPage.answerMachineDetectEnableSection.isVisible()) {
                await dialerPage.answerMachineDetectSection.click()
            }
            await expect(dialerPage.answerMachineDetectEnableSection).not.toBeVisible()
            await dialerPage.answerMachineDetectSection.click()
            await expect(dialerPage.answerMachineDetectEnableSection).toBeVisible()
            await dialerPage.saveImage()
        })

        await test.step("Step3-In Dialing Strategy tab change one the Custom System Disposition", async () => {
            /*
            Should be possible to change this and moving to Agents tab.
            */
            console.log("=============Step3============")
            await dialerPage.dialingStrategyNavTab.click()
            // await dialerPage.setSystemDispositions("Busy", "Final")
            // await expect(dialerPage.radioRetryButton).not.toBeChecked()
            // await expect(dialerPage.radioFinalButton).toBeChecked()
            // await dialerPage.setSystemDispositions("Abandoned", "Final")
            // await expect(dialerPage.radioRetryButton).not.toBeChecked()
            // await expect(dialerPage.radioFinalButton).toBeChecked()
            if(!test_ff.PO_NEW_RETRIES_UI_ENABLED){
                await dialerPage.setSystemDispositions("Busy", "Final")
                await expect(dialerPage.radioRetryButton).not.toBeChecked()
                await expect(dialerPage.radioFinalButton).toBeChecked()
                await dialerPage.setSystemDispositions("Busy", "Retry")
                await expect(dialerPage.radioRetryButton).toBeChecked()
                await expect(dialerPage.radioFinalButton).not.toBeChecked()
            }else{
                console.log(`the system disposition is remove in new UI version`)
            }
            await dialerPage.saveImage()

        })

        await test.step("Step5-In Lists tab remove DNCL.", async () => {
            /*
            Should be possible to do this and moving to Reporting tab and/or save the changes..
            */
            console.log("=============Step5============")
            await dialerPage.ListsNavTab.click()
            // const selectedDNCName = await dialerPage.addDNCLists("2")
            // console.log("selectedDNCName is "+selectedDNCName)
            // await dialerPage.checkSelectedDNCLists(selectedDNCName)
            await dialerPage.removeSelectedDNClist()

            await dialerPage.saveAndSubmit()
            await dialerPage.saveImage()
            // await dialerPage.wait_for_toast()

        })

    })

    await test.step("Smoke-RCX-1316：Check campaign list", async () => {

        await test.step("Step1-Click on the Campaign Manager APP", async () => {
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
        })

        await test.step("Step2-Check information in the campaign list page", async () => {
            /*
            Campaigns as header
            A Create campaing button
            Total of campaigns
            The following column headers: Name, Priority, Dialing Mode, Status, Buttons related with status
            10 campaigns per page.
            Page numbers, next and previous button at the bottom of the page.
            Campaigns should appears by creation order.
            */
            await expect(await dialerPage.campTitle.textContent()).toEqual("Campaigns")
            await expect(dialerPage.createCampaignBtn).toBeVisible()
            await dialerPage.saveImage()

        })

        await test.step("Step3-Look to columns", async () => {
            /*
            Campaigns names in column name should be clickable.
            Priority column should show priority of the campaign.
            Dialing Mode column should show only Predictive.
            Status column should indicating one the following status to the campaign: Ready, Paused, Running or Incomplete.
            Clickable buttons: Start, Resume, Pause should appears in the last column with exception for campaigns with status equal to incomplete that haven’t any this button.
            */
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            await dialerPage.checkCampaignsListColumn()
            await dialerPage.saveImage()

        })

        await test.step("Step5-In text box search a campaign by name", async () => {
            /*
            Only campaigns with that name should appears
            */
            const searchName = await dialerPage.searchCampaigns(campaign_name, "Fuzzy")
            await dialerPage.checkSearchResultByName(searchName)
            await dialerPage.saveImage()
        })

    })

    await test.step("Smoke-RCX-2529：Duplicate campaign", async () => {

        let totalNum;

        await test.step("Step1-Click on duplicate campaign button", async () => {
            /*
            A modal should open with:
            Title equal to Duplicate campaign
            Information about Campaign to be duplicated
            New campaign name
            Place holder equal to Campaing name + copy
            0/35 as limit
            Create and cancel button
            "x" button
            */
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
            // await dialerPage.wait_for_locator(dialerPage.campTitle)
            totalNum = await dialerPage.getCampaignsCountFromList()
            await dialerPage.clickDupCampaignButton(campaign_name)
            await expect(await dialerPage.createCampaignDialogTitle.textContent()).toEqual("Duplicate campaign")
            await expect(await dialerPage.campaignNameInput.getAttribute("placeholder")).toEqual(campaign_name + " Copy")
            await dialerPage.campaignNameInput.fill(campaign_name + " Copy")
            await expect(dialerPage.cancelBtn).toBeVisible()
            await expect(dialerPage.confirmBtn).toBeEnabled()
            await expect(dialerPage.closeDialogButton).toBeEnabled()
            await dialerPage.saveImage()
        })

        await test.step("Step5-Click on create button", async () => {
            /*
            Campaign should be open in edit mode in mode tab and all fields should appears filled.
            Save and discard button should be disabled
            */
            let re = new RegExp('https:(.*?)\/campaigns\\?source_id')
            await Promise.all([
                dialerPage.page.waitForResponse(response => re.test(response.url()) && response.status() === 201 && response.request().method() === "POST"),
                await dialerPage.waitAndClearToast(),
                await dialerPage.confirmCreateCampaign()
            ])
            // await dialerPage.confirmBtn.click()

            // await dialerPage.page.waitForResponse(response => re.test(response.url()) && response.status()===201 && response.request().method()==="POST" )
            await dialerPage.waitAndClearToast()
            await expect(await dialerPage.campaignsStatus.textContent()).toContain("Incomplete")
            await dialerPage.saveImage()

        })

        await test.step("Step6-Move to Dialing strategy tab", async () => {
            /*
            All fields should appears correctly filled
            */
            await dialerPage.dialingStrategyNavTab.click()
            await dialerPage.saveImage()
        })

        await test.step("Step7-Move to Agents tab", async () => {
            /*
            All fields should appears correctly filled
            */
            await dialerPage.AgentNavTab.click()
            await dialerPage.saveImage()
        })

        await test.step("Step8-Move to Lists tab", async () => {
            /*
            Lists from original campaign shouldn’t appears
            */
            await dialerPage.ListsNavTab.click()
            await dialerPage.saveImage()
        })

        await test.step("Step9-Click on < button to return to the campaign’s page", async () => {
            /*
            Duplicated campaign should appears on the campaign page and their status should be equal to incomplete
            */
            await dialerPage.backToCampaignsListButton.click()
            await dialerPage.searchCampaigns(campaign_name + "_Copy", "Exact")
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const campaignsStatus = await dialerPage.getTabelCellValue("Status", 1, dialerPage.commonTable, dialerPage.commonTableHeader)
            await expect(campaignsStatus).toEqual("Incomplete")
            await dialerPage.saveImage()

        })

    })

    await test.step("Smoke-RCX-xxxx：Delete campaign", async () => {

        await test.step("Step1-Click on delete campaign button and confirm", async () => {
            /*
            Delete campaign
            */
            await dialerPage.waitForCampaignPage()
            let totalNum_before_delete = await dialerPage.getCampaignsCountFromList()
            await dialerPage.deleteCampaignFromList(campaign_name + " Copy")
            await dialerPage.waitAndClearToast()
            let totalNum_after_delete = await dialerPage.getCampaignsCountFromList()

            await expect(eval(totalNum_before_delete-totalNum_after_delete)).toEqual(1)
            await dialerPage.saveImage()

        })

    })


})

test("Smoke-Preview Campaign Create", async ({page},testInfo) => {
    test.setTimeout(180000)
    const testRegion = testInfo.project.use.account[0].local
    const testEnv = testInfo.project.use.account[0].env
    const ff_info = eval(testRegion+"_"+testEnv+"_FeatureFlag")
    const test_ff = new ff_info().ff
    const dialerPage = new DialerPage(page,test_ff)

    await test.step("Smoke-RCX-3738：Create a Preview Campaign", async () => {

        preview_campaign_name = "metoto_test_preview_" + Math.random().toString(36).substring(2, 10)

        await test.step("Step1-Click on Create campaign button and write valid for campaign name and click on create button", async () => {
            /*
            Mode tab should be shown with predictive dialing mode selected, priority with 5 by default,
            and all other fields empty
            */
            await dialerPage.waitForCampaignPage()
            // await dialerPage.waitForFrameLoad()
            await dialerPage.createCampaign(preview_campaign_name)
            await dialerPage.waitAndClearToast()
            await expect(await dialerPage.getSettingTitle()).toEqual("Mode")
            await expect(await dialerPage.campainPrioritylist).toBeVisible()
            const defaultPriority = await dialerPage.campainPrioritylist.locator("div.co-rating__option.co--active>label").textContent()
            await expect(defaultPriority).toEqual("5")
            await dialerPage.saveImage()

        })

        await test.step("Step2-Change dialing mode to preview dialing", async () => {
            /*
            Only Max Ring time field should appears
            */
            await dialerPage.setDialingMode("Preview dialing")
            await dialerPage.saveImage()

        })

        await test.step("Step4-Write a valid Max. Ring time value", async () => {
            /*
            Next button should be enabled and should be possible to click on it and move to Dialing Strategy tab
            */
            await dialerPage.setMaxRingtime(testData.mode.maxRingtime)
            await expect(dialerPage.maxRingtimeInput_feedback).toBeHidden()
            // await dialerPage.saveAndNext()
            // await expect( await dialerPage.getSettingTitle()).toEqual("Dialing strategy")
            await dialerPage.saveImage()
        })

        await test.step("Step6-Choose Strategy Type as Automatic", async () => {
            /*
            Should be possible to select this
            */

            await dialerPage.setStrategyType("Automatic")
            await dialerPage.saveImage()

        })

        await test.step("Step7-Write a valid value on Connection Timeout field", async () => {
            /*
            lower than 6
            higher than 300
            decimal value between 6 and 300
            decimal value lower than 6
            decimal value higher than 300
            The following error message should appears: "Please insert a value between 6 and 300, without decimal places"
            */
            await dialerPage.setConnectionTimeout(testData.dialingStategy.connectionTimeout)
            await expect(dialerPage.connectionTimeoutInput_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step9-Write a valid value fo Maximum skips per record (e.g. 2)", async () => {
            /*
            Error message should disappears
            */
            await dialerPage.setMaxSkipTime(testData.dialingStategy.maxSkipTime)
            await expect(dialerPage.maxSkipsPerRecordInput_feedback).toBeHidden()

            await dialerPage.saveAndNext()
            await expect(await dialerPage.getSettingTitle()).toEqual("Dialing strategy")
            await dialerPage.saveImage()
        })

        await test.step("Step11-Select valid calling hours", async () => {
            /*
            Error message should disappears
            */
            await dialerPage.setCallingDays(testData.dialingStategy.callWeekDays)
            await dialerPage.setCallingFromHour()
            await dialerPage.setFromHour(testData.dialingStategy.fromHour)
            await dialerPage.setFromMinute()
            await dialerPage.setFromMeridian("AM")
            await dialerPage.blur()

            await dialerPage.setCallingToHour()
            await dialerPage.setToHour(testData.dialingStategy.toHour)
            await dialerPage.setToMinute()
            await dialerPage.setToMeridian("AM")
            await dialerPage.blur()

            await expect(dialerPage.callHours_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step13-Write a valid value for Global max. attempts per recordn", async () => {
            /*
            Error message should disappears
            */
            await dialerPage.setGlobalMaxAttempts(testData.dialingStategy.maxAttempts)
            await expect(await dialerPage.globalMaxAttemptsInput_feedback).toBeHidden()
            await dialerPage.saveImage()

        })

        await test.step("Step15-Write a valid value", async () => {
            /*
            Error message should disappears and button should be enabled now
            */
            await dialerPage.setRetryPeriod(testData.dialingStategy.retryPeriod, testData.dialingStategy.retryPeriodType)
            await expect(await dialerPage.retryPeriodInput_feedback).toBeHidden()
            await dialerPage.saveImage()
        })

        await test.step("Step16-Select one phone number", async () => {
            /*
            Should be possible select and click on next button and
            fill other tabs (agents and lists) in order to create a campaign
            */
            await dialerPage.selectCallIDs(1)
            await dialerPage.saveAndNext()
            await dialerPage.selectAgents(testData.agents.agentlist)
            await dialerPage.saveAndNext()
            await dialerPage.addRecordLists(["4"], "")
            await dialerPage.saveAndSubmit()
            // await dialerPage.wait_for_toast()
            await dialerPage.wait_for_animated(dialerPage.commonTable)
            const newCampaignName = await dialerPage.getCampaignName(await dialerPage.getTableItem(dialerPage.commonTable, "1"))
            await expect(newCampaignName).toEqual(preview_campaign_name)
            await dialerPage.saveImage()
        })


    })
})

