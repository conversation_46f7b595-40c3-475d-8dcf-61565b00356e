{"files": ["/pages/basePage.js", "/pages/outbound_dialer/dialerPage.js", "/tests/smoke/test_campaign_record_flow_clone.spec.js"], "stacks": [[11, [[0, 228, 23, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 411, 30, ""], [2, 407, 20, ""], [2, 389, 16, ""]]], [13, [[0, 229, 29, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 411, 30, ""], [2, 407, 20, ""], [2, 389, 16, ""]]], [27, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 278, 20, "DialerPage.waitForError_new"], [0, 231, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [29, [[0, 283, 37, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [31, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 284, 24, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [33, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 278, 20, "DialerPage.waitForError_new"], [0, 285, 24, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [35, [[0, 283, 37, "DialerPage.waitForError_new"], [0, 285, 13, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [37, [[0, 486, 25, "DialerPage.hiddenConflictToast"], [0, 232, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [39, [[1, 329, 38, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [41, [[1, 414, 31, "DialerPage.getPageTitle"], [1, 330, 27, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [43, [[0, 292, 47, "DialerPage.waitForVisibleLocator"], [1, 346, 38, "DialerPage.waitForFrameLoad"], [1, 331, 14, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [45, [[1, 387, 44, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [47, [[1, 389, 34, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [49, [[1, 396, 42, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]], [51, [[1, 397, 42, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 411, 13, ""], [2, 407, 9, ""], [2, 389, 5, ""]]]]}