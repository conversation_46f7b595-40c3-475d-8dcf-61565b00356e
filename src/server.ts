#!/usr/bin/env node
import AdmZ<PERSON> from "adm-zip";
import { Content, FastMCP, imageContent } from "fastmcp";
import fs from "fs";
import path from "path";
import { z } from "zod";

import { filterNetworkTraceWithPreset } from "./networkTraceFilter.js";
import { filterTraceWithPreset } from "./traceFilter.js";

/**
 * Creates a filtered version of a network trace file to reduce size and remove bloated data
 * @param networkTraceFilePath The path to the original network trace file
 * @returns The path to the filtered network trace file
 */
async function createFilteredNetworkTrace(
  networkTraceFilePath: string
): Promise<string> {
  const filteredPath = networkTraceFilePath.replace(
    ".network",
    "_filtered.network"
  );

  // Check if filtered network trace already exists and is newer than original
  if (fs.existsSync(filteredPath)) {
    const originalStat = fs.statSync(networkTraceFilePath);
    const filteredStat = fs.statSync(filteredPath);
    if (filteredStat.mtime > originalStat.mtime) {
      return filteredPath;
    }
  }

  try {
    // Use the TypeScript network filtering function with minimal preset
    await filterNetworkTraceWithPreset(
      networkTraceFilePath,
      filteredPath,
      "minimal"
    );
    // console.log(`Created filtered network trace: ${filteredPath}`);
    return filteredPath;
  } catch (error) {
    // console.warn(`Error filtering network trace: ${error}`);
    return networkTraceFilePath;
  }
}

/**
 * Creates a filtered version of a trace file to reduce size and remove bloated data
 * @param traceFilePath The path to the original trace file
 * @returns The path to the filtered trace file
 */
async function createFilteredTrace(traceFilePath: string): Promise<string> {
  const filteredPath = traceFilePath.replace(".trace", "_filtered.trace");

  // Check if filtered trace already exists and is newer than original
  if (fs.existsSync(filteredPath)) {
    const originalStat = fs.statSync(traceFilePath);
    const filteredStat = fs.statSync(filteredPath);
    if (filteredStat.mtime > originalStat.mtime) {
      return filteredPath;
    }
  }

  try {
    // Use the TypeScript filtering function
    await filterTraceWithPreset(traceFilePath, filteredPath, "minimal");
    // console.log(`Created filtered trace: ${filteredPath}`);
    return filteredPath;
  } catch (error) {
    // console.warn(`Error filtering trace: ${error}`);
    return traceFilePath;
  }
}

/**
 * Extracts trace.zip file to a temporary directory
 * @param traceZipPath The path to the trace.zip file
 * @returns An object with the output directory path
 */
function extractTraceZip(traceZipPath: string): {
  outputDir: string;
} {
  if (!fs.existsSync(traceZipPath)) {
    throw new Error(`Trace file not found: ${traceZipPath}`);
  }

  // Create a unique output directory based on the zip file name and timestamp
  const zipBaseName = path.basename(traceZipPath, '.zip');
  const timestamp = Date.now();
  const outputDir = path.join(path.dirname(traceZipPath), `${zipBaseName}_extracted_${timestamp}`);

  const zip = new AdmZip(traceZipPath);
  zip.extractAllTo(outputDir, true);

  // Create filtered trace file if the main trace exists
  const mainTraceFile = `${outputDir}/0-trace.trace`;
  if (fs.existsSync(mainTraceFile)) {
    // Create filtered trace asynchronously - don't wait for it
    createFilteredTrace(mainTraceFile).catch((error) => {
      // console.warn(`Failed to create filtered trace: ${error}`);
    });
  }

  // Create filtered network trace file if the network trace exists
  const networkTraceFile = `${outputDir}/0-trace.network`;
  if (fs.existsSync(networkTraceFile)) {
    // Create filtered network trace asynchronously - don't wait for it
    createFilteredNetworkTrace(networkTraceFile).catch((error) => {
      // console.warn(`Failed to create filtered network trace: ${error}`);
    });
  }

  return {
    outputDir,
  };
}

const server = new FastMCP({
  name: "Playwright Trace Analyzer",
  version: "1.0.0",
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Network Log from Trace",
  },
  description:
    "Get browser network logs from a trace.zip file. By default returns a filtered version that removes analytics, third-party services, and verbose metadata while preserving essential debugging information. Use raw=true to get unfiltered logs.",
  execute: async (args) => {
    const { outputDir } = extractTraceZip(args.traceZipPath);
    let output = "";
    const originalNetworkFilePath = `${outputDir}/0-trace.network`;

    try {
      if (args.raw) {
        // Return raw unfiltered network log
        const networkContent = fs.readFileSync(originalNetworkFilePath, "utf8");
        output += `Raw network log (unfiltered):\n\n${networkContent}`;
      } else {
        // Return filtered network log
        const filteredNetworkFilePath = await createFilteredNetworkTrace(
          originalNetworkFilePath
        );
        const networkContent = fs.readFileSync(filteredNetworkFilePath, "utf8");

        if (filteredNetworkFilePath.includes("_filtered.network")) {
          output += `Filtered network log (80%+ size reduction, third-party services removed):\n\n${networkContent}`;
        } else {
          output += `Network log:\n\n${networkContent}`;
        }
      }
    } catch (error) {
      output += `\n\nError reading network file: ${error}`;
    }
    return output;
  },
  name: "get-network-log",
  parameters: z.object({
    raw: z
      .boolean()
      .optional()
      .default(false)
      .describe(
        "Return raw unfiltered network log including all analytics, third-party services, and verbose metadata. Default is false (filtered)."
      ),
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Trace from Trace File",
  },
  description:
    "Get the trace from a trace.zip file. This includes step-by-step playwright test execution info along with console logs. By default returns a filtered version that removes bloated data like DOM snapshots while preserving essential debugging information. Use raw=true to get unfiltered traces.",
  execute: async (args) => {
    const { outputDir } = extractTraceZip(args.traceZipPath);
    let output = "";
    const originalTraceFilePath = `${outputDir}/0-trace.trace`;

    try {
      if (args.raw) {
        // Return raw unfiltered trace
        const traceContent = fs.readFileSync(originalTraceFilePath, "utf8");
        output += `Raw trace content (unfiltered):\n\n${traceContent}`;
      } else {
        // Return filtered trace
        const filteredTraceFilePath = await createFilteredTrace(
          originalTraceFilePath
        );
        const traceContent = fs.readFileSync(filteredTraceFilePath, "utf8");

        if (filteredTraceFilePath.includes("_filtered.trace")) {
          output += `Filtered trace content (95%+ size reduction applied):\n\n${traceContent}`;
        } else {
          output += `Trace content:\n\n${traceContent}`;
        }
      }
    } catch (error) {
      output += `\n\nError reading trace: ${error}`;
    }
    return output;
  },
  name: "get-trace",
  parameters: z.object({
    raw: z
      .boolean()
      .optional()
      .default(false)
      .describe(
        "Return raw unfiltered trace including all DOM snapshots and verbose data. Default is false (filtered)."
      ),
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Screenshots from Trace",
  },
  description:
    "Get all available screenshots from a trace.zip file. Useful for debugging.",
  execute: async (args) => {
    const { outputDir } = extractTraceZip(args.traceZipPath);

    const resourcesDir = `${outputDir}/resources`;
    if (!fs.existsSync(resourcesDir)) {
      return `Resources directory not found in the trace file`;
    }

    try {
      const files = fs.readdirSync(resourcesDir);
      const imageFiles = files.filter((file) => /\.(jpe?g|png)$/i.test(file));

      if (imageFiles.length === 0) {
        return "No screenshots found in the trace file";
      }
      const content: Content[] = [];
      for (const imgFile of imageFiles) {
        const fullPath = `${outputDir}/resources/${imgFile}`;
        content.push({
          text: `Screenshot: ${imgFile}`,
          type: "text",
        });
        content.push(await imageContent({ path: fullPath }));
      }
      return {
        content,
      };
    } catch (error) {
      if (error instanceof Error) {
        return `Error listing screenshots: ${error.message}`;
      }
      return `Unknown error listing screenshots`;
    }
  },
  name: "get-screenshots",
  parameters: z.object({
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

// 导出函数供测试使用
export { extractTraceZip, createFilteredTrace, createFilteredNetworkTrace };

// 只有在直接运行时才启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  server.start({
    transportType: "stdio",
  });
}
