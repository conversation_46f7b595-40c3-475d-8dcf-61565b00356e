{"automation.confirm": "Confirm", "teaser.alt_image": "Request Upgrade", "recomendationType.automation": "Automation", "generic.recommendation": "Recommendation", "pinned.showSidePanelButton": "Pinned messages", "emptyState.error.refresh": "Refresh", "date.months.mar": "March", "date.months.may": "May", "recomendationType.answer": "Answer", "date_months_mar": "March", "navbar.recommendations": "Recommendations", "article.updated": "Updated", "date.abbr_week.wed": "Wed", "exportModal.or": "OR", "messages.bookmark_create_error": "Failed to add bookmark", "bookmarks_answers_title": "Answers", "generic_agent": "Agent", "messages_connection_error": "Something went wrong", "interaction_summary_title": "Summary", "navbar.transcription": "Transcription", "highlights_widget_title": "Highlights", "regenerate": "Regenerate", "hoursAgo": "{hours} hours ago", "empty_state_bookmarks.title": "No bookmarks yet", "date.abbrWeek.sun": "Sun", "quick_answer_title": "Quick Answer for {intent}", "transcription.teaser.title": "Never ask the same question twice!", "emptyState.timeline.title": "Timeline of events", "wrapUp.callEnded": "Call ended", "navbar.search": "Search", "action_buttons_restore": "Rest<PERSON>", "wrap_up.label": "Wrap-up", "date.abbr_week.mon": "Mon", "search.need_help": "Do you need help?", "action_buttons_expand": "Expand", "add_to_notes": "Add to Notes", "empty_state_error_alt_image": "No elements were found (img)", "generic_app_launchpad": "AI Launchpad", "smart_scripts_recent_title": "Recent", "smart_scripts_placeholder_image_alt": "Placeholder image for empty scripts list", "rag_answer": "Generated Answer", "date.months.jul": "July", "highlights.newEntries": "New Entries", "source_open_url": "Open in {source}", "messages.websocketParsingError": "Failed to process server response", "bookmarks_edited_tag": "Edited", "export_modal.include_timestamps": "Include timestamps", "bookmarks_page_title": "Bookmarks", "empty_state.transcriptions.title": "Call transcript", "top_searches_title": "Most frequent searches", "date.months.aug": "August", "date.months.dec": "December", "pinned.pin_tooltip": "Pin message", "generic_contact": "Contact", "websocket_cannot_connect": "Agent <PERSON><PERSON> could not connect to the server", "tip_agent_assist_title": "Resolve customer issues quickly", "teaser.upgrade.request.success_message": "We'll reach out to you soon", "recommendations.quickAnswer": "Quick Answer", "virtual_agent": "Virtual Agent", "ai_generated_error": "Couldn’t generate with AI", "ai_generated_send_action": "Add to Disposition & Notes", "messages_add_template_error": "Unable to add to message", "bookmarks_remove_bookmark": "Unbookmark", "protocol_navigation_error": "Could not navigate to {app}", "teaser.upgrade.request": "Request Upgrade", "noIntent": "No intent match", "viewer_summary_title": "Interaction Summary", "go_back": "Back", "tip_feedback_title": "Copilot needs your feedback", "close": "Close", "search.placeholder": "Search...", "bookmarks_deleted_tag": "Deleted", "date_months_may": "May", "get_started_card_ai_models_subtitle": "Enable AI Models to automate your contact center performance", "add_to_disposition": "Add to Disposition", "synced_on": "Synced on {updatedAt}", "top_intents_recommendations": "Top recommended article", "wrap_up.export_transcription": "Export transcription", "export_modal.export_transcription": "Export transcription", "on_maintenance_description": "We expect to be back in a couple hours. Thank you for your patience", "minutesAgo": "{minutes} min ago", "messages.websocket_closed": "Lost connection to server", "date.abbrWeek.tue": "<PERSON><PERSON>", "readMore": "Read more", "ai_generated_feedback_negative": "No", "ai_generated_feedback_thank_you_message": "Thank you for the feedback!", "messages.bookmark_delete_error": "Failed to remove bookmark", "kBExternalSources.webcrawlerv3": "Web Crawler", "exportModal.includeTimestamps": "Include timestamps", "emptyState.recommendations.searching": "Searching for recommendations...", "messages.websocketReconnected": "Reconnected to server", "top_intents_sub_title": "See the best answer to each trending topic", "bookmarks.add_bookmark": "Bookmark", "date.abbrWeek.wed": "Wed", "generate_answer_error": "Couldn’t generate answer", "messages_bookmark_create_error": "Could not add bookmark", "feedback_message": "Was this helpful?", "pinned.numberPinnedMessages": "{count, plural, =1 {1 pinned message} other {{count} pinned messages}}", "ai_generated_source_tag": "AI-generated", "top_intents_title": "Trending topics", "automations_item_name": "Smart script for {name}", "quick_replies_next_btn": "Next", "empty_state.no_content.description": "Get your answers on Agent Assist by connecting your knowledge base or creating new content in Knowledge Management.", "empty_state.error.alt_image": "No elements were found (img)", "pinned.copyAllPinnedButton": "<PERSON>py all pinned messages", "date_months_feb": "February", "date_months_jan": "January", "empty_state_error_title": "Something went wrong", "empty_state_no_content_alt_image": "No content configured", "date.abbr_week.thu": "<PERSON>hu", "search_ariaLabel": "Search by term", "read_more": "Read more", "teaser_upgrade_agent": "Reach out your administrator to upgrade your account!", "footer.recommendations": "Recommendations", "search.no_results": "No search results for <strong>{searchTerm}</strong>", "empty_state.no_content.btn_label": "Go to Knowledge Management", "date.months.apr": "April", "messages.connection_error": "Something went wrong", "ai_generated_sources": "Sources", "tip_knowledge_description": "Jumping between lots of different apps to get the right answer? Copilot does that job for you, so you don't need to switch windows.", "no_answer_recommendations_available_warning": "There is no available answer. Use the recommendations available, or try again later.", "pinned.pinTooltip": "Pin message", "bookmarks_btn_tooltip": "See bookmarks", "interaction_disposition_title": "Disposition", "search.ariaLabel": "Search by term", "pinned.show_side_panel_button": "Pinned messages", "empty_state_digital_description": "Copilot is looking for the best tips to help you during your interaction with the customer", "highlights.new_entries": "New Entries", "empty_state.recommendations.searching": "Searching for recommendations...", "recommendations_widget_title": "Recommendations", "websocket_failure": "Copilot lost connection to the server", "messages.websocket_reconnected": "Reconnected to server", "emptyState.transcriptions.title": "Call transcript", "date_months_oct": "October", "automation_title": "Automation for {intent}", "get_started_skip_btn": "Skip for now", "date.months.nov": "November", "bookmarks.quick_answer_title": "Quick Answer for <strong>{intent}</strong>", "ai_fact_check_label": "AI-generated answer", "wrap_up.copy_pinned_messages": "<PERSON><PERSON> pinned messages", "rich_media_speak_tag": "Speak", "date_months_aug": "August", "kBExternalSources.webcrawler": "Web Crawler", "exportModal.copyToClipboard": "Copy to clipboard", "generic_app": "Copilot", "transcription_widget_title": "Transcription", "answers_content_widget_title": "Answers & content", "kBExternalSources.confluence": "Confluence", "viewer_summary_disclaimer": "Summarization feature in Preview mode", "gpt_answers_preview_label": "This feature is in preview mode", "kBExternalSources.custom": "Custom", "gpt_answers_title": "Answers", "kBExternalSources.remoteFile": "Remote File", "kBExternalSources.salesforce": "Salesforce", "websocket_closed": "There is a connection issue.", "transcription_match_count": "{count, plural, =1 {1 match} other {{count} matches}}", "empty_state.recommendations.description": "Agent Assist will recommend the most relevant content or actions during a conversation so you can help your customers faster", "bookmarks_edited_tag_tooltip": "This article has been edited in {source} since you bookmarked it on {createdAt}", "updated_on": "Updated on {updatedAt}", "teaser.upgrade.request.success_btn": "Keep searching", "date.abbrWeek.fri": "<PERSON><PERSON>", "highlights_widget_sub_title": "Main topics of the conversation", "search.noResults": "No results found for", "pinned.side_panel_title": "Pinned messages", "viewer_default_title": "Automation", "tip_agent_assist_description": "Copilot interprets your conversations with customers in real time and automatically looks for the best answer to each question.", "gpt_answers_dismiss": "<PERSON><PERSON><PERSON>", "warning_page_title": "Get Started", "emptyState.timeline.searching": "Searching for highlights...", "automation_placeholder": "Insert {label}", "navbar.highlights": "Highlights", "automations_source": "Automation Designer", "empty_state.highlights.searching": "Searching for highlights...", "date.abbrWeek.thu": "<PERSON>hu", "generic.app": "Agent Assist", "source_open_url_fallback": "Open in knowledge base", "talkdesk": "Talkdesk", "search_need_help": "Do you need help?", "insert_value": "Insert value", "wrap_up.call_ended": "Call ended", "export_modal.or": "OR", "ai_generated_title": "Disposition and Summary", "generate_answer": "Help me answer", "rag_tooltip": "Copilot generated answer based on your company's knowledge base content", "pinned.sidePanelTitle": "Pinned messages", "wrapUp.copyPinnedMessages": "<PERSON><PERSON> pinned messages", "automation.next": "Next", "slider_suggestions": "Suggestions", "plus": "Plus", "smart_scripts_placeholder_title": "No recent smart scripts", "search_teaser_title": "Tired of searching?", "bookmarks_automations_title": "Automations", "warning_page_subtitle": "Follow the recommended steps to start using Copilot", "teaser.upgrade.request.title": "Request Upgrade", "summary_failed_to_load_content": "Not able to provide summary", "wrapUp.label": "Wrap-up", "empty_state.error.title": "Something went wrong", "empty_state.highlights.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "teaser_upgrade_admin": "Upgrade your account to access this feature!", "search.result_count": "{count, plural, =1 {1 search result for <strong>{searchTerm}</strong>} other {{count} search results for <strong>{searchTerm}</strong>}}", "kBExternalSources.sharepointv2": "Sharepoint", "empty_state_no_content_btn_label": "Go to Knowledge Management", "navbar_transcription": "Transcription", "footer.transcription": "Transcription", "search.heading": "Search on Agent Assist", "date.months.sep": "September", "transcription_teaser_title": "Never ask the same question twice!", "transcription_teaser_description": "With live call transcriptions, you will never miss a detail of the conversation.<br>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "search_initial_title": "Don't know how to start?", "kBExternalSources.c2Perform": "C2Perform", "viewer_summary_error": "There was an issue communicating with the summarization service.", "date.abbr_week.sun": "Sun", "empty_state.error.refresh": "Refresh", "kBExternalSources.sharepoint": "Sharepoint", "kBExternalSources.googledrive": "Google Drive", "kBExternalSources.intercom": "Intercom", "summaryOnDemandTooltip": "Summarize every 20 seconds what happened until now", "secondsAgo": "a few seconds ago", "recommendation_type.automation": "Automation", "teaser.upgrade.request.success_title": "Upgrade request sent", "messages_bookmark_cannot_add_deleted": "This content was deleted and cannot be bookmarked", "messages_get_automation_error": "Could not retrieve automation {name}", "teaser_upgrade_request_error_message": "We cannot process your request.<br>Please, try again in a few seconds", "action_buttons_search": "Search", "date.abbrWeek.mon": "Mon", "ai_generated_feedback_positive": "Yes", "teaser_upgrade_request_success_title": "Upgrade request sent", "emptyState.recommendations.title": "Recommended content and actions", "automation.placeholder": "Insert {label}", "emptyState.timeline.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "footer.timeline": "Timeline", "ai_generated_btn": "Generate with AI", "search.welcome": "Welcome to Agent <PERSON>!", "generic.recommendations": "Recommendations", "summary_failed_to_load_disposition": "Not able to provide disposition", "date.abbr_week.tue": "<PERSON><PERSON>", "bookmarks_deleted_tag_tooltip": "This article was deleted in {source} on {deletedAt}", "get_started_card_voice_transcription_subtitle": "Identify languages and assign them to your users", "emptyState.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "empty_state.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "copiedToClipboardSuccess": "Copied to clipboard", "date.months.jan": "January", "kBExternalSources.serviceNow": "ServiceNow", "kBExternalSources.zendeskGuide": "Zendesk Guide", "exportModal.downloadMessage": "The download is starting, if you do not see it check your browser permissions", "search_teaser_description": "With live call transcriptions, you will never miss a detail of the conversation.<br>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "viewer_empty_state_message": "This automation does not include a flow that can be used in Agent Assist", "download_message_success": "The download is starting, if you do not see it check your browser permissions", "export_modal.download_file": "Download file (.txt)", "empty_state_digital_title": "Searching for recommendations...", "messages_bookmark_list_error": "Cannot retrieve bookmarks", "updated": "Updated {updatedAt}", "slider_see_all": "See all ({count})", "mainNotEnoughUtterancesTooltip": "Not enough information to summarize yet", "search.teaser.description": "Request the full version of Agent Assist and get the answers you are looking for when you most need them, with zero manual effort.", "export_modal.download_message": "The download is starting, if you do not see it check your browser permissions", "viewer_empty_or_invalid_flow": "The content is either missing or not properly configured, check with your conversation designer", "search_results_count": "{count, plural, =1 {<strong>1 Result</strong> from your sources} other {<strong>{count} Results</strong> from your sources}}", "empty_state.no_content.alt_image": "No content configured", "pinned.unpin_tooltip": "Unpin message", "date_months_jun": "June", "top_searches_subtitle": "Click to search for answers for these topics", "new_app.empty_state.transcriptions.title": "Call transcript", "quick_answer": "Quick Answer", "top_intents_error": "Could not retrieve recommended articles", "search.updated": "Updated {updatedAt}", "smart_scripts_page_title": "Smart scripts", "add_template_to_message": "Add to message", "emptyState.error.altImage": "No elements were found (img)", "pinned.copy_all_pinned_button": "<PERSON>py all pinned messages", "ai_generated_try_again": "Try again", "recommendations_filters_answers": "{count, plural, =1 {1 Answer} other {{count} Answers}}", "slider_placeholder": "Searching for recommendations...", "messages_smart_scripts_list_error": "Cannot retrieve smart scripts", "ai_fact_check_explanation": "Fact-check before answering", "messages.connectionError": "Something went wrong", "messages.websocketClosed": "Lost connection to server", "generic_language": "configured", "messages.feedback_error": "Something went wrong while submitting your feedback. Please try again.", "messages.websocket_parsing_error": "Failed to process server response", "tip_bookmark_title": "Was that answer useful?", "exportModal.downloadFile": "Download file (.txt)", "generic.contact": "Contact", "top_searches_empty_msg": "Start searching now", "date.abbrWeek.sat": "Sat", "action_buttons_download": "Download .txt", "empty_state_error_refresh": "Refresh", "on_maintenance_title": "Copilot is currently down for maintenance", "kBExternalSources.webcrawlerv2": "Web Crawler", "bookmarks_knowledge_title": "Knowledge", "recommendations.seeInfo": "See more information", "feedback_error": "Something went wrong while submitting your feedback. Please try again.", "messages.automation_error": "Something went wrong while submitting your action. Please try again.", "new_app.empty_state.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "ai_generated_feedback_title": "Was this helpful?", "search_initial_subtitle": "Try these suggestions", "get_started_pending_tasks_btn": "See pending tasks", "pinned.unpinTooltip": "Unpin message", "search.teaser.title": "Tired of searching?", "empty_state.highlights.title": "Highlights of events", "transcription_new_message": "New message", "tip_knowledge_title": "The knowledge you need is right here", "knowledge_management": "Knowledge Management", "add_to_notes_error_title": "Couldn't add message to Notes", "automation_success": "Your action was submitted successfully", "article.header": "Go back", "search_placeholder": "Search your knowledge base", "add_to_notes_error_description": "Sorry! The Notes widget is currently unavailable.", "empty_state_no_content_title": "Fill up your box!", "empty_state_error_message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "empty_state_bookmarks.description": "Bookmark answers and access them here", "new_app.search.ariaLabel": "Search by term", "empty_state.no_content.title": "Fill up your box!", "date.abbr_week.fri": "<PERSON><PERSON>", "copy_message_tooltip": "Copy to clipboard", "bookmarks_add_bookmark": "Bookmark", "teaser.upgrade.request.error_message": "We cannot process your request.<br>Please, try again in a few seconds", "date.months.feb": "February", "empty_state.no_content.note": "Reach out to your manager to start creating content!", "bookmarks.remove_bookmark": "Unbookmark", "empty_state.bookmarks.title": "No bookmarks yet", "pinned.number_pinned_messages": "{count, plural, =1 {1 pinned message} other {{count} pinned messages}}", "emptyState.recommendations.description": "Agent Assist will recommend the most relevant content or actions during a conversation so you can help your customers faster", "transcription.teaser.description": "With live call transcriptions, you will never miss a detail of the conversation.<br/>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "messages_automations_list_error": "Cannot retrieve automations", "regenerateNotEnoughUtterancesTooltip": "No new information to summarize yet", "slider_last": "{count, plural, =0 {Latest} other {Latest (+{count})}}", "call_accepted": "{<PERSON><PERSON><PERSON>} accepted call ⋅ {time}", "transcription_search_keywords": "Search for keywords...", "ai_generated_title_tooltip": "AI-generated from transcription", "highlights.teaser.description": "Agent Assist will work on the background to <strong>create a live summary of the conversation</strong>, with the <strong>best answers</strong> for each customer query being displayed as you go.<br><strong>No more manual search or jumping between apps.</strong>", "search.description": "Search for useful content to assist you during your interaction with the customer", "websocket_reconnected": "Agent Assist reconnected to the server", "empty_state.error.message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "websocket_call_event_no_config": "Missing configurations in Launchpad", "messages_bookmark_delete_error": "Could not remove bookmark", "kBExternalSources.sharepointv3": "Sharepoint", "get_started_card_ai_models_title": "Empower Copilot with AI", "empty_state.bookmarks.description": "Bookmark recommendations to quickly access them at any time", "gpt_answers_tooltip": "Copilot identifies unanswered customer queries and writes an answer based on your company's Knowledge Base content.", "smart_scripts_placeholder_text": "Real-time smart scripts recommendations will appear here, as well as scripts from the last interaction", "tip_feedback_description": "Use the thumbs up and down buttons to let us know if a recommendation was useful or not. Our algorithms are always learning, and your contribution is precious.", "gpt_answers_fetch_article_error": "Unable to fetch the article", "no_answer_available_warning": "There is no answer available. Try again later.", "copyMessageTooltip": "Copy to clipboard", "recommendations.readMore": "Read more", "get_started_header": "Get started", "empty_state_no_content_note": "Reach out to your manager to start creating content!", "search_initial_show_all": "Show all", "teaser.upgrade.admin": "Upgrade your account to access this feature!", "websocket_call_event_unsupported_language": "The configured service does not support the \"{language}\" value in the language configuration.", "empty_state_no_content_description": "Get your answers on Copilot by connecting your knowledge base or creating new content in Knowledge Management.", "date_months_jul": "July", "preview": "Preview", "date_months_sep": "September", "slider_first": "First", "bookmarks.km_source": "Knowledge Management", "bookmarks.knowledge_title": "Knowledge", "tip_bookmark_description": "Bookmark relevant answers and articles to easily access them at any time, without the need to search or ask someone again.", "search_welcome": "Welcome to Copilot", "new_app.bookmarks.page_title": "Bookmarks", "summary": "Summary", "quick_replies_key_press_hint": "press ENTER ↵", "date_months_dec": "December", "date.months.jun": "June", "date.months.oct": "October", "highlights.teaser.title": "Focus on your customer!", "recomendationType.article": "Article", "teaser_upgrade_request_title": "Request Upgrade", "generic.agent": "Agent", "automation_error": "Something went wrong while submitting your action. Please try again.", "new_app.messages.bookmark_list_error": "Cannot retrieve bookmarks", "search.initial.title": "Don't know how to start?", "wrapUp.exportTranscription": "Export transcription", "exportModal.exportTranscription": "Export transcription", "empty_state_transcriptions_description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "date_months_apr": "April", "search_no_results": "No search results for <strong>{searchTerm}</strong>", "emptyState.highlights.title": "Highlights of events", "recommendation_type.article": "Article", "messages.automationError": "Something went wrong while submitting your action. Please try again.", "search.initial.show_all": "Show all", "date_months_nov": "November", "get_started_card_voice_transcription_title": "Enable voice transcription of customer interactions", "teaser.upgrade": "Reach out your administrator to upgrade your account!", "search_action_tooltip": "Search this message", "recommendation_type.answer": "Answer", "messages_flow_step_error": "Could not communicate with server", "empty_state_transcriptions_searching": "Waiting for the conversation to start...", "websocket_parsing_error": "Failed to process server response", "highlights.teaser.description.text": "Agent Assist will work on the background to <strong>create a live summary of the conversation</strong>, with the <strong>best answers</strong> for each customer query being displayed as you go.<br/><strong>No more manual search or jumping between apps.</strong>", "transcription.teaser.description.text": "With live call transcriptions, you will never miss a detail of the conversation.<br/>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "recommendations_widget_sub_title": "{count} suggested in total", "messages.bookmark_list_error": "Failed to retrieve bookmarks", "smart_script_item_name": "Smart script for {name}", "smart_scripts_btn_tooltip": "See smart scripts", "teaser_upgrade_request_success_btn": "Keep searching", "automation_confirm": "Confirm", "teaser.upgrade.agent": "Reach out your administrator to upgrade your account!", "emptyState.highlights.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "top_intents_empty_recommendations": "There are no recommended articles for this topic in your knowledge base. If you need help handling this topic, please ask your manager.", "emptyState.error.message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "emptyState.error.title": "Something went wrong", "copied_to_clipboard_success": "Copied to clipboard", "summary_title": "Interaction summary", "emptyState.highlights.searching": "Searching for highlights...", "new_app.search_placeholder": "Search your knowledge base", "interaction_next_actions_title": "Agent next steps", "messages.feedbackError": "Something went wrong while submitting your feedback. Please try again.", "kBInternalKnowledge": "Internal Knowledge", "recommendations_filters_articles": "{count} Content", "automation_next": "Next", "kBExternalSources.fileUpload": "File Upload", "summary_failed_to_load_next_actions": "Not able to provide next actions", "empty_state_transcriptions_title": "Call transcript", "gpt_answers_fallback_results": "It wasn’t possible to generate an answer but these articles might help you:", "generic_provider": "default", "transcription_widget_sub_title": "{duration} call", "pleaseTryAgainLater": "Please try again later.", "summaryCreated": "Summary created", "summaryNotCreated": "Summary not created", "viewer_empty_state_title": "Invalid Flow", "viewer_empty_or_invalid_flow_title": "Could not load smart script step", "export_modal.copy_to_clipboard": "Copy to clipboard", "date.abbr_week.sat": "Sat", "search.initial.subtitle": "Try these suggestions", "generic_app_knowledge_management": "Knowledge Management", "action_buttons_summarize": "Summarize", "teaser_upgrade_request_success_message": "We'll reach out to you soon", "empty_state.recommendations.title": "Recommended content and actions", "smart_scripts_recommended_title": "Recommended", "footer.search": "Search"}