!function(){var e={2706:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.encode=function(e){return JSON.stringify(d(e))},t.encodeAsTable=function(e){return d(e)},t.decode=function(e){return function(e){if("number"==typeof e&&e<0)return n(e);var t=new Array(e.length);function r(e){return e in t?t[e]:t[e]=n(e)}function n(t){if(t<0){if(t===i)return;if(t===a)return;if(t===s)return NaN;if(t===u)return 1/0;if(t===l)return-1/0;throw new Error("invalid ARSON index: "+t)}var n=e[t];if(n&&"object"===(0,o.default)(n)){if(Array.isArray(n)){var d=n[0];if("string"==typeof d&&d in c){var h=c[d].reconstruct,y=h();return y&&f.push({reconstruct:h,empty:y,argIndexes:n.slice(1)}),e[t]=y||h(n.slice(1).map(r))}}p.push(n)}return n}var f=[],p=[];return e.forEach((function(e,t){r(t)})),f.forEach((function(e){e.args=e.argIndexes.map(r)})),p.forEach((function(t){Object.keys(t).forEach((function(n){var o=t[n];if("number"==typeof o)if(o<0){if(o===a)return void delete t[n];t[n]=r(o)}else t[n]=e[o]}))})),f.forEach((function(e){e.reconstruct.call(e.empty,e.args)})),e[0]}(JSON.parse(e))};var o=n(r(8698)),i=-1,a=-2,s=-3,u=-4,l=-5,c=Object.create(null),f=function(e,t){function r(r){if("function"!=typeof t[r])throw new Error("second argument to ARSON.registerType("+JSON.stringify(e)+", ...) must be an object with a "+r+" method")}r("deconstruct"),r("reconstruct"),c[e]=t},p=Object.prototype.toString;function d(e){var t=[],r="function"==typeof Map&&new Map;function n(e){switch((0,o.default)(e)){case"undefined":return i;case"number":if(isNaN(e))return s;if(!isFinite(e))return e<0?l:u}var n;return r?void 0===(n=r.get(e))&&(n=t.push(e)-1,r.set(e,n)):(n=t.indexOf(e))<0&&(n=t.push(e)-1),n}function f(e){var t=e;if(e&&"object"===(0,o.default)(e)){var r=Object.keys(e);if(function(e){var t=e&&"object"===(0,o.default)(e);if(t){return(Object.getPrototypeOf?Object.getPrototypeOf(e):e.__proto__)===Object.prototype}return!1}(e))t={};else{if(!Array.isArray(e)){for(var i in c){var s=c[i].deconstruct(e);if(s){for(l=0;l<s.length;++l)s[l]=n(s[l]);return s.unshift(i),s}}return{}.toString.call(e)}t=Array(e.length);var u=e.length;if(u>r.length)for(var l=0;l<u;++l)t[l]=a}r.forEach((function(r){t[r]=n(e[r])}))}return t}var p=n(e);if(p<0)return p;for(var d=[],h=0;h<t.length;++h)d[h]=f(t[h]);return d}"function"==typeof Buffer&&"function"==typeof Buffer.isBuffer&&f("Buffer",{deconstruct:function(e){return Buffer.isBuffer(e)&&[e.toString("base64"),"base64"]},reconstruct:function(e){return e&&Buffer.from(e[0],e[1])}}),f("Date",{deconstruct:function(e){return"[object Date]"===p.call(e)&&[e.toJSON()]},reconstruct:function(e){return e&&new Date(e[0])}}),f("RegExp",{deconstruct:function(e){if("[object RegExp]"===p.call(e)){var t=[e.source],r="";return e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.global&&(r+="g"),r&&t.push(r),t}},reconstruct:function(e){return e&&new RegExp(e[0],e[1])}}),"function"==typeof Set&&"function"==typeof Array.from&&f("Set",{deconstruct:function(e){if("[object Set]"===p.call(e))return Array.from(e)},reconstruct:function(e){if(!e)return new Set;e.forEach(this.add,this)}}),"function"==typeof Map&&"function"==typeof Array.from&&f("Map",{deconstruct:function(e){if("[object Map]"===p.call(e))return Array.from(e)},reconstruct:function(e){if(!e)return new Map;e.forEach((function(e){this.set(e[0],e[1])}),this)}})},7006:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(g)return;(0,u.protectFunc)((function(){switch(e.type){case"CONFIGURE_STREAM_LIMIT":d.configure(e.streams);break;case"ADD_EVENT":var t=function(t){var r=function(e,t){var r=[],n=null;for(;null!==(n=t.exec(e));)r.push(n);return t.lastIndex=0,r}(t,f);return r.reduce((function(t,r){return d.capture("inline-image",e.event.time,r[0].length)?t:t.replace(r[0],p)}),t)};switch(e.event.type){case"lr.browser.StyleContents":e.event.data.localContents&&(e.event.data.localContents=t(e.event.data.localContents)),e.event.data.snapshot&&(e.event.data.snapshot=t(e.event.data.snapshot));break;case"lr.browser.StyleChangeEvent":e.event.data.ruleText&&(e.event.data.ruleText=t(e.event.data.ruleText))}var r=i.default.encodeEventList([m.encodeEvent(e.event,{node:function(e){var r;if("elementInfo"in e&&null!==(r=e.elementInfo)&&void 0!==r&&r.attributes)for(var n=0,o=Object.keys(e.elementInfo.attributes);n<o.length;n++){var i=o[n],a=e.elementInfo.attributes[i];"string"==typeof a&&(e.elementInfo.attributes[i]=t(a))}},attr:function(e,r){return"string"==typeof r?t(r):r}})]),n={type:"EVENT",eventBuffer:r,recordingID:e.recordingID,sessionID:e.sessionID};if("lr.browser.MouseEvent"===e.event.type&&"CLICK"===e.event.data.eventType&&(n.isPendingClick=!0,n.processingID=e.event.data.processingID),r.byteLength>O||("lr.redux.ReduxAction"===e.event.type||"lr.redux.InitialState"===e.event.type)&&r.byteLength>b){var o;switch(e.event.type){case"lr.redux.InitialState":o="Redux state too large (> 4MB). Consider sanitizing: https://docs.logrocket.com/reference/redux-logging";break;case"lr.redux.ReduxAction":o="Redux action too large (> 4MB). Consider sanitizing: https://docs.logrocket.com/reference/redux-logging";break;case"lr.network.RequestEvent":o="Network request too large (> 10MB). Consider sanitizing: https://docs.logrocket.com/reference/network";break;case"lr.network.ResponseEvent":o="Network response too large (> 10MB). Consider sanitizing:  https://docs.logrocket.com/reference/network";break;default:o="Payload too large (> 10MB). Event: ".concat(e.event.type)}n.eventBuffer=i.default.encodeEventList([m.encodeEvent(c(c({},e.event),{},{type:"lr.error.Truncated",data:{reason:o}}))]),n.error=o,n.errorSource=e.event.type,g=!0}"lr.redux.InitialState"!==e.event.type&&"lr.redux.ReduxAction"!==e.event.type||(n.shouldWarnAboutReduxSize=!0),v(n);break;default:(0,s.default)(!1,"Worker received invalid event type: ".concat(JSON.stringify(e),"."))}}),(function(){g=!0;var t={};try{t=JSON.stringify(e)}catch(r){t=Object.prototype.toString.call(e)}return{appID:h,recordingID:y,payload:t}}))()};var o=n(r(8416)),i=n(r(5163)),a=r(1060),s=n(r(1143)),u=r(2952);r(5230);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f=/data:([\w+/(\w+)+]+)(;base64,([a-z0-9+/=]+)|,((%0A)?%3Csvg.*?%3C\/svg%3E))/gi,p="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==",d=new u.ThroughputStreamsLimiter({"inline-image":{maxPeriodSeconds:10,maxSingleItemBytes:2097152,maxThroughputBytes:2097152}});var h,y,m=new a.Encoder(i.default),g=!1,O=10444800,b=4177920,v=function(e){return postMessage(e)}},5230:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},5279:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(1390),o=new n.DiffPatcher({arrays:{detectMove:!1},propertyFilter:function(e,t){return"function"!=typeof t.left[e]&&"function"!=typeof t.right[e]&&!function(e){for(var t=0,r=e.parent;void 0!==r;){if(r.left===e.left&&r.right===e.right)return!0;if(r=r.parent,++t>10)return!0}return!1}(t)}});t.default=o},3237:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(5279)).default;t.default=o},3030:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(6690)),i=n(r(8416)),a=n(r(6604)),s=r(9064),u=r(2952);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){(0,i.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e){function t(e){switch(e.info){case"documentInfo":return c(c({},e),{},{documentInfo:c(c({},e.documentInfo),{},{childNodes:e.documentInfo.childNodes.map(t)})});case"documentFragmentInfo":return c(c({},e),{},{documentFragmentInfo:c(c({},e.documentFragmentInfo),{},{childNodes:e.documentFragmentInfo.childNodes.map(t)})});case"elementInfo":return c(c({},e),{},{elementInfo:c(c({},e.elementInfo),{},{attributes:(0,a.default)(e.elementInfo.attributes,u.deepDearsonify),childNodes:e.elementInfo.childNodes.map(t)})});default:return e}}switch(e.type){case"lr.browser.NodeInitEvent":return c(c({},e.data),{},{rootNode:t(e.data.rootNode)});case"lr.browser.ShadowInitEvent":return c(c({},e.data),{},{shadowRootNode:t(e.data.shadowRootNode)});case"lr.browser.NodeChangeEvent":return c(c({},e.data),{},{addedOrMoved:e.data.addedOrMoved.map((function(e){return c(c({},e),{},{nodeData:t(e.nodeData)})})),attributes:e.data.attributes.map((function(e){return c(c({},e),{},{attributes:(0,a.default)(e.attributes,u.deepDearsonify)})}))});case"lr.core.LogEvent":return c(c({},e.data),{},{args:e.data.args&&e.data.args.map(u.deepDearsonify)});case"lr.core.Exception":return c(c({},e.data),{},{message:(0,u.deepDearsonify)(e.data.message),messageArgs:(0,u.deepDearsonify)(e.data.messageArgs)});case"lr.redux.InitialState":return c(c({},e.data),{},{state:(0,u.deepDearsonify)(e.data.state)});case"lr.redux.ReduxAction":return c(c({},e.data),{},{action:(0,u.deepDearsonify)(e.data.action),stateDelta:null!=e.data.stateDelta?(0,u.deepDearsonify)(e.data.stateDelta):void 0,state:null!=e.data.state?(0,u.deepDearsonify)(e.data.state):void 0});case"lr.network.RequestEvent":case"lr.network.ResponseEvent":return c(c({},e.data),{},{body:(0,u.deepDearsonify)(e.data.body)});default:return e.data}}t.default=function e(t){var r=this;(0,o.default)(this,e),this.Protocol=void 0,this.decodeEvent=function(e){var t,n="Buffer"===(null===(t=e.data)||void 0===t?void 0:t.type)?Buffer.from(e.data.data):e.data,o=r.Protocol.decodeEvent(c(c({},e),{},{data:n}));return c(c({},e),{},{data:f(o),$SerializedLevel:s.SerializedLevel.Deserialized})},this.maybeDecodeEvent=function(e){return function(e){var t;return"Buffer"===(null===(t=e.data)||void 0===t?void 0:t.type)||e.data instanceof Buffer||"undefined"!=typeof Uint8Array&&e.data instanceof Uint8Array}(e)?r.decodeEvent(e):e},this.Protocol=t}},3225:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(6690)),i=n(r(9728)),a=n(r(8416)),s=n(r(6604)),u=n(r(3237)),l=(r(9064),r(2952));function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t){var r;function n(e){var r,o,i,a;return null==t||null===(r=t.node)||void 0===r||r.call(t,e),e.documentInfo?f(f({},e),{},{documentInfo:f(f({},e.documentInfo),{},{childNodes:null===(o=e.documentInfo.childNodes)||void 0===o?void 0:o.map(n)}),documentFragmentInfo:void 0,elementInfo:void 0}):e.documentFragmentInfo?f(f({},e),{},{documentFragmentInfo:f(f({},e.documentFragmentInfo),{},{childNodes:null===(i=e.documentFragmentInfo.childNodes)||void 0===i?void 0:i.map(n)}),documentInfo:void 0,elementInfo:void 0}):e.elementInfo?f(f({},e),{},{elementInfo:f(f({},e.elementInfo),{},{attributes:(0,s.default)(e.elementInfo.attributes,l.deepArsonify),childNodes:null===(a=e.elementInfo.childNodes)||void 0===a?void 0:a.map(n)}),documentFragmentInfo:void 0,documentInfo:void 0}):f(f({},e),{},{documentFragmentInfo:void 0,documentInfo:void 0,elementInfo:void 0})}switch(e.type){case"lr.browser.NodeInitEvent":return f(f({},e.data),{},{rootNode:n(e.data.rootNode)});case"lr.browser.ShadowInitEvent":return f(f({},e.data),{},{shadowRootNode:n(e.data.shadowRootNode)});case"lr.browser.NodeChangeEvent":return f(f({},e.data),{},{addedOrMoved:e.data.addedOrMoved.map((function(e){return f(f({},e),{},{nodeData:n(e.nodeData)})})),attributes:e.data.attributes.map((function(e){return f(f({},e),{},{attributes:(0,s.default)(e.attributes,(function(e,r){var n,o,i=null!==(n=null==t||null===(o=t.attr)||void 0===o?void 0:o.call(t,r,e))&&void 0!==n?n:e;return(0,l.deepArsonify)(i)}))})}))});case"lr.core.LogEvent":return f(f({},e.data),{},{args:null===(r=e.data.args)||void 0===r?void 0:r.map(l.deepArsonify)});case"lr.core.Exception":return f(f({},e.data),{},{message:(0,l.deepArsonify)(e.data.message),messageArgs:(0,l.deepArsonify)(e.data.messageArgs)});case"lr.redux.InitialState":return f(f({},e.data),{},{state:(0,l.deepArsonify)(e.data.state)});case"lr.redux.ReduxAction":return f(f({},e.data),{},{action:(0,l.deepArsonify)(e.data.action),stateDelta:null!=e.data.stateDelta?(0,l.deepArsonify)(e.data.stateDelta):void 0,state:null!=e.data.state?(0,l.deepArsonify)(e.data.state):void 0});case"lr.network.RequestEvent":case"lr.network.ResponseEvent":return f(f({},e.data),{},{body:(0,l.deepArsonify)(e.data.body)});default:return e.data}}var d=function(){function e(t){var r=this;(0,o.default)(this,e),this.Protocol=void 0,this.reduxStateByStoreId={},this.encodeEvent=function(e,t){if(0===e.seqID)for(var n in r.reduxStateByStoreId){if(Object.prototype.hasOwnProperty.call(r.reduxStateByStoreId,n))r.reduxStateByStoreId[n].sessionID===e.sessionID&&(r.reduxStateByStoreId[n].sessionID=void 0)}return f(f({},e),{},{data:r.Protocol.encodeEventData(e.type,r.processEventData(e,t))})},this.Protocol=t}return(0,i.default)(e,[{key:"processEventData",value:function(e,t){if("lr.redux.ReduxAction"===e.type){var r=this.reduxStateByStoreId[e.data.storeId],n=e.data;if(r){var o=u.default.diff({o:r.state},{o:e.data.stateDelta});r.sessionID!==e.sessionID&&(n.state=r.state),this.reduxStateByStoreId[e.data.storeId]={state:e.data.stateDelta,sessionID:e.sessionID},n.stateDelta=void 0!==o?o:null}else this.reduxStateByStoreId[e.data.storeId]={state:e.data.stateDelta,sessionID:e.sessionID},n.state=e.data.stateDelta,n.stateDelta=null;return p(f(f({},e),{},{data:n}),t)}return"lr.redux.InitialState"===e.type&&(this.reduxStateByStoreId[e.data.storeId]={state:e.data.state,sessionID:e.sessionID}),p(e,t)}}]),e}();t.default=d},1060:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Decoder",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"Encoder",{enumerable:!0,get:function(){return i.default}});var o=n(r(3030)),i=n(r(3225))},5818:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=Date.now.bind(Date),n=r(),o="undefined"!=typeof performance&&performance.now?performance.now.bind(performance):function(){return r()-n};t.default=o},7807:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DONE_POSTING=void 0;t.DONE_POSTING=-1},8316:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(861)),i=n(r(8698)),a=n(r(6690)),s=r(7807),u=function e(t){var r=t.worker,n=t.onReceiveFullMessage;(0,a.default)(this,e);var u=[];r.addEventListener("message",(function(e){if(e.data===s.DONE_POSTING){var t=[];u.forEach((function(e){e&&"object"===(0,i.default)(e)&&t.push(e)})),t.forEach((function(e){for(var t in e)e.hasOwnProperty(t)&&(e[t]=u[e[t]])})),u.length>0&&(n(u[0]),u=[])}else u=[].concat((0,o.default)(u),(0,o.default)(e.data))}))};t.default=u},4774:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(4687)),i=n(r(7424)),a=n(r(6690)),s=n(r(9728)),u=n(r(8698)),l=n(r(5818)),c=r(7807),f=n(r(9983));function p(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var h=2,y=4,m=8,g=function(e,t){var r=Object.getPrototypeOf(e);return null!==r&&"function"==typeof r[t]};function O(e){if(Array.isArray(e))return e.map((function(e){return"object"===(0,u.default)(e)&&null!==e?O(e):e}));for(var t={},r=0,n=Object.keys(e);r<n.length;r++){var o,i=n[r],a=e[i];null!==(o=Object.getOwnPropertyDescriptor(e,i))&&void 0!==o&&o.hasOwnProperty("get")?t[i]="[object Getter]":"object"===(0,u.default)(a)&&null!==a?t[i]=O(a):t[i]=a}return t}var b=function(){function e(t){var r=t.size,n=t.start;(0,a.default)(this,e),this.i=void 0,this.sum=void 0,this.size=void 0,this.values=void 0,this.i=1,this.sum=n,this.size=r,this.values=new Array(r),this.values[0]=n}return(0,s.default)(e,[{key:"add",value:function(e){var t=this.i++%this.size;this.sum-=this.values[t]||0,this.values[t]=e,this.sum+=e}},{key:"avg",value:function(){return this.sum/Math.min(this.size,this.i)}}]),e}(),v=function(){function e(t){var r=t.worker,n=t.isReactNative,o=void 0!==n&&n;(0,a.default)(this,e),this._worker=void 0,this._messages=void 0,this._byteCache=void 0,this._byteStreams=void 0,this._throughput=void 0,this._bytes=void 0,this._isProcessing=void 0,this._isReactNative=void 0,this._queue=void 0,this._doWork=void 0,this._worker=r,this._messages=[],this._byteCache={},this._byteStreams={},this._throughput=new b({size:10,start:1e3}),this._bytes=0,this._isProcessing=!1,this._isReactNative=o,this._queue=[],this._doWork=this.doWorkGenerator()}return(0,s.default)(e,[{key:"sendQueue",value:function(){try{this._worker.postMessage(this._queue)}catch(n){var e,t=p(this._queue);try{for(t.s();!(e=t.n()).done;){var r=e.value;try{this._worker.postMessage([r])}catch(e){this._worker.postMessage([{}.toString.call(r)])}}}catch(e){t.e(e)}finally{t.f()}}this._queue=[]}},{key:"doWorkGenerator",value:o.default.mark((function e(){var t,r=this;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=o.default.mark((function e(){var t,n,a,s,u,l,f,d,O,b,v,E,T,I,w,S,P;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r._messages.length){e.next=4;break}return e.next=3,-1;case 3:return e.abrupt("return","continue");case 4:if(t=new Map,n=[],a=0,s=function(e){var r=t.get(e);return void 0===r&&(r=n.length,t.set(e,r),n.push(e)),r},u=function e(t){if(null==t)return t;try{var n={}.toString.call(t);switch(n){case"[object String]":return a+=t.length*h,t.valueOf();case"[object Boolean]":return a+=y,t;case"[object Number]":case"[object Date]":return a+=m,t;case"[object Error]":case"[object DOMError]":case"[object DOMException]":var o="".concat(t.constructor.name,": ").concat(t.message||t.name);return a+=o.length*h,o;case"[object Object]":if(g(t,"toJS")&&g(t,"toJSON"))return e(t.toJSON());case"[object Array Iterator]":case"[object Array]":var i=Array.isArray(t)?[]:{};for(var u in t){var l;Object.prototype.hasOwnProperty.call(t,u)&&(a+=u.length*h,!r._isReactNative&&null!==(l=Object.getOwnPropertyDescriptor(t,u))&&void 0!==l&&l.hasOwnProperty("get")?i[u]=s("[object Getter]"):i[u]=s(t[u]))}return i;default:return n}}catch(e){return{}}},!(l=r._messages.shift())){e.next=44;break}O=(0,i.default)(l,2),b=O[0],v=O[1],E=null!==(f=null==b||null===(d=b.event)||void 0===d?void 0:d.type)&&void 0!==f?f:null==b?void 0:b.type,s(b),T=a,I=p(n),e.prev=16,I.s();case 18:if((w=I.n()).done){e.next=29;break}if(S=w.value,P=u(S),!(r._queue.length&&a-T>r._throughput.avg())){e.next=26;break}return r.sendQueue(),e.next=25,a-T;case 25:T=a;case 26:r._queue.push(P);case 27:e.next=18;break;case 29:e.next=34;break;case 31:e.prev=31,e.t0=e.catch(16),I.e(e.t0);case 34:return e.prev=34,I.f(),e.finish(34);case 37:return r._byteCache[E].add(a),r._queue.length>0&&r.sendQueue(),r._isReactNative&&r._worker.donePostingMessage?r._worker.donePostingMessage():r._worker.postMessage(c.DONE_POSTING),r._byteStreams[E]-=v,r._bytes-=v,e.next=44,a-T;case 44:case"end":return e.stop()}}),e,null,[[16,31,34,37]])}));case 1:return e.delegateYield(t(),"t0",3);case 3:if("continue"!==e.t0){e.next=6;break}return e.abrupt("continue",1);case 6:e.next=1;break;case 8:case"end":return e.stop()}}),e)}))},{key:"_processMessages",value:function(){var e=this;if(!this._isProcessing){this._isProcessing=!0;var t=(0,f.default)((function(){var r=0,n=(0,l.default)()+5;do{var o=e._doWork.next().value;if(!("number"==typeof o&&o>0))return void(e._isProcessing=!1);r+=o}while((0,l.default)()<n);e._throughput.add(r),setTimeout(t)}));setTimeout(t)}}},{key:"postMessage",value:function(e){var t,r,n=e;this._isReactNative&&(n=O(e));var o=null!==(t=null===(r=n.event)||void 0===r?void 0:r.type)&&void 0!==t?t:n.type;this._byteCache[o]||(this._byteCache[o]=new b({size:5,start:n.sizeOverride||0}));var i=this._byteCache[o].avg();n.sizeOverride&&(i=n.sizeOverride);var a=this._byteStreams[o]||0;return this._byteStreams[o]=a+i,this._bytes+=i,delete n.sizeOverride,this._messages.push([n,i]),this._processMessages(),[this._bytes,this._byteStreams]}}]),e}();t.default=v},4523:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"MessageSender",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"MessageReceiver",{enumerable:!0,get:function(){return i.default}});var o=n(r(4774)),i=n(r(8316))},9064:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(4322);Object.keys(n).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))}));var o=r(527);Object.keys(o).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))}))},4322:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValidEventNames=t.ValidRootNames=t.lr=t.SerializedLevel=t.requireValidEventType=t.isValidEventType=void 0;var r=function(e){return i.includes(e)};t.isValidEventType=r;var n,o;t.requireValidEventType=function(e){if(r(e))return e;throw new Error("The string '".concat(e,"' is not a valid event name."))},t.SerializedLevel=n,function(e){e[e.Binary=0]="Binary",e[e.Deserialized=1]="Deserialized",e[e.Serialized=2]="Serialized"}(n||(t.SerializedLevel=n={})),t.lr=o,function(e){var t,r,n,o,i,a,s,u,l,c,f,p,d,h,y,m,g,O,b,v,E,T,I,w,S,P,_,N,D,A,j,R,C,k,x,L,M,F,U,B,V,H,G,Y,J,W,q,K,z,X,Z,$,Q,ee,te,re,ne,oe,ie,ae,se,ue,le,ce,fe,pe,de,he,ye,me,ge,Oe,be,ve,Ee,Te,Ie,we,Se,Pe,_e,Ne,De,Ae,je,Re,Ce,ke,xe,Le,Me,Fe,Ue,Be,Ve,He,Ge,Ye,Je,We,qe,Ke,ze,Xe,Ze,$e,Qe,et,tt,rt,nt,ot,it,at,st,ut,lt,ct,ft,pt,dt,ht,yt,mt,gt,Ot,bt,vt,Et,Tt,It,wt,St,Pt,_t,Nt;!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.ANONYMOUS=1]="ANONYMOUS",e[e.IDENTIFIED=2]="IDENTIFIED"}(t||(t={})),e.IdentityStatus=t,r||(r=e.Activity||(e.Activity={})),o=n||(n=e.android||(e.android={})),a=i||(i=o.ActivityLifecycleEvent||(o.ActivityLifecycleEvent={})),function(e){e.ACTIVITY_CREATED="ACTIVITY_CREATED",e.ACTIVITY_STARTED="ACTIVITY_STARTED",e.ACTIVITY_RESUMED="ACTIVITY_RESUMED",e.ACTIVITY_PAUSED="ACTIVITY_PAUSED",e.ACTIVITY_STOPPED="ACTIVITY_STOPPED",e.ACTIVITY_SAVE_INSTANCE_STATE="ACTIVITY_SAVE_INSTANCE_STATE",e.ACTIVITY_DESTROYED="ACTIVITY_DESTROYED",e.PAGE_TAG="PAGE_TAG"}(s||(s={})),a.LifecycleType=s,l=u||(u=o.canvas||(o.canvas={})),function(e){e.NONE_ALIGN="NONE_ALIGN",e.ALIGN_CENTER="ALIGN_CENTER",e.ALIGN_LEFT="ALIGN_LEFT",e.ALIGN_RIGHT="ALIGN_RIGHT",e.ALIGN_JUSTIFY="ALIGN_JUSTIFY"}(c||(c={})),l.Align=c,function(e){e.BLEND_MODE_CLEAR="BLEND_MODE_CLEAR",e.BLEND_MODE_COLOR="BLEND_MODE_COLOR",e.BLEND_MODE_COLOR_BURN="BLEND_MODE_COLOR_BURN",e.BLEND_MODE_COLOR_DODGE="BLEND_MODE_COLOR_DODGE",e.BLEND_MODE_DARKEN="BLEND_MODE_DARKEN",e.BLEND_MODE_DIFFERENCE="BLEND_MODE_DIFFERENCE",e.BLEND_MODE_DST="BLEND_MODE_DST",e.BLEND_MODE_DST_ATOP="BLEND_MODE_DST_ATOP",e.BLEND_MODE_DST_IN="BLEND_MODE_DST_IN",e.BLEND_MODE_DST_OUT="BLEND_MODE_DST_OUT",e.BLEND_MODE_DST_OVER="BLEND_MODE_DST_OVER",e.BLEND_MODE_EXCLUSION="BLEND_MODE_EXCLUSION",e.BLEND_MODE_HARD_LIGHT="BLEND_MODE_HARD_LIGHT",e.BLEND_MODE_HUE="BLEND_MODE_HUE",e.BLEND_MODE_LIGHTEN="BLEND_MODE_LIGHTEN",e.BLEND_MODE_LUMINOSITY="BLEND_MODE_LUMINOSITY",e.BLEND_MODE_MODULATE="BLEND_MODE_MODULATE",e.BLEND_MODE_MULTIPLY="BLEND_MODE_MULTIPLY",e.BLEND_MODE_OVERLAY="BLEND_MODE_OVERLAY",e.BLEND_MODE_PLUS="BLEND_MODE_PLUS",e.BLEND_MODE_SATURATION="BLEND_MODE_SATURATION",e.BLEND_MODE_SCREEN="BLEND_MODE_SCREEN",e.BLEND_MODE_SOFT_LIGHT="BLEND_MODE_SOFT_LIGHT",e.BLEND_MODE_SRC="BLEND_MODE_SRC",e.BLEND_MODE_SRC_ATOP="BLEND_MODE_SRC_ATOP",e.BLEND_MODE_SRC_IN="BLEND_MODE_SRC_IN",e.BLEND_MODE_SRC_OUT="BLEND_MODE_SRC_OUT",e.BLEND_MODE_SRC_OVER="BLEND_MODE_SRC_OVER",e.BLEND_MODE_XOR="BLEND_MODE_XOR"}(f||(f={})),l.BlendMode=f,function(e){e.NONE_CAP="NONE_CAP",e.CAP_BUTT="CAP_BUTT",e.CAP_ROUND="CAP_ROUND",e.CAP_SQUARE="CAP_SQUARE"}(p||(p={})),l.Cap=p,function(e){e.NONE_COMPOSITE_TYPE="NONE_COMPOSITE_TYPE",e.ADD="ADD",e.CLEAR="CLEAR",e.COLOR="COLOR",e.COLOR_BURN="COLOR_BURN",e.COLOR_DODGE="COLOR_DODGE",e.DARKEN="DARKEN",e.DIFFERENCE="DIFFERENCE",e.DST="DST",e.DST_ATOP="DST_ATOP",e.DST_IN="DST_IN",e.DST_OUT="DST_OUT",e.DST_OVER="DST_OVER",e.EXCLUSION="EXCLUSION",e.HARD_LIGHT="HARD_LIGHT",e.HUE="HUE",e.LIGHTEN="LIGHTEN",e.LUMINOSITY="LUMINOSITY",e.MODULATE="MODULATE",e.MULTIPLY="MULTIPLY",e.OVERLAY="OVERLAY",e.PLUS="PLUS",e.SATURATION="SATURATION",e.SOFT_LIGHT="SOFT_LIGHT",e.SCREEN="SCREEN",e.SRC="SRC",e.SRC_ATOP="SRC_ATOP",e.SRC_IN="SRC_IN",e.SRC_OUT="SRC_OUT",e.SRC_OVER="SRC_OVER",e.XOR="XOR"}(d||(d={})),l.CompositeType=d,function(e){e.NONE_DEFAULT_TYPEFACE="NONE_DEFAULT_TYPEFACE",e.DEFAULT_TYPEFACE="DEFAULT_TYPEFACE",e.DEFAULT_TYPEFACE_BOLD="DEFAULT_TYPEFACE_BOLD",e.DEFAULT_TYPEFACE_MONOSPACE="DEFAULT_TYPEFACE_MONOSPACE",e.DEFAULT_TYPEFACE_SANS_SERIF="DEFAULT_TYPEFACE_SANS_SERIF",e.DEFAULT_TYPEFACE_SERIF="DEFAULT_TYPEFACE_SERIF"}(h||(h={})),l.DefaultTypeface=h,function(e){e.NONE_GRADIENT_TYPE="NONE_GRADIENT_TYPE",e.LINEAR_GRADIENT="LINEAR_GRADIENT",e.RADIAL_GRADIENT="RADIAL_GRADIENT",e.SWEEP_GRADIENT="SWEEP_GRADIENT"}(y||(y={})),l.GradientType=y,function(e){e.NONE_JOIN="NONE_JOIN",e.JOIN_BEVEL="JOIN_BEVEL",e.JOIN_MITER="JOIN_MITER",e.JOIN_ROUND="JOIN_ROUND"}(m||(m={})),l.Join=m,function(e){e.CLIP_OUT_RECT="CLIP_OUT_RECT",e.CLIP_PATH="CLIP_PATH",e.CLIP_RECT="CLIP_RECT",e.CLOSE_VIEW="CLOSE_VIEW",e.CONCAT="CONCAT",e.DRAW_ARGB="DRAW_ARGB",e.DRAW_ARC="DRAW_ARC",e.DRAW_BITMAP="DRAW_BITMAP",e.DRAW_BITMAP_MESH="DRAW_BITMAP_MESH",e.DRAW_CIRCLE="DRAW_CIRCLE",e.DRAW_COLOR="DRAW_COLOR",e.DRAW_DOUBLE_ROUND_RECT="DRAW_DOUBLE_ROUND_RECT",e.DRAW_LINE="DRAW_LINE",e.DRAW_LINES="DRAW_LINES",e.DRAW_OVAL="DRAW_OVAL",e.DRAW_PAINT="DRAW_PAINT",e.DRAW_PATCH="DRAW_PATCH",e.DRAW_PATH="DRAW_PATH",e.DRAW_PICTURE="DRAW_PICTURE",e.DRAW_POINT="DRAW_POINT",e.DRAW_POINTS="DRAW_POINTS",e.DRAW_POS_TEXT="DRAW_POS_TEXT",e.DRAW_RGB="DRAW_RGB",e.DRAW_RECT="DRAW_RECT",e.DRAW_RENDER_NODE="DRAW_RENDER_NODE",e.DRAW_ROUND_RECT="DRAW_ROUND_RECT",e.DRAW_TEXT="DRAW_TEXT",e.DRAW_TEXT_ON_PATH="DRAW_TEXT_ON_PATH",e.DRAW_TEXT_RUN="DRAW_TEXT_RUN",e.DRAW_VERTICES="DRAW_VERTICES",e.DISABLE_Z="DISABLE_Z",e.ENABLE_Z="ENABLE_Z",e.OPEN_VIEW="OPEN_VIEW",e.RESTORE="RESTORE",e.RESTORE_TO_COUNT="RESTORE_TO_COUNT",e.ROTATE="ROTATE",e.SAVE="SAVE",e.SAVE_LAYER="SAVE_LAYER",e.SAVE_LAYER_ALPHA="SAVE_LAYER_ALPHA",e.SCALE="SCALE",e.SET_BITMAP="SET_BITMAP",e.SET_DENSITY="SET_DENSITY",e.SET_DRAW_FILTER="SET_DRAW_FILTER",e.SET_MATRIX="SET_MATRIX",e.SKEW="SKEW",e.TRANSLATE="TRANSLATE",e.DRAW_PARAGRAPH="DRAW_PARAGRAPH"}(g||(g={})),l.OperationType=g,function(e){e.NONE_PAINT_STYLE="NONE_PAINT_STYLE",e.STYLE_FILL="STYLE_FILL",e.STYLE_FILL_AND_STROKE="STYLE_FILL_AND_STROKE",e.STYLE_STROKE="STYLE_STROKE"}(O||(O={})),l.PaintStyle=O,function(e){e.PORTER_DUFF_MODE_ADD="PORTER_DUFF_MODE_ADD",e.PORTER_DUFF_MODE_CLEAR="PORTER_DUFF_MODE_CLEAR",e.PORTER_DUFF_MODE_DARKEN="PORTER_DUFF_MODE_DARKEN",e.PORTER_DUFF_MODE_DST="PORTER_DUFF_MODE_DST",e.PORTER_DUFF_MODE_DST_OVER="PORTER_DUFF_MODE_DST_OVER",e.PORTER_DUFF_MODE_DST_IN="PORTER_DUFF_MODE_DST_IN",e.PORTER_DUFF_MODE_DST_OUT="PORTER_DUFF_MODE_DST_OUT",e.PORTER_DUFF_MODE_DST_ATOP="PORTER_DUFF_MODE_DST_ATOP",e.PORTER_DUFF_MODE_LIGHTEN="PORTER_DUFF_MODE_LIGHTEN",e.PORTER_DUFF_MODE_MULTIPLY="PORTER_DUFF_MODE_MULTIPLY",e.PORTER_DUFF_MODE_OVERLAY="PORTER_DUFF_MODE_OVERLAY",e.PORTER_DUFF_MODE_SCREEN="PORTER_DUFF_MODE_SCREEN",e.PORTER_DUFF_MODE_SRC="PORTER_DUFF_MODE_SRC",e.PORTER_DUFF_MODE_SRC_ATOP="PORTER_DUFF_MODE_SRC_ATOP",e.PORTER_DUFF_MODE_SRC_IN="PORTER_DUFF_MODE_SRC_IN",e.PORTER_DUFF_MODE_SRC_OUT="PORTER_DUFF_MODE_SRC_OUT",e.PORTER_DUFF_MODE_SRC_OVER="PORTER_DUFF_MODE_SRC_OVER",e.PORTER_DUFF_MODE_XOR="PORTER_DUFF_MODE_XOR"}(b||(b={})),l.PorterDuffMode=b,function(e){e.REGION_OP_DIFFERENCE="REGION_OP_DIFFERENCE",e.REGION_OP_INTERSECT="REGION_OP_INTERSECT",e.REGION_OP_REPLACE="REGION_OP_REPLACE",e.REGION_OP_REVERSE_DIFFERENCE="REGION_OP_REVERSE_DIFFERENCE",e.REGION_OP_UNION="REGION_OP_UNION",e.REGION_OP_XOR="REGION_OP_XOR"}(v||(v={})),l.RegionOp=v,function(e){e.NONE_TILE_MODE="NONE_TILE_MODE",e.TILE_MODE_CLAMP="TILE_MODE_CLAMP",e.TILE_MODE_REPEAT="TILE_MODE_REPEAT",e.TILE_MODE_MIRROR="TILE_MODE_MIRROR"}(E||(E={})),l.TileMode=E,function(e){e.NONE_TYPEFACE_STYLE="NONE_TYPEFACE_STYLE",e.STYLE_NORMAL="STYLE_NORMAL",e.STYLE_BOLD="STYLE_BOLD",e.STYLE_ITALIC="STYLE_ITALIC",e.STYLE_BOLD_ITALIC="STYLE_BOLD_ITALIC"}(T||(T={})),l.TypefaceStyle=T,I||(I=l.Operation||(l.Operation={})),function(e){var t,r,n,o;!function(e){e.ALIGN_CENTER="ALIGN_CENTER",e.ALIGN_LEFT="ALIGN_LEFT",e.ALIGN_RIGHT="ALIGN_RIGHT"}(t||(t={})),e.Align=t,function(e){e.CAP_BUTT="CAP_BUTT",e.CAP_ROUND="CAP_ROUND",e.CAP_SQUARE="CAP_SQUARE"}(r||(r={})),e.Cap=r,function(e){e.JOIN_BEVEL="JOIN_BEVEL",e.JOIN_MITER="JOIN_MITER",e.JOIN_ROUND="JOIN_ROUND"}(n||(n={})),e.Join=n,function(e){e.STYLE_FILL="STYLE_FILL",e.STYLE_FILL_AND_STROKE="STYLE_FILL_AND_STROKE",e.STYLE_STROKE="STYLE_STROKE"}(o||(o={})),e.Style=o}(w||(w=l.Paint||(l.Paint={}))),function(e){var t;!function(e){e.TILE_MODE_CLAMP="TILE_MODE_CLAMP",e.TILE_MODE_REPEAT="TILE_MODE_REPEAT",e.TILE_MODE_MIRROR="TILE_MODE_MIRROR"}(t||(t={})),e.TileMode=t}(S||(S=l.Shader||(l.Shader={}))),_=P||(P=l.Snapshot||(l.Snapshot={})),function(e){e.REDACTION_MODE_DISABLED="REDACTION_MODE_DISABLED",e.REDACTION_MODE_BLOCKED="REDACTION_MODE_BLOCKED"}(N||(N={})),_.RedactionMode=N,function(e){var t,r;!function(e){e.DEFAULT_TYPEFACE="DEFAULT_TYPEFACE",e.DEFAULT_TYPEFACE_BOLD="DEFAULT_TYPEFACE_BOLD",e.DEFAULT_TYPEFACE_MONOSPACE="DEFAULT_TYPEFACE_MONOSPACE",e.DEFAULT_TYPEFACE_SANS_SERIF="DEFAULT_TYPEFACE_SANS_SERIF",e.DEFAULT_TYPEFACE_SERIF="DEFAULT_TYPEFACE_SERIF"}(t||(t={})),e.DefaultTypeface=t,function(e){e.STYLE_NORMAL="STYLE_NORMAL",e.STYLE_BOLD="STYLE_BOLD",e.STYLE_ITALIC="STYLE_ITALIC",e.STYLE_BOLD_ITALIC="STYLE_BOLD_ITALIC"}(r||(r={})),e.Style=r}(D||(D=l.Typeface||(l.Typeface={}))),A||(A=o.ResourceInitializationEvent||(o.ResourceInitializationEvent={})),R=j||(j=o.TouchEvent||(o.TouchEvent={})),function(e){e.DOWN="DOWN",e.UP="UP",e.MOVE="MOVE"}(C||(C={})),R.MotionType=C,x=k||(k=e.AppFramework||(e.AppFramework={})),function(e){e.NATIVE_MOBILE="NATIVE_MOBILE",e.REACT_NATIVE="REACT_NATIVE",e.JETPACK_COMPOSE="JETPACK_COMPOSE",e.SWIFT_UI="SWIFT_UI",e.SHOPIFY="SHOPIFY",e.FLUTTER="FLUTTER",e.REACT_NATIVE_NEW_ARCH="REACT_NATIVE_NEW_ARCH"}(L||(L={})),x.FrameworkType=L,F=M||(M=e.browser||(e.browser={})),B=U||(U=F.MouseEvent||(F.MouseEvent={})),function(e){e.MOUSEOVER="MOUSEOVER",e.MOUSEOUT="MOUSEOUT",e.MOUSEUP="MOUSEUP",e.MOUSEDOWN="MOUSEDOWN",e.MOUSELEAVE="MOUSELEAVE",e.MOUSEENTER="MOUSEENTER",e.DRAGSTART="DRAGSTART",e.DRAGEND="DRAGEND",e.DRAGLEAVE="DRAGLEAVE",e.CLICK="CLICK",e.CONTEXTMENU="CONTEXTMENU",e.DBLCLICK="DBLCLICK",e.DROP="DROP",e.MOUSEMOVE="MOUSEMOVE",e.DRAGOVER="DRAGOVER",e.DRAGENTER="DRAGENTER",e.DRAG="DRAG",e.FOCUS="FOCUS",e.BLUR="BLUR",e.TOUCHSTART="TOUCHSTART",e.TOUCHMOVE="TOUCHMOVE",e.TOUCHEND="TOUCHEND",e.MOUSEHOVER="MOUSEHOVER"}(V||(V={})),B.EventType=V,G=H||(H=F.NavigationEvent||(F.NavigationEvent={})),function(e){e.PAGE_LOAD="PAGE_LOAD",e.POP_STATE="POP_STATE",e.PUSH_STATE="PUSH_STATE",e.REPLACE_STATE="REPLACE_STATE"}(Y||(Y={})),G.NavigationType=Y,W=J||(J=F.Node||(F.Node={})),q||(q=W.TextInfo||(W.TextInfo={})),z=K||(K=F.NodeChangeEvent||(F.NodeChangeEvent={})),X||(X=z.TextEvent||(z.TextEvent={})),$=Z||(Z=F.NPSEvent||(F.NPSEvent={})),function(e){e.WOOTRIC="WOOTRIC",e.DELIGHTED="DELIGHTED"}(Q||(Q={})),$.NPSProvider=Q,ee||(ee=F.PIIExposureEvent||(F.PIIExposureEvent={})),function(e){var t;!function(e){e.INSERT_RULE="INSERT_RULE",e.DELETE_RULE="DELETE_RULE",e.SET_PROPERTY="SET_PROPERTY",e.REMOVE_PROPERTY="REMOVE_PROPERTY"}(t||(t={})),e.EventType=t}(te||(te=F.StyleChangeEvent||(F.StyleChangeEvent={}))),re||(re=e.Buffer||(e.Buffer={})),oe=ne||(ne=e.clickhouse||(e.clickhouse={})),ae=ie||(ie=oe.IssueDescription||(oe.IssueDescription={})),function(e){e.EXCEPTION="EXCEPTION",e.NETWORK_ERROR="NETWORK_ERROR",e.RAGE_CLICK="RAGE_CLICK",e.DEAD_CLICK="DEAD_CLICK",e.FRUSTRATING_NETWORK="FRUSTRATING_NETWORK",e.ERROR_STATE="ERROR_STATE",e.DEAD_CLICK_V2="DEAD_CLICK_V2"}(se||(se={})),ae.IssueType=se,function(e){var t;!function(e){e.EXCEPTION="EXCEPTION",e.NETWORK_ERROR="NETWORK_ERROR",e.RAGE_CLICK="RAGE_CLICK",e.DEAD_CLICK="DEAD_CLICK",e.FRUSTRATING_NETWORK="FRUSTRATING_NETWORK",e.ERROR_STATE="ERROR_STATE",e.DEAD_CLICK_V2="DEAD_CLICK_V2"}(t||(t={})),e.IssueType=t}(ue||(ue=oe.IssueScore||(oe.IssueScore={}))),function(e){var t,r,n,o;!function(e){e.UNKNOWN="UNKNOWN",e.EXCEPTION="EXCEPTION",e.CRASH_REPORT="CRASH_REPORT"}(t||(t={})),e.IssueType=t,n=r||(r=e.SymbolicatorResponseFrame||(e.SymbolicatorResponseFrame={})),function(e){e.UNKNOWN="UNKNOWN",e.symbolicated="symbolicated",e.missing_symbol="missing_symbol",e.unknown_image="unknown_image",e.missing="missing",e.malformed="malformed"}(o||(o={})),n.SymbolicatorStatus=o}(le||(le=oe.StackTrace||(oe.StackTrace={}))),fe=ce||(ce=e.core||(e.core={})),de=pe||(pe=fe.Exception||(fe.Exception={})),function(e){e.UNHANDLED_REJECTION="UNHANDLED_REJECTION",e.WINDOW="WINDOW",e.MESSAGE="MESSAGE",e.CONSOLE="CONSOLE",e.ANDROID="ANDROID",e.IOS="IOS"}(he||(he={})),de.ExceptionType=he,me=ye||(ye=de.DebugModules||(de.DebugModules={})),function(e){e.macho="macho",e.pe="pe",e.elf="elf"}(ge||(ge={})),me.ModuleType=ge,be=Oe||(Oe=fe.LogEvent||(fe.LogEvent={})),function(e){e.DEBUG="DEBUG",e.INFO="INFO",e.LOG="LOG",e.WARN="WARN",e.ERROR="ERROR"}(ve||(ve={})),be.LogLevel=ve,Ee||(Ee=e.CustomEvent||(e.CustomEvent={})),Te||(Te=e.error||(e.error={})),we=Ie||(Ie=e.Event||(e.Event={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.WEB=1]="WEB",e[e.ANDROID=2]="ANDROID",e[e.IOS=3]="IOS"}(Se||(Se={})),we.PlatformType=Se,_e=Pe||(Pe=e.feedback||(e.feedback={})),De=Ne||(Ne=_e.FeedbackAction||(_e.FeedbackAction={})),function(e){e.TEXT="TEXT",e.RATING="RATING"}(Ae||(Ae={})),De.ResponseType=Ae,je||(je=e.filter||(e.filter={})),Re||(Re=e.guide||(e.guide={})),function(e){var t,r;!function(e){var t;!function(e){e.VIEW_APPEARED="VIEW_APPEARED",e.VIEW_DISAPPEARED="VIEW_DISAPPEARED",e.PAGE_TAG="PAGE_TAG"}(t||(t={})),e.LifecycleType=t}(t||(t=e.LifecycleEvent||(e.LifecycleEvent={}))),function(e){var t;!function(e){e.DOWN="DOWN",e.UP="UP",e.MOVE="MOVE"}(t||(t={})),e.MotionType=t}(r||(r=e.TouchEvent||(e.TouchEvent={})))}(Ce||(Ce=e.ios||(e.ios={}))),xe=ke||(ke=e.Metadata||(e.Metadata={})),function(e){e.UNKNOWN="UNKNOWN",e.DOM="DOM",e.SKIA="SKIA",e.PDF="PDF",e.PIXEL="PIXEL"}(Le||(Le={})),xe.ReplayType=Le,Fe=Me||(Me=xe.DeviceInfo||(xe.DeviceInfo={})),function(e){e.ANDROID="ANDROID",e.IOS="IOS"}(Ue||(Ue={})),Fe.DeviceType=Ue,Be||(Be=xe.SDKState||(xe.SDKState={})),He=Ve||(Ve=e.metrics||(e.metrics={})),Ye=Ge||(Ge=He.Metric||(He.Metric={})),function(e){e.timeToFirstByte="timeToFirstByte",e.largestContentfulPaintTime="largestContentfulPaintTime",e.initialPageLoadTime="initialPageLoadTime",e.firstInputDelay="firstInputDelay",e.cumulativeLayoutShift="cumulativeLayoutShift",e.mobileFrameRenderTime="mobileFrameRenderTime"}(Je||(Je={})),Ye.MetricType=Je,qe=We||(We=e.mobile||(e.mobile={})),ze=Ke||(Ke=qe.ImageNonCaptureEvent||(qe.ImageNonCaptureEvent={})),function(e){e.single="single",e.total="total"}(Xe||(Xe={})),ze.CaptureLimitType=Xe,function(e){var t;!function(e){e.macho="macho",e.pe="pe",e.elf="elf"}(t||(t={})),e.ModuleType=t}(Ze||(Ze=qe.SymbolicationModules||(qe.SymbolicationModules={}))),Qe=$e||($e=e.network||(e.network={})),function(e){e.GET="GET",e.HEAD="HEAD",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE",e.CONNECT="CONNECT",e.OPTIONS="OPTIONS",e.TRACE="TRACE",e.PATCH="PATCH"}(et||(et={})),Qe.MethodType=et,rt=tt||(tt=Qe.NetworkStatusEvent||(Qe.NetworkStatusEvent={})),function(e){e.UNKNOWN="UNKNOWN",e.NONE="NONE",e.SLOW2G="SLOW2G",e.TWOG="TWOG",e.THREEG="THREEG",e.FOURG="FOURG"}(nt||(nt={})),rt.EffectiveType=nt,it=ot||(ot=Qe.PerfResourceEvent||(Qe.PerfResourceEvent={})),function(e){e.LINK="LINK",e.SCRIPT="SCRIPT",e.CSS="CSS",e.IMG="IMG",e.IMAGE="IMAGE",e.OTHER="OTHER",e.NAVIGATION="NAVIGATION",e.TRACK="TRACK",e.VIDEO="VIDEO"}(at||(at={})),it.InitiatorType=at,function(e){var t;!function(e){e.DEFAULT="DEFAULT",e.BASIC="BASIC",e.CORS="CORS",e.ERROR="ERROR",e.OPAQUE="OPAQUE",e.OPAQUEREDIRECT="OPAQUEREDIRECT"}(t||(t={})),e.ResponseType=t}(st||(st=Qe.ResponseEvent||(Qe.ResponseEvent={}))),lt=ut||(ut=e.performance||(e.performance={})),ft=ct||(ct=lt.AppStartTiming||(lt.AppStartTiming={})),function(e){e.APP_COLD_START="APP_COLD_START",e.APP_WARM_START="APP_WARM_START",e.APP_HOT_START="APP_HOT_START"}(pt||(pt={})),ft.AppStartType=pt,ht=dt||(dt=ft.Measurement||(ft.Measurement={})),function(e){e.APPLICATION_INITIALIZED="APPLICATION_INITIALIZED",e.ACTIVITY_STARTED="ACTIVITY_STARTED",e.ACTIVITY_RESUMED="ACTIVITY_RESUMED",e.CONTENT_PROVIDER_CREATED="CONTENT_PROVIDER_CREATED",e.ACTIVITY_CREATED="ACTIVITY_CREATED",e.APPLICATION_RUNTIME_INITIALIZED="APPLICATION_RUNTIME_INITIALIZED",e.DID_FINISH_LAUNCHING="DID_FINISH_LAUNCHING",e.WINDOW_DID_BECOME_VISIBLE="WINDOW_DID_BECOME_VISIBLE"}(yt||(yt={})),ht.MeasurementType=yt,mt||(mt=lt.BusyFrames||(lt.BusyFrames={})),gt||(gt=lt.CpuUsage||(lt.CpuUsage={})),bt=Ot||(Ot=lt.LongTasks||(lt.LongTasks={})),vt||(vt=bt.LongTask||(bt.LongTask={})),Et||(Et=lt.Memory||(lt.Memory={})),Tt||(Tt=lt.NetworkThroughput||(lt.NetworkThroughput={})),It||(It=e.redux||(e.redux={})),function(e){var t;!function(e){e.GET="GET",e.HEAD="HEAD",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE",e.CONNECT="CONNECT",e.OPTIONS="OPTIONS",e.TRACE="TRACE",e.PATCH="PATCH"}(t||(t={})),e.MethodType=t}(wt||(wt=e.sentry||(e.sentry={}))),Pt=St||(St=e.settings||(e.settings={})),_t||(_t=Pt.RecordingCondition||(Pt.RecordingCondition={})),Nt||(Nt=e.utils||(e.utils={}))}(o||(t.lr=o={}));t.ValidRootNames=["lr.Event","lr.EventList","lr.PubSub"];var i=["lr.Activate","lr.Activity","lr.android.ActivityLifecycleEvent","lr.android.FlatViewCapture","lr.android.InputChangeEvent","lr.android.Log","lr.android.NetworkStatusEvent","lr.android.ResourceInitializationEvent","lr.android.RootViewSnapshots","lr.android.TouchEvent","lr.android.ViewCapture","lr.android.ViewSnapshot","lr.AppFramework","lr.browser.AdoptedStyleSheetsEvent","lr.browser.ConstructedStyleSheetEvent","lr.browser.DeadClick","lr.browser.FrustratingNetworkEvent","lr.browser.IframeInitEvent","lr.browser.InputChangeEvent","lr.browser.InputEvent","lr.browser.KeypressEvent","lr.browser.LoadEvent","lr.browser.MouseEvent","lr.browser.NavigationEvent","lr.browser.NodeChangeEvent","lr.browser.NodeInitEvent","lr.browser.NPSEvent","lr.browser.PIIExposureEvent","lr.browser.ScrollEvent","lr.browser.ShadowInitEvent","lr.browser.StyleChangeEvent","lr.browser.StyleContents","lr.browser.StyleSheetDisabledEvent","lr.browser.UnloadEvent","lr.browser.UTMParamsEvent","lr.browser.ViewportResizeEvent","lr.Buffer","lr.clickhouse.CrashReport","lr.clickhouse.ExtendSessionRetention","lr.clickhouse.IssueDescription","lr.clickhouse.IssueScore","lr.clickhouse.RemoveSession","lr.clickhouse.ScreenshotMetadata","lr.clickhouse.ServerIdentify","lr.clickhouse.SessionArchived","lr.clickhouse.SessionExported","lr.clickhouse.SessionRetentionDelete","lr.clickhouse.StackTrace","lr.core.Exception","lr.core.LogEvent","lr.core.OldException","lr.core.PageTitleChange","lr.ConditionalRecordingConfirmation","lr.CustomEvent","lr.DebugLog","lr.error.Truncated","lr.feedback.FeedbackResponse","lr.feedback.RatingResponse","lr.filter.ErrorState","lr.filter.Match","lr.filter.VisibleElement","lr.guide.GuideStepEnd","lr.guide.GuideStepStart","lr.Identify","lr.InitialPageLoadMetrics","lr.IntercomShow","lr.ios.InputChangeEvent","lr.ios.LifecycleEvent","lr.ios.PDFViewCapture","lr.ios.TouchEvent","lr.Metadata","lr.metrics.Measurement","lr.metrics.Metric","lr.mobile.CustomFonts","lr.mobile.ImageNonCaptureEvent","lr.mobile.SymbolicationModules","lr.mobile.WebViewInitEvent","lr.network.NetworkStatusEvent","lr.network.PerfResourceEvent","lr.network.RequestEvent","lr.network.ResponseEvent","lr.performance.AppStartTiming","lr.performance.BusyFrames","lr.performance.CpuUsage","lr.performance.FirstInputDelay","lr.performance.LongTasks","lr.performance.Memory","lr.performance.NetworkThroughput","lr.PreviousSession","lr.redux.InitialState","lr.redux.ReduxAction","lr.sentry.Exception","lr.sentry.NetworkResponse","lr.ZendeskShow"];t.ValidEventNames=i},527:function(e,t,r){"use strict";r(4322)},5163:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0});var o={};t.default=void 0;var i=n(r(9402)),a=r(8345),s=r(9064);Object.keys(s).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(o,e)||e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))}));var u=(0,a.createWriteInterface)((function(e){return i.default.lookupType(e)}));t.default=u},9402:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(6739)),i={lookupType:function(e){return e.split(".").reduce((function(e,t){return e[t]}),o.default)}};t.default=i},8345:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.createInterface=t.createReadInterface=t.createWriteInterface=void 0;var o=n(r(8416)),i=r(9064);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var u=function(e){return{encodeEventData:function(t,r){var n=e(t);return n.encode(n.fromObject(r)).finish()},encodeEventContainer:function(t){return e("lr.Event").encode(t).finish()},encodeEventList:function(t){return e("lr.EventList").encode({events:t}).finish()},encodePubSub:function(t){return e("lr.PubSub").encode(t).finish()}}};t.createWriteInterface=u;var l=function(e){var t=function(e){return"string"==typeof e?Buffer.from(e,"base64"):e},r=function(r,n){var o=e(r);return o.toObject(o.decode(t(n)),{enums:Number,defaults:!0})},n=function(r,n){var o=e(r);return o.toObject(o.decode(t(n)),{enums:String,defaults:!0,oneofs:!0})};return{lookupType:e,decodeEvent:function(e){return s(s({},e),{},{data:this.decodeEventData(e.type,e.data),$SerializedLevel:i.SerializedLevel.Serialized})},decodeEventData:function(e,t){return n(e,t)},decodeEventContainer:function(e){return r("lr.Event",e)},decodeEventList:function(e){return r("lr.EventList",e)},decodePubSub:function(e){return r("lr.PubSub",e)},decodeFilter:function(e){return n("lr.filter.Filter",e)},decodeGuide:function(e){return n("lr.guide.Guide",e)},decodeAppVersionSettings:function(e){return n("lr.settings.AppVersionSettings",e)},decodeIPSettings:function(e){return n("lr.settings.IPSettings",e)},decodeLocationSettings:function(e){return n("lr.settings.LocationSettings",e)},decodeRecordingCondition:function(e){return n("lr.settings.RecordingCondition",e)},decodeUserAgentSettings:function(e){return n("lr.settings.UserAgent",e)}}};t.createReadInterface=l;t.createInterface=function(e){return s(s({},l(e)),u(e))}},4393:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.ThroughputStreamsLimiter=t.ThroughputLimiter=void 0;var o=n(r(6690)),i=n(r(9728)),a=n(r(1657)),s=function(){function e(t){var r=t.maxPeriodSeconds,n=t.maxSingleItemBytes,i=t.maxThroughputBytes;(0,o.default)(this,e),this.buckets=[],this.droppedBytes=0,this.maxPeriodSeconds=void 0,this.maxSingleItemBytes=void 0,this.maxThroughputBytes=void 0,this.maxPeriodSeconds=r,this.maxSingleItemBytes=n,this.maxThroughputBytes=i}return(0,i.default)(e,[{key:"configure",value:function(e){var t=e.maxPeriodSeconds,r=e.maxSingleItemBytes,n=e.maxThroughputBytes;this.maxPeriodSeconds=null!=t?t:this.maxPeriodSeconds,this.maxSingleItemBytes=null!=r?r:this.maxSingleItemBytes,this.maxThroughputBytes=null!=n?n:this.maxThroughputBytes}},{key:"currentThroughput",get:function(){var e=this.timeSpan;return 0===e?0:this.totalBytes/e}},{key:"timeSpan",get:function(){if(0===this.buckets.length)return 0;var e=this.buckets[0].time;return this.buckets[this.buckets.length-1].time-e+1}},{key:"totalBytes",get:function(){return this.buckets.reduce((function(e,t){return e+t.bytes}),0)}},{key:"capture",value:function(e,t){for(var r=Math.floor(e/1e3),n=r-this.maxPeriodSeconds;this.buckets.length>0&&this.buckets[0].time<=n;)this.buckets.shift();0!==this.buckets.length&&this.buckets[this.buckets.length-1].time===r||this.buckets.push({time:r,bytes:0});var o=t<=this.maxSingleItemBytes&&this.currentThroughput<=this.maxThroughputBytes;return o?this.buckets[this.buckets.length-1].bytes+=t:this.droppedBytes+=t,o}}]),e}();t.ThroughputLimiter=s;var u=function(){function e(t){(0,o.default)(this,e),this.streams=void 0,this.streams=(0,a.default)(t,(function(e){return new s(e)}))}return(0,i.default)(e,[{key:"configure",value:function(e){for(var t=0,r=Object.keys(e);t<r.length;t++){var n=r[t];this.isValidStream(n)&&this.streams[n].configure(e[n])}}},{key:"capture",value:function(e,t,r){return this.streams[e].capture(t,r)}},{key:"currentThroughput",get:function(){return(0,a.default)(this.streams,(function(e){return e.currentThroughput}))}},{key:"isValidStream",value:function(e){return e in this.streams}}]),e}();t.ThroughputStreamsLimiter=u},8668:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={collectWindowErrors:!0,debug:!1},o="undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{},i=[].slice,a="?",s=/^(?:Uncaught (?:exception: )?)?((?:Eval|Internal|Range|Reference|Syntax|Type|URI)Error): ?(.*)$/;function u(){return"undefined"==typeof document||void 0===document.location?"":document.location.href}n.report=function(){var e,t,r=[],l=null,c=null,f=null;function p(e,t){var o=null;if(!t||n.collectWindowErrors){for(var a in r)if(r.hasOwnProperty(a))try{r[a].apply(null,[e].concat(i.call(arguments,2)))}catch(e){o=e}if(o)throw o}}function d(t,r,o,i,l){if(f)n.computeStackTrace.augmentStackTraceWithInitialElement(f,r,o,t),h();else if(l)p(n.computeStackTrace(l),!0);else{var c,d={url:r,line:o,column:i},y=void 0,m=t;if("[object String]"==={}.toString.call(t))(c=t.match(s))&&(y=c[1],m=c[2]);d.func=a,p({name:y,message:m,url:u(),stack:[d]},!0)}return!!e&&e.apply(this,arguments)}function h(){var e=f,t=l;l=null,f=null,c=null,p.apply(null,[e,!1].concat(t))}function y(e,t){var r=i.call(arguments,1);if(f){if(c===e)return;h()}var o=n.computeStackTrace(e);if(f=o,c=e,l=r,setTimeout((function(){c===e&&h()}),o.incomplete?2e3:0),!1!==t)throw e}return y.subscribe=function(n){!function(){if(t)return;e=o.onerror,o.onerror=d,t=!0}(),r.push(n)},y.unsubscribe=function(e){for(var t=r.length-1;t>=0;--t)r[t]===e&&r.splice(t,1)},y.uninstall=function(){!function(){if(!t)return;o.onerror=e,t=!1,e=void 0}(),r=[]},y}(),n.computeStackTrace=function(){function e(e){if(void 0!==e.stack&&e.stack){for(var t,r,n=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|<anonymous>).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,o=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|resource|\[native).*?)(?::(\d+))?(?::(\d+))?\s*$/i,i=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,s=e.stack.split("\n"),l=[],c=(/^(.*) is undefined$/.exec(e.message),0),f=s.length;c<f;++c){if(t=n.exec(s[c])){var p=t[2]&&-1!==t[2].indexOf("native");r={url:p?null:t[2],func:t[1]||a,args:p?[t[2]]:[],line:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}else if(t=i.exec(s[c]))r={url:t[2],func:t[1]||a,args:[],line:+t[3],column:t[4]?+t[4]:null};else{if(!(t=o.exec(s[c])))continue;r={url:t[3],func:t[1]||a,args:t[2]?t[2].split(","):[],line:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}!r.func&&r.line&&(r.func=a),l.push(r)}return l.length?(l[0].column||void 0===e.columnNumber||(l[0].column=e.columnNumber+1),{name:e.name,message:e.message,url:u(),stack:l}):null}}function t(e,t,r,n){var o={url:t,line:r};if(o.url&&o.line){if(e.incomplete=!1,o.func||(o.func=a),e.stack.length>0&&e.stack[0].url===o.url){if(e.stack[0].line===o.line)return!1;if(!e.stack[0].line&&e.stack[0].func===o.func)return e.stack[0].line=o.line,!1}return e.stack.unshift(o),e.partial=!0,!0}return e.incomplete=!0,!1}function r(e,i){for(var s,l,c=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,f=[],p={},d=!1,h=r.caller;h&&!d;h=h.caller)if(h!==o&&h!==n.report){if(l={url:null,func:a,line:null,column:null},h.name?l.func=h.name:(s=c.exec(h.toString()))&&(l.func=s[1]),void 0===l.func)try{l.func=s.input.substring(0,s.input.indexOf("{"))}catch(e){}p[""+h]?d=!0:p[""+h]=!0,f.push(l)}i&&f.splice(0,i);var y={name:e.name,message:e.message,url:u(),stack:f};return t(y,e.sourceURL||e.fileName,e.line||e.lineNumber,e.message||e.description),y}function o(t,o){var i=null;o=null==o?0:+o;try{if(i=e(t))return i}catch(e){if(n.debug)throw e}try{if(i=r(t,o+1))return i}catch(e){if(n.debug)throw e}return{name:t.name,message:t.message,url:u()}}return o.augmentStackTraceWithInitialElement=t,o.computeStackTraceFromStackProp=e,o}();var l=n;t.default=l},1842:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document,o=new n.Handler(t);return r.addEventListener(e,(function(){for(var e,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];null===(e=o.get())||void 0===e||e.apply(void 0,r)}),{capture:!0,passive:!0}),(0,n.createUnsubListener)(o)};var n=r(6836)},6633:function(e,t){"use strict";function r(e,t){var r=t;return t&&e.toLowerCase().replace(/\s/g,"").indexOf("macos")>-1&&(r=t.replace("10.15.7","10.15.7+")),r}Object.defineProperty(t,"__esModule",{value:!0}),t.adjustOsVersion=r,t.adjustOs=function(e){return r(e,e)}},8382:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,r=e.logger,n=e.url,o=void 0===n?"":n,i=(r.getConfig("lr.browser")||{}).urlSanitizer,a=void 0===i?function(e){return e}:i;try{t=a(o)}catch(e){console.error(e)}if("string"==typeof t)return t;return""}},5632:function(e,t){"use strict";function r(e){return"#document-fragment"===e.nodeName&&"open"===e.mode?"$shadow-root":e.nodeName?e.nodeName.toLowerCase():""}Object.defineProperty(t,"__esModule",{value:!0}),t.getNodeName=r,t.getTargetForEvent=function(e){if("function"==typeof e.composedPath&&e.composed)return e.composedPath()[0];if(e.path)return e.path[0];return e.target},t.getNodePath=function(e){var t=e,o=[],i=0;for(;t&&(t.parentNode||t.host);){var a=r(t);if("body"===a)break;var s={nodeName:a},u=s.nodeName.length;if(t.id&&t.id.length>0&&(s.id=t.id,u+=t.id.length),t.classList&&t.classList.length>0){s.classList=Array.prototype.slice.call(t.classList,0);var l=Array.prototype.join.call(t.classList,"");l&&(u+=l.length)}if(i+u>n)break;if(t.parentNode&&t.parentNode.children)try{var c=0,f=t;do{c+=1,f=f.previousElementSibling}while(f);s.nthChild=c}catch(e){}i+=u,o.push(s),t=t.parentNode||t.host}return o};var n=1e3},9790:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.ES_ISSUE_TYPE_BASE_FILTER=t.ISSUE_GROUP_TYPE=t.ISSUE_TYPE=void 0;var o,i=n(r(8416)),a={EXCEPTION:"EXCEPTION",NETWORK_ERROR:"NETWORK_ERROR",RAGE_CLICK:"RAGE_CLICK",DEAD_CLICK:"DEAD_CLICK",DEAD_CLICK_V2:"DEAD_CLICK_V2",FRUSTRATING_NETWORK:"FRUSTRATING_NETWORK",ERROR_STATE:"ERROR_STATE",CRASH_REPORT:"CRASH_REPORT"};t.ISSUE_TYPE=a;t.ISSUE_GROUP_TYPE={ENCODED_FILTER:"ENCODED_FILTER",SENTRY:"SENTRY",TROYTOWN:"TROYTOWN"};var s=(o={},(0,i.default)(o,a.RAGE_CLICK,{rageClicked:{operator:"TEXT_IS",strings:[]}}),(0,i.default)(o,a.DEAD_CLICK,{deadClicked:{operator:"TEXT_IS",strings:[]}}),o);t.ES_ISSUE_TYPE_BASE_FILTER=s},2777:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.LOG_FILTER_TYPES=t.LOG_TYPES=void 0;var o,i=n(r(8416)),a={LOG:"LOG",WARN:"WARN",ERROR:"ERROR",DEBUG:"DEBUG",INFO:"INFO",REDUX:"REDUX",LIFECYCLE:"LIFECYCLE",NAVIGATION:"NAVIGATION",NETWORK:"NETWORK",PREV_SESSION:"PREV_SESSION",NEXT_SESSION:"NEXT_SESSION",LOAD_MORE:"LOAD_MORE",EXCEPTION:"EXCEPTION",CRASH:"CRASH",CUSTOM_EVENT:"CUSTOM_EVENT"};t.LOG_TYPES=a;var s=(o={},(0,i.default)(o,a.LOG,{label:"Logs",filterLabel:"Log",types:[a.LOG]}),(0,i.default)(o,a.WARN,{label:"Warnings",filterLabel:"Warning",types:[a.WARN]}),(0,i.default)(o,a.ERROR,{label:"Errors",filterLabel:"Error",types:[a.ERROR,a.EXCEPTION,a.CRASH]}),(0,i.default)(o,a.INFO,{label:"Info",filterLabel:"Info",types:[a.INFO]}),(0,i.default)(o,a.DEBUG,{label:"Debug",filterLabel:"Debug",types:[a.DEBUG]}),(0,i.default)(o,a.REDUX,{label:"Redux",types:[a.REDUX]}),(0,i.default)(o,a.NAVIGATION,{label:"Navigation",types:[a.NAVIGATION,a.LIFECYCLE]}),(0,i.default)(o,a.CUSTOM_EVENT,{label:"Custom Events",types:[a.CUSTOM_EVENT]}),o);t.LOG_FILTER_TYPES=s},394:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_INSIGHTS_HEATMAP_FOR_URL_OPERATOR=t.DEFAULT_HEATMAP_FOR_URL_OPERATOR=t.nextSortDirection=t.SORT_DIRECTION=t.FEEDBACK_SORT_COLUMN=t.HEATMAP_FOR_URL_OPERATOR=t.METRIC_RETENTION_TYPE=t.METRIC_TIMESERIES_TYPE=void 0;t.METRIC_TIMESERIES_TYPE={SESSION_COUNT:"SESSION_COUNT",PERCENTILE:"PERCENTILE",CONVERSION_RATE:"CONVERSION_RATE",SESSION_PERCENTAGE:"SESSION_PERCENTAGE",ACTIVE_USERS:"ACTIVE_USERS",EVENT_COUNT:"EVENT_COUNT"};t.METRIC_RETENTION_TYPE={USER_PERCENTAGE:"USER_PERCENTAGE"};var r={IS:"IS",CONTAINS:"CONTAINS",LIKE:"LIKE",HREF_LIKE:"HREF_LIKE"};t.HEATMAP_FOR_URL_OPERATOR=r;t.FEEDBACK_SORT_COLUMN={RATING:"RATING",SUBMISSION_DATE:"SUBMISSION_DATE"};var n={UNSET:"UNSET",ASC:"ASC",DESC:"DESC"};t.SORT_DIRECTION=n;t.nextSortDirection=function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).reverse,r=n.UNSET,o=n.ASC,i=n.DESC,a=[r,i,o];void 0!==t&&t&&(a=[r,o,i]);var s=a.indexOf(e);return a[(s+1)%a.length]};var o=r.IS;t.DEFAULT_HEATMAP_FOR_URL_OPERATOR=o;var i=r.HREF_LIKE;t.DEFAULT_INSIGHTS_HEATMAP_FOR_URL_OPERATOR=i},7961:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MAXIMUM_SUPPORTED_VERSION=t.MINIMUM_SUPPORTED_VERSION=t.IOS_VERSION=t.ANDROID_VERSION=void 0;t.ANDROID_VERSION="1.52.0";t.IOS_VERSION="1.52.0";t.MINIMUM_SUPPORTED_VERSION="1.0.0";t.MAXIMUM_SUPPORTED_VERSION="1.53.0"},4005:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DELIGHTED_FEEDBACK_PREFIX=t.DELIGHTED_RESPONSES_REGEX=t.WOOTRIC_RESPONSES_REGEX=void 0;t.WOOTRIC_RESPONSES_REGEX=/^https:\/\/production.wootric.com\/responses/;t.DELIGHTED_RESPONSES_REGEX=/^https:\/\/web.delighted.com\/e\/[a-zA-Z-]*\/c/;t.DELIGHTED_FEEDBACK_PREFIX="comment="},6287:function(e,t){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.PlatformType=void 0,t.PlatformType=r,function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.WEB=1]="WEB",e[e.ANDROID=2]="ANDROID",e[e.IOS=3]="IOS"}(r||(t.PlatformType=r={}))},9446:function(e,t){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.REPLAY_TYPE=void 0,t.REPLAY_TYPE=r,function(e){e.SKIA="SKIA",e.DOM="DOM",e.PIXEL="PIXEL"}(r||(t.REPLAY_TYPE=r={}))},886:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.SESSION_TYPE_TO_DISPLAY=t.REPLAY_SDK_TYPE=t.SDK_REPLAY_TYPE=t.SDK_TYPE_TO_DISPLAY=t.SDK_TYPE=void 0;var o,i,a,s,u=n(r(8416)),l=r(9446);t.SDK_TYPE=s,function(e){e.WEB="web",e.MOBILE="mobile",e.UNKNOWN="unknown"}(s||(t.SDK_TYPE=s={}));var c=(o={},(0,u.default)(o,s.WEB,"Web"),(0,u.default)(o,s.MOBILE,"Mobile"),o);t.SDK_TYPE_TO_DISPLAY=c;var f=(i={},(0,u.default)(i,s.WEB,l.REPLAY_TYPE.DOM),(0,u.default)(i,s.MOBILE,l.REPLAY_TYPE.SKIA),i);t.SDK_REPLAY_TYPE=f;var p=(a={},(0,u.default)(a,l.REPLAY_TYPE.DOM,s.WEB),(0,u.default)(a,l.REPLAY_TYPE.SKIA,s.MOBILE),(0,u.default)(a,l.REPLAY_TYPE.PIXEL,s.WEB),a);t.REPLAY_SDK_TYPE=p;var d=c;t.SESSION_TYPE_TO_DISPLAY=d},5612:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.getStatusText=function(e){return a[String(e)]},t.XHR_CUSTOM_FAILURE_STATUSES=t.XHR_CUSTOM_STATUS_CODES=t.STATUS_CODES=void 0;var o,i=n(r(8416)),a={0:"",100:"Continue",101:"Switching Protocol",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"unused",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"};t.STATUS_CODES=a;var s={error:0,timeout:444,aborted:499,offline:1001};t.XHR_CUSTOM_STATUS_CODES=s;var u=(o={},(0,i.default)(o,s.error,"Error"),(0,i.default)(o,s.timeout,"Timeout"),(0,i.default)(o,s.aborted,"Aborted"),(0,i.default)(o,s.offline,"Offline"),o);t.XHR_CUSTOM_FAILURE_STATUSES=u},9146:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return e&&t&&e.indexOf(t)>-1}},6836:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.createUnsubListener=function(e){return function(){e.clear()}},t.Handler=void 0;var o=n(r(6690)),i=n(r(9728)),a=function(){function e(t){(0,o.default)(this,e),this._value=void 0,this._value=t}return(0,i.default)(e,[{key:"get",value:function(){return this._value}},{key:"clear",value:function(){this._value=void 0}}]),e}();t.Handler=a},7681:function(e,t,r){"use strict";var n=r(8698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return{arson:o.encode(e)}},t.deepArsonifyEncodeAsTable=function(e){return{arson:o.encodeAsTable(e)}};var o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}o.default=e,r&&r.set(e,o);return o}(r(2706));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}},1754:function(e,t,r){"use strict";var n=r(8698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(!e)return e;return o.decode(e.arson)};var o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}o.default=e,r&&r.set(e,o);return o}(r(2706));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}},4267:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.delay=void 0;t.delay=function(e){return new Promise((function(t){return setTimeout(t,e)}))}},1537:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return e&&t&&e.substring(e.length-t.length)===t}},7800:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){if("function"!=typeof e[t])return o;try{var i=function(){for(var e,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var o=a.apply(this,r);return null===(e=s.get())||void 0===e||e.apply(this,r),o},a=e[t],s=new n.Handler(r);return e[t]=i,function(){s.clear(),e[t]===i&&(e[t]=a)}}catch(e){return o}};var n=r(6836),o=function(){}},6999:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}},8445:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return r;return-1}},1936:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.sdkThrottle,i=void 0===n?500:n;if(!e.length)return null;var a=(0,o.default)(e,{time:t},"time"),s=e[a]||e[a-1],u=e[a-1]||s,l=Math.min(s.time-u.time,i),c=Math.max(t-(s.time-l),0),f=0===l?1:Math.min(c/l,1);return{prev:u,next:s,ratio:f}};var o=n(r(1594))},8490:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return e.concat(t)}},8170:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCssRule=t.getCssRules=void 0;var r=function(e){try{if(e instanceof CSSStyleSheet||e instanceof CSSGroupingRule){var t=e.cssRules;return!t&&e instanceof CSSStyleSheet?e.rules:t}return null}catch(e){return null}};t.getCssRules=r;t.getCssRule=function(e,t){var n=r(e);return!n||t>=n.length?null:n.item(t)}},1429:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(e)try{var t=("string"==typeof e?JSON.parse(e):e).query.trim().match(/(query|mutation) ([a-z_-]+)/i);return t?{operationType:t[1],operationName:t[2]}:{}}catch(e){return{}}return{}}},1625:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=(0,n(r(9983)).default)((function(e){var t={nodeName:e.nodeName?e.nodeName.toLowerCase():""};return e.id&&e.id.length>0&&(t.id=e.id),e.classList&&e.classList.length>0&&(t.classList=Array.prototype.slice.call(e.classList,0)),t}));t.default=o},7105:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.combineHashes=t.hashString=void 0;var o=n(r(9983)),i=r(2769);function a(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var u=(0,o.default)((function(e){try{var t=0;if("string"==typeof e&&e.length)for(var r=0;r<e.length;r++)t=31*t+e.charCodeAt(r),t|=0;return t}catch(t){return(0,i.sendTelemetry)("Failed to hash string",{extra:{str:e}}),null}}));t.hashString=u;t.combineHashes=function(e){var t,r=e.shift()||0,n=a(e);try{for(n.s();!(t=n.n()).done;){r^=t.value+2654435769+(r<<6)+(r>>2)}}catch(e){n.e(e)}finally{n.f()}return r}},2256:function(e,t){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.parseIdentityStatus=function(e){if(!e)return r.Unknown;switch(parseInt(e,10)){case r.Anonymous:return r.Anonymous;case r.Identified:return r.Identified;default:return r.Unknown}},t.IdentityStatus=void 0,t.IdentityStatus=r,function(e){e[e.Unknown=0]="Unknown",e[e.Anonymous=1]="Anonymous",e[e.Identified=2]="Identified"}(r||(t.IdentityStatus=r={}))},2952:function(e,t,r){"use strict";var n=r(4836),o=r(8698);Object.defineProperty(t,"__esModule",{value:!0});var i={enhanceFunc:!0,shallowArsonify:!0,shallowDearsonify:!0,deepArsonify:!0,deepDearsonify:!0,addListener:!0,protectFunc:!0,findIndex:!0,find:!0,flatten:!0,selectorMatches:!0,parseSelectorForMatch:!0,sendErrorTelemetry:!0,sendTelemetry:!0,sanitizeValue:!0,adjustOs:!0,adjustOsVersion:!0,selectorFromNodePath:!0,startsWith:!0,endsWith:!0,contains:!0,randomInt:!0,getCssRules:!0,getCssRule:!0,getNodeSelector:!0,getGraphQLOperation:!0,isSessionEvent:!0,isActivityEvent:!0,parseIntFromHex:!0,isRecordingSampled:!0,makeRecordingID:!0,setFromArray:!0,setToArray:!0,applyUrlSanitizer:!0,maybeCleanSwiftUIClassName:!0,LOG_FILTER_TYPES:!0,LOG_TYPES:!0,METRIC_TIMESERIES_TYPE:!0,HEATMAP_FOR_URL_OPERATOR:!0,DEFAULT_HEATMAP_FOR_URL_OPERATOR:!0,DEFAULT_INSIGHTS_HEATMAP_FOR_URL_OPERATOR:!0,SORT_DIRECTION:!0,FEEDBACK_SORT_COLUMN:!0,METRIC_RETENTION_TYPE:!0,ISSUE_TYPE:!0,ISSUE_GROUP_TYPE:!0,ES_ISSUE_TYPE_BASE_FILTER:!0,findKeyFrames:!0,interpolate:!0,interpolateMobile:!0,removeOutdated:!0,DELIGHTED_RESPONSES_REGEX:!0,WOOTRIC_RESPONSES_REGEX:!0,PlatformType:!0,REPLAY_TYPE:!0,ReplayType:!0,SDK_REPLAY_TYPE:!0,REPLAY_SDK_TYPE:!0,SDK_TYPE:!0,SDK_TYPE_TO_DISPLAY:!0,SESSION_TYPE_TO_DISPLAY:!0,STATUS_CODES:!0,XHR_CUSTOM_STATUS_CODES:!0,XHR_CUSTOM_FAILURE_STATUSES:!0,getStatusText:!0,parseQueryString:!0,likeOperator:!0,scrollMapHistogramToPercent:!0,combineHashes:!0,hashString:!0,isThirdPartyUrl:!0,encodeUserTraitString:!0,parseUserTraitString:!0,IdentityStatus:!0,parseIdentityStatus:!0,scrubException:!0,matchesRootHostname:!0,MobileConstants:!0};Object.defineProperty(t,"enhanceFunc",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"shallowArsonify",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"shallowDearsonify",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"deepArsonify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"deepDearsonify",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"addListener",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(t,"protectFunc",{enumerable:!0,get:function(){return p.default}}),Object.defineProperty(t,"findIndex",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(t,"find",{enumerable:!0,get:function(){return h.default}}),Object.defineProperty(t,"flatten",{enumerable:!0,get:function(){return y.default}}),Object.defineProperty(t,"selectorMatches",{enumerable:!0,get:function(){return m.default}}),Object.defineProperty(t,"parseSelectorForMatch",{enumerable:!0,get:function(){return g.default}}),Object.defineProperty(t,"sendErrorTelemetry",{enumerable:!0,get:function(){return O.default}}),Object.defineProperty(t,"sendTelemetry",{enumerable:!0,get:function(){return O.sendTelemetry}}),Object.defineProperty(t,"sanitizeValue",{enumerable:!0,get:function(){return b.default}}),Object.defineProperty(t,"adjustOs",{enumerable:!0,get:function(){return v.adjustOs}}),Object.defineProperty(t,"adjustOsVersion",{enumerable:!0,get:function(){return v.adjustOsVersion}}),Object.defineProperty(t,"selectorFromNodePath",{enumerable:!0,get:function(){return E.default}}),Object.defineProperty(t,"startsWith",{enumerable:!0,get:function(){return T.default}}),Object.defineProperty(t,"endsWith",{enumerable:!0,get:function(){return I.default}}),Object.defineProperty(t,"contains",{enumerable:!0,get:function(){return w.default}}),Object.defineProperty(t,"randomInt",{enumerable:!0,get:function(){return S.default}}),Object.defineProperty(t,"getCssRules",{enumerable:!0,get:function(){return P.getCssRules}}),Object.defineProperty(t,"getCssRule",{enumerable:!0,get:function(){return P.getCssRule}}),Object.defineProperty(t,"getNodeSelector",{enumerable:!0,get:function(){return _.default}}),Object.defineProperty(t,"getGraphQLOperation",{enumerable:!0,get:function(){return N.default}}),Object.defineProperty(t,"isSessionEvent",{enumerable:!0,get:function(){return D.default}}),Object.defineProperty(t,"isActivityEvent",{enumerable:!0,get:function(){return A.default}}),Object.defineProperty(t,"parseIntFromHex",{enumerable:!0,get:function(){return j.default}}),Object.defineProperty(t,"isRecordingSampled",{enumerable:!0,get:function(){return R.default}}),Object.defineProperty(t,"makeRecordingID",{enumerable:!0,get:function(){return C.default}}),Object.defineProperty(t,"setFromArray",{enumerable:!0,get:function(){return k.default}}),Object.defineProperty(t,"setToArray",{enumerable:!0,get:function(){return x.default}}),Object.defineProperty(t,"applyUrlSanitizer",{enumerable:!0,get:function(){return L.default}}),Object.defineProperty(t,"maybeCleanSwiftUIClassName",{enumerable:!0,get:function(){return M.default}}),Object.defineProperty(t,"LOG_FILTER_TYPES",{enumerable:!0,get:function(){return F.LOG_FILTER_TYPES}}),Object.defineProperty(t,"LOG_TYPES",{enumerable:!0,get:function(){return F.LOG_TYPES}}),Object.defineProperty(t,"METRIC_TIMESERIES_TYPE",{enumerable:!0,get:function(){return U.METRIC_TIMESERIES_TYPE}}),Object.defineProperty(t,"HEATMAP_FOR_URL_OPERATOR",{enumerable:!0,get:function(){return U.HEATMAP_FOR_URL_OPERATOR}}),Object.defineProperty(t,"DEFAULT_HEATMAP_FOR_URL_OPERATOR",{enumerable:!0,get:function(){return U.DEFAULT_HEATMAP_FOR_URL_OPERATOR}}),Object.defineProperty(t,"DEFAULT_INSIGHTS_HEATMAP_FOR_URL_OPERATOR",{enumerable:!0,get:function(){return U.DEFAULT_INSIGHTS_HEATMAP_FOR_URL_OPERATOR}}),Object.defineProperty(t,"SORT_DIRECTION",{enumerable:!0,get:function(){return U.SORT_DIRECTION}}),Object.defineProperty(t,"FEEDBACK_SORT_COLUMN",{enumerable:!0,get:function(){return U.FEEDBACK_SORT_COLUMN}}),Object.defineProperty(t,"METRIC_RETENTION_TYPE",{enumerable:!0,get:function(){return U.METRIC_RETENTION_TYPE}}),Object.defineProperty(t,"ISSUE_TYPE",{enumerable:!0,get:function(){return B.ISSUE_TYPE}}),Object.defineProperty(t,"ISSUE_GROUP_TYPE",{enumerable:!0,get:function(){return B.ISSUE_GROUP_TYPE}}),Object.defineProperty(t,"ES_ISSUE_TYPE_BASE_FILTER",{enumerable:!0,get:function(){return B.ES_ISSUE_TYPE_BASE_FILTER}}),Object.defineProperty(t,"findKeyFrames",{enumerable:!0,get:function(){return V.default}}),Object.defineProperty(t,"interpolate",{enumerable:!0,get:function(){return H.default}}),Object.defineProperty(t,"interpolateMobile",{enumerable:!0,get:function(){return H.interpolateMobile}}),Object.defineProperty(t,"removeOutdated",{enumerable:!0,get:function(){return G.default}}),Object.defineProperty(t,"DELIGHTED_RESPONSES_REGEX",{enumerable:!0,get:function(){return Y.DELIGHTED_RESPONSES_REGEX}}),Object.defineProperty(t,"WOOTRIC_RESPONSES_REGEX",{enumerable:!0,get:function(){return Y.WOOTRIC_RESPONSES_REGEX}}),Object.defineProperty(t,"PlatformType",{enumerable:!0,get:function(){return J.PlatformType}}),Object.defineProperty(t,"REPLAY_TYPE",{enumerable:!0,get:function(){return W.REPLAY_TYPE}}),Object.defineProperty(t,"ReplayType",{enumerable:!0,get:function(){return W.ReplayType}}),Object.defineProperty(t,"SDK_REPLAY_TYPE",{enumerable:!0,get:function(){return q.SDK_REPLAY_TYPE}}),Object.defineProperty(t,"REPLAY_SDK_TYPE",{enumerable:!0,get:function(){return q.REPLAY_SDK_TYPE}}),Object.defineProperty(t,"SDK_TYPE",{enumerable:!0,get:function(){return q.SDK_TYPE}}),Object.defineProperty(t,"SDK_TYPE_TO_DISPLAY",{enumerable:!0,get:function(){return q.SDK_TYPE_TO_DISPLAY}}),Object.defineProperty(t,"SESSION_TYPE_TO_DISPLAY",{enumerable:!0,get:function(){return q.SESSION_TYPE_TO_DISPLAY}}),Object.defineProperty(t,"STATUS_CODES",{enumerable:!0,get:function(){return K.STATUS_CODES}}),Object.defineProperty(t,"XHR_CUSTOM_STATUS_CODES",{enumerable:!0,get:function(){return K.XHR_CUSTOM_STATUS_CODES}}),Object.defineProperty(t,"XHR_CUSTOM_FAILURE_STATUSES",{enumerable:!0,get:function(){return K.XHR_CUSTOM_FAILURE_STATUSES}}),Object.defineProperty(t,"getStatusText",{enumerable:!0,get:function(){return K.getStatusText}}),Object.defineProperty(t,"parseQueryString",{enumerable:!0,get:function(){return z.parseQueryString}}),Object.defineProperty(t,"likeOperator",{enumerable:!0,get:function(){return X.likeOperator}}),Object.defineProperty(t,"scrollMapHistogramToPercent",{enumerable:!0,get:function(){return Z.default}}),Object.defineProperty(t,"combineHashes",{enumerable:!0,get:function(){return $.combineHashes}}),Object.defineProperty(t,"hashString",{enumerable:!0,get:function(){return $.hashString}}),Object.defineProperty(t,"isThirdPartyUrl",{enumerable:!0,get:function(){return Q.isThirdPartyUrl}}),Object.defineProperty(t,"encodeUserTraitString",{enumerable:!0,get:function(){return ee.encodeUserTraitString}}),Object.defineProperty(t,"parseUserTraitString",{enumerable:!0,get:function(){return ee.parseUserTraitString}}),Object.defineProperty(t,"IdentityStatus",{enumerable:!0,get:function(){return te.IdentityStatus}}),Object.defineProperty(t,"parseIdentityStatus",{enumerable:!0,get:function(){return te.parseIdentityStatus}}),Object.defineProperty(t,"scrubException",{enumerable:!0,get:function(){return re.scrubException}}),Object.defineProperty(t,"matchesRootHostname",{enumerable:!0,get:function(){return ne.matchesRootHostname}}),t.MobileConstants=void 0;var a=n(r(7800)),s=n(r(8898)),u=n(r(1636)),l=n(r(7681)),c=n(r(1754)),f=n(r(1842)),p=n(r(9983)),d=n(r(8445)),h=n(r(6999)),y=n(r(8490)),m=n(r(9642)),g=n(r(7187)),O=he(r(2769)),b=n(r(8253)),v=r(6633),E=n(r(8404)),T=n(r(9242)),I=n(r(1537)),w=n(r(9146)),S=n(r(9412)),P=r(8170),_=n(r(1625)),N=n(r(1429)),D=n(r(8252)),A=n(r(4013)),j=n(r(3068)),R=n(r(8367)),C=n(r(5216)),k=n(r(1196)),x=n(r(6078)),L=n(r(8382)),M=n(r(1510)),F=r(2777),U=r(394),B=r(9790),V=n(r(1936)),H=he(r(6290)),G=n(r(7336)),Y=r(4005),J=r(6287),W=r(9446),q=r(886),K=r(5612),z=r(1418),X=r(2130),Z=n(r(2421)),$=r(7105),Q=r(6781),ee=r(5229),te=r(2256),re=r(6731),ne=r(9232),oe=he(r(7961));t.MobileConstants=oe;var ie=r(5632);Object.keys(ie).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===ie[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return ie[e]}}))}));var ae=r(5815);Object.keys(ae).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===ae[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return ae[e]}}))}));var se=r(748);Object.keys(se).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===se[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return se[e]}}))}));var ue=r(239);Object.keys(ue).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===ue[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return ue[e]}}))}));var le=r(4267);Object.keys(le).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===le[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return le[e]}}))}));var ce=r(2530);Object.keys(ce).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===ce[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return ce[e]}}))}));var fe=r(4393);Object.keys(fe).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===fe[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return fe[e]}}))}));var pe=r(1349);function de(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(de=function(e){return e?r:t})(e)}function he(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=de(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}Object.keys(pe).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(i,e)||e in t&&t[e]===pe[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return pe[e]}}))}))},6290:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,t.interpolateMobile=function(e,t,r){if(!e)return{};var n=e.prev,o=e.next;if("MOVE"===o.type)return u(e,t);if(n===o&&Math.abs(r-o.time)>500)return null;return o};var o=n(r(8416));function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,i=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw i}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e,t){if(!e)return{};var r,n=e.prev,a=e.next,u=e.ratio,l=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},a),c=i(t);try{for(c.s();!(r=c.n()).done;){var f=r.value;l[f]=n[f]+(a[f]-n[f])*u}}catch(e){c.e(e)}finally{c.f()}return l}},4013:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return["lr.browser.MouseEvent","lr.browser.ScrollEvent","lr.browser.InputEvent","lr.browser.InputChangeEvent","lr.android.TouchEvent","lr.android.InputChangeEvent","lr.ios.TouchEvent","lr.ios.InputChangeEvent"].indexOf(e)>-1}},8367:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t<=0)return!1;var r=Math.floor(t*i);return(0,o.default)(e)%i<=r};var o=n(r(3068)),i=1e4},8252:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return r.indexOf(e.type)>-1||"CLICK"===e.data.eventType};var r=["lr.browser.NavigationEvent","lr.browser.InputChangeEvent","lr.browser.InputEvent"]},6781:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.isThirdPartyUrl=void 0;var o=r(2769),i=n(r(9146));function a(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var u=["commerce.adobedc.net","demdex.net","omtrdc.net","magento-ds.com","mktoutil.com","doubleclick.net","google.com/g/collect","google-analytics.com/","merchant-center-analytics.goog/","google.com/pagead","google.com/adsense/","google.com/afs/ads","googleadapis.l.google.com","googleads.github.io","googlesyndication.com/pagead/","googlesyndication.com/safeframe/","facebook.com/tr","connect.facebook.net","https://t.co","http://t.co","clarity.ms/collect","clarity.ms/tag","bat.bing.com","bing.com/action","adsdk.microsoft.com","ads.linkedin.com","reddit.com/pixels","redditstatic.com/ads","conversions-config.reddit.com/v1/pixel","instapagemetrics.com/t","6sc.co","6sense.com/v3","go-mpulse.net/api","akamaihd.net","datadoghq","events.attentivemobile.com/e","static/hotjar.com","api.amplitude.com","statuspage.io/api","snapchat.com","ct.pinterest.com","adroll.com","stackadapt.com/saq_pxl","adnxs.com/pixie","clarity.ms/collect","braze.com/api","narrativ.com/api","analytics.tiktok.com/i18n/pixel","grsm.io","partnerlinks.io","taboola.com","criteo.com","app.howl.link/api","api.intentiq.com","api.getkoala.com","nr-data.net","api.segment.io","capture.trackjs.com/capture","sprig.com","bugsnag.com","api.honeybadger.io","wootric.com","medallia.com/api","transcend.io","rollout.io","chameleon.io/observe","boomtrain.com","teads.tv","everesttech.net","widengle.com","qualtrics.com","branch.io","rollbar.com","optimizely.com"];t.isThirdPartyUrl=function(e){try{if(!e)return!1;var t,r=a(u);try{for(r.s();!(t=r.n()).done;){var n=t.value;if((0,i.default)(e,n))return!0}}catch(e){r.e(e)}finally{r.f()}return!1}catch(t){return(0,o.sendTelemetry)("Failed to check isThirdPartyUrl",{extra:{str:e}}),!1}}},5815:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidAppID=function(e){return"string"==typeof e&&r.test(e)};var r=/^[a-z0-9_-]+\/[a-z0-9_-]+$/},2130:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.likeOperator=function(e,t){var r=t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/\\\*/g,".*").replace(/\\\?/g,".");return new RegExp("^".concat(r,"$")).test(e)}},536:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r="undefined"!=typeof console&&console.error&&console.error.bind?console.error.bind(console):function(){};t.default=r},5216:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.prefix,r=void 0===t?6:t,o=r>5?(0,n.uuidv7)():(0,n.uuid)();return"".concat(r,"-").concat(o)};var n=r(2530)},1657:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(null==e)return{};var r={};return Object.keys(e).forEach((function(n){r[n]=t(e[n])})),r}},9232:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.matchesRootHostname=function(e,t){var r=e.startsWith(".")?e:".".concat(e),n=t.length-r.length;return(n>=0?t.substring(n):".".concat(t))===r}},1510:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e?e.replace(/</g,"%3c").replace(/>/g,"%3e").replace(/\s/g,""):""}},3068:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.replace(/[^0-9a-f]/g,"");return parseInt("0x".concat(t),16)||0}},1418:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.parseQueryString=void 0;var o=n(r(7424));t.parseQueryString=function(e){var t=e.indexOf("?");return-1===t?{}:function(e){var t={};return e.split("&").forEach((function(e){var r=e.split("=").map((function(e){return e.replace(/\+/g," ")})).map(decodeURIComponent),n=(0,o.default)(r,2),i=n[0],a=n[1];t[i]=a})),t}(e.substring(t+1))}},7187:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.replace(/\s*>\s*|:nth-child\(([1-9][0-9]*|odd|even)\)|\s+/gi,(function(e){return e.indexOf(">")>-1?" >":0===e.trim().length?" ":"".concat(s).concat(e.slice(u.length))})).split(/\s+/).map((function(e){var t={},r=e;if(">"===r.charAt(0)&&(t.directDesc=!0,r=r.slice(1)),r=r.replace(/^([a-z0-9-_\[\]]+)/gi,(function(e){return t.nodeName=e,""})),t.nodeName&&(t.nodeName.indexOf("[")>-1||t.nodeName.indexOf("]")>-1))return null;if(r.charAt(0)===s){var n=r.slice(1).split(")"),u=(0,i.default)(n,2),f=u[0],p=u[1];t.nthChild=f,r=p}return(r=r.replace(/\\./g,l)).replace(/\.|#/gi,(function(e){return" ".concat(e)})).trim().split(" ").forEach((function(e){var r=e.replace(c,".");if((0,a.default)(r,".")){var n=r.slice(1);t.classList=t.classList?[].concat((0,o.default)(t.classList),[n]):[n]}else(0,a.default)(r,"#")&&(t.id=r.slice(1))})),t}));if(t.some((function(e){return!e})))return null;return t};var o=n(r(861)),i=n(r(7424)),a=n(r(9242)),s="~",u=":nth-child(",l="\\peri0d\\",c=new RegExp("\\".concat(l,"\\"),"g")},9983:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};return function(){var r;try{r=e.apply(void 0,arguments)}catch(e){if("undefined"!=typeof window&&window._lrdebug)throw e;var n=t(e);(0,i.default)("LogRocket",e),(0,o.default)(e,n)}return r}};var o=n(r(2769)),i=n(r(536))},9412:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=Math.ceil(e),n=Math.floor(t);return Math.floor(Math.random()*(n-r))+r}},7336:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(0===e.length)return e;var r=(0,o.default)(e,{time:t.time-i},"time")-1,n=e[r];if(n&&t.time-n.time>i&&r>0)return e.slice(r);return e};var o=n(r(1594)),i=5e3},748:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.requireValue=void 0;t.requireValue=function(e){if(void 0===e)throw new Error("Value must not be undefined.");return e}},8253:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=String(e);if("lipsum"===String(t).toLowerCase())return r(n);return""};var r=function(e){for(var t="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. ",r=Math.ceil(e.length/232),n="";r>0;)n+=t,r--;return n.slice(0,e.length)}},2421:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){for(var r=[],n=e.reduce((function(e,t){return e+t.count}),0),o=0,i=1;i<=100;i++){var a=t*(.01*i);o>=e.length?r.push(0):(r.push(n),a>e[o].maxScroll&&(n-=e[o].count,o++))}return r}},6731:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.scrubException=function(e,t){if(t){var r,n=i(u);try{for(n.s();!(r=n.n()).done;){var o=r.value,a=t[o];s(a)&&(e[o]=a.toString())}}catch(e){n.e(e)}finally{n.f()}var c,f=i(l);try{for(f.s();!(c=f.n()).done;){for(var p=c.value,d=t[p]||{},h={},y=0,m=Object.keys(d);y<m.length;y++){var g=m[y],O=d[g];s(O)&&(h[g.toString()]=O.toString())}e[p]=h}}catch(e){f.e(e)}finally{f.f()}}};var o=n(r(8698));function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,i=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw i}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function s(e){return/boolean|number|string/.test((0,o.default)(e))}var u=["level","logger"],l=["tags","extra"]},8404:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=[],n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}((0,o.default)(t).reverse());try{var a=function(){var t=e.value,n=t.nodeName,i=t.id,a=t.nthChild,s=t.classList,u=[],l=[];a&&l.push("nth-child(".concat(a,")")),n&&(u.push(n),l.sort().map((function(e){return u.push(":".concat(e))})));var c=/\./g;i&&u.push("#".concat(i.replace(c,"\\."))),s&&s.length&&(s=s.map((function(e){return e.toLowerCase()})).sort(),u=[].concat((0,o.default)(u),(0,o.default)(s.map((function(e){return".".concat(e.replace(c,"\\."))}))))),u.length&&r.push(u.join(" "))};for(n.s();!(e=n.n()).done;)a()}catch(e){n.e(e)}finally{n.f()}return r.join(" > ")};var o=n(r(861));function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}},9642:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(!t)return!1;var r,n,u=e.slice(0).reverse(),c=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(t);try{var f=function(){var e=n.value;return e.directDesc?l.apply(void 0,[e].concat((0,o.default)(u.splice(0,1))))?"continue":{v:!1}:-1===(r=(0,a.default)(u,(function(t){return l(e,t)})))?{v:!1}:void u.splice(0,r+1)};for(c.s();!(n=c.n()).done;){var p=f();if("continue"!==p&&"object"===(0,i.default)(p))return p.v}}catch(e){c.e(e)}finally{c.f()}return!0};var o=n(r(861)),i=n(r(8698)),a=n(r(8445));function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function u(e){return e?e.replace(/\\./g,"."):e}function l(e,t){if(!e||!t)return!1;if(e.id&&u(e.id)!==u(t.id))return!1;if(e.nodeName&&e.nodeName!==t.nodeName)return!1;if(e.classList&&(!t.classList||e.classList.map((function(e){return u(e.toLowerCase())})).some((function(e){return-1===t.classList.map((function(e){return u(e.toLowerCase())})).indexOf(e)}))))return!1;if(e.nthChild){var r=parseInt(e.nthChild,10);return isNaN(r)?Math.abs(t.nthChild%2)===("odd"===e.nthChild?1:0):r===t.nthChild}return!0}},2769:function(e,t,r){"use strict";var n=r(4836);Object.defineProperty(t,"__esModule",{value:!0}),t.sendTelemetry=function(e,t){if("undefined"!=typeof window&&window._lrdebug)return void(0,i.default)(e);if(t&&t.extra&&t.extra.appID&&"function"==typeof t.extra.appID.indexOf&&0===t.extra.appID.indexOf("au2drp/")&&Math.random()>=.25)return;c(u({message:e},t))},t.default=function(e,t){try{var r,n,o=e.message;try{r=JSON.stringify(t).slice(0,1e3)}catch(e){try{r="Could not stringify payload: ".concat(Object.prototype.toString.call(t))}catch(e){}}try{n=a.default.computeStackTrace(e).stack.map((function(e){return{filename:e.url,lineno:e.line,colno:e.column,function:e.func||"?"}}))}catch(e){}c({message:o,extra:{stringPayload:r},exception:{values:[{type:e.type,value:o,stacktrace:{frames:n}}]}})}catch(e){(0,i.default)("Failed to send",e)}};var o=n(r(8416)),i=n(r(536)),a=n(r(8668));function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l="9a675e490783885c680482e35a0a51005b082663";function c(e){try{var t,r,n=e.message,o="https://e.logrocket.com/api/3/store/?sentry_version=7&sentry_client=http%2F3.8.0&sentry_key=b64*****************************",a=JSON.stringify(u({message:n,logger:"javascript",platform:"javascript",request:{headers:{"User-Agent":"undefined"!=typeof navigator&&navigator.userAgent},url:"undefined"!=typeof location&&location.href},release:l,environment:(null===(t=window)||void 0===t||null===(r=t.__SDKCONFIG__)||void 0===r?void 0:r.scriptEnv)||"prod"},e));if("undefined"!=typeof window){var s=new(window._lrXMLHttpRequest||XMLHttpRequest);s.open("POST",o),s.send(a)}else"undefined"!=typeof fetch&&fetch(o,{method:"POST",body:a}).catch((function(e){(0,i.default)("Failed to send via fetch",e)}))}catch(e){(0,i.default)("Failed to send",e)}}},1196:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=new Set;return e.forEach((function(e){return t.add(e)})),t}},6078:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=[];return e.forEach((function(e){return t.push(e)})),t}},8898:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t={}.toString.call(e);switch(t){case"[object Undefined]":return{undefined:{}};case"[object Null]":return{null:{}};case"[object Boolean]":return{boolean:{bool:e}};case"[object Number]":return{number:{double:e}};case"[object String]":return{string:{string:e}};case"[object Object]":return{object:{map:e}};case"[object Array]":return{array:{map:e}};case"[object Date]":return{date:{double:e.getTime()}};case"[object Error]":return{string:{string:"".concat(e.name,": ").concat(e.message)}};default:return{unsupported:{type:t}}}}},1636:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){switch(e.type||Object.keys(e)[0]){case"undefined":return;case"null":return null;case"boolean":return e.boolean.bool;case"number":return e.number.double;case"string":return e.string.string;case"object":return e.object.map;case"array":return Object.keys(e.array.map).reduce((function(t,r){return t[r]=e.array.map[r],t}),[]);case"date":return new Date(e.date.double);case"unsupported":return e.unsupported.type;default:throw new TypeError("Unexpected value in shallowDearsonify: ".concat(e))}}},9242:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return e&&t&&e.substring(r,r+t.length)===t}},239:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},1349:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUserIdentifyEvent=t.MAX_USER_TRAIT_BYTES=t.MAX_IDENTITY_ARG_LENGTH=void 0;var r=1024;t.MAX_IDENTITY_ARG_LENGTH=r;t.MAX_USER_TRAIT_BYTES=128;var n=function(e){return"".concat(e," too large (max ").concat(r," characters)")};t.validateUserIdentifyEvent=function(e,t){var o=e.userID,i=e.email,a=e.name,s=e.traits,u=[],l=[];o.toString().length>r&&l.push(n("userID")),a&&a.length>r&&l.push(n("name")),i&&i.length>r&&l.push(n("email"));var c={};if(s)for(var f=0,p=Object.keys(s);f<p.length;f++){var d=p[f],h=s[d],y=d,m=!1;t&&t.encode(y).length>128&&(u.push("user trait key too large"),m=!0),h.toString().length>r&&(u.push(n("user trait value")),m=!0),m||(c[y]=h.toString())}return{event:{userID:o,email:i,name:a,traits:c},warnings:u,errors:l}}},5229:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseUserTraitString=t.encodeUserTraitString=void 0;var r=":",n=String.fromCharCode(26);t.encodeUserTraitString=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return t.map((function(e){return e.replace(new RegExp(r,"g"),n)})).join(r)};t.parseUserTraitString=function(e){return(e||r).split(r).map((function(e){return e.replace(new RegExp(n,"g"),r)}))}},2530:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uuid=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(t){var n=Number(t);return(n^16*Math.random()>>n/4).toString(16)}return r.replace(/[018]/g,e)},t.uuidv7=function(){var e=function(){var e=new Uint8Array(16);if("undefined"!=typeof crypto&&"function"==typeof crypto.getRandomValues)crypto.getRandomValues(e);else for(var t=0;t<e.byteLength;t++)e[t]=n();return e}(),t=Date.now();return e[0]=t/1099511627776&255,e[1]=t/4294967296&255,e[2]=t/16777216&255,e[3]=t/65536&255,e[4]=t/256&255,e[5]=t/1&255,e[6]=15&e[6]|112,e[8]=63&e[8]|128,o(e[0])+o(e[1])+o(e[2])+o(e[3])+"-"+o(e[4])+o(e[5])+"-"+o(e[6])+o(e[7])+"-"+o(e[8])+o(e[9])+"-"+o(e[10])+o(e[11])+o(e[12])+o(e[13])+o(e[14])+o(e[15])},t.extractTimestampFromUuidV7=function(e){var t=e.length-36;return parseInt(e.substring(t,13+t).replace("-",""),16)};var r="10000000-1000-4000-8000-100000000000";var n=function(){return Math.min(255,255*Math.random())};var o=function(e){var t=e.toString(16);return 1===t.length?"0".concat(t):t}},1143:function(e){"use strict";e.exports=function(e,t,r,n,o,i,a,s){if(!e){var u;if(void 0===t)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[r,n,o,i,a,s],c=0;(u=new Error(t.replace(/%s/g,(function(){return l[c++]})))).name="Invariant Violation"}throw u.framesToPop=1,u}}},8552:function(e,t,r){var n=r(852)(r(5639),"DataView");e.exports=n},1989:function(e,t,r){var n=r(1789),o=r(401),i=r(7667),a=r(1327),s=r(1866);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},8407:function(e,t,r){var n=r(7040),o=r(4125),i=r(2117),a=r(7529),s=r(4705);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},7071:function(e,t,r){var n=r(852)(r(5639),"Map");e.exports=n},3369:function(e,t,r){var n=r(4785),o=r(1285),i=r(6e3),a=r(9916),s=r(5265);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},3818:function(e,t,r){var n=r(852)(r(5639),"Promise");e.exports=n},8525:function(e,t,r){var n=r(852)(r(5639),"Set");e.exports=n},1258:function(e,t,r){var n=r(3369),o=r(619),i=r(2385);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},6384:function(e,t,r){var n=r(8407),o=r(7465),i=r(3779),a=r(7599),s=r(4758),u=r(4309);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=s,l.prototype.set=u,e.exports=l},2705:function(e,t,r){var n=r(5639).Symbol;e.exports=n},1149:function(e,t,r){var n=r(5639).Uint8Array;e.exports=n},577:function(e,t,r){var n=r(852)(r(5639),"WeakMap");e.exports=n},4963:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},4636:function(e,t,r){var n=r(2545),o=r(5694),i=r(1469),a=r(4144),s=r(5776),u=r(6719),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),c=!r&&o(e),f=!r&&!c&&a(e),p=!r&&!c&&!f&&u(e),d=r||c||f||p,h=d?n(e.length,String):[],y=h.length;for(var m in e)!t&&!l.call(e,m)||d&&("length"==m||f&&("offset"==m||"parent"==m)||p&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||s(m,y))||h.push(m);return h}},9932:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},2488:function(e){e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},2908:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},8470:function(e,t,r){var n=r(7813);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return-1}},9465:function(e,t,r){var n=r(8777);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},8483:function(e,t,r){var n=r(5063)();e.exports=n},7816:function(e,t,r){var n=r(8483),o=r(3674);e.exports=function(e,t){return e&&n(e,t,o)}},7786:function(e,t,r){var n=r(1811),o=r(327);e.exports=function(e,t){for(var r=0,i=(t=n(t,e)).length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},8866:function(e,t,r){var n=r(2488),o=r(1469);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},4239:function(e,t,r){var n=r(2705),o=r(9607),i=r(2333),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},13:function(e){e.exports=function(e,t){return null!=e&&t in Object(e)}},9454:function(e,t,r){var n=r(4239),o=r(7005);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},939:function(e,t,r){var n=r(2492),o=r(7005);e.exports=function e(t,r,i,a,s){return t===r||(null==t||null==r||!o(t)&&!o(r)?t!=t&&r!=r:n(t,r,i,a,e,s))}},2492:function(e,t,r){var n=r(6384),o=r(7114),i=r(8351),a=r(6096),s=r(4160),u=r(1469),l=r(4144),c=r(6719),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,m,g){var O=u(e),b=u(t),v=O?p:s(e),E=b?p:s(t),T=(v=v==f?d:v)==d,I=(E=E==f?d:E)==d,w=v==E;if(w&&l(e)){if(!l(t))return!1;O=!0,T=!1}if(w&&!T)return g||(g=new n),O||c(e)?o(e,t,r,y,m,g):i(e,t,v,r,y,m,g);if(!(1&r)){var S=T&&h.call(e,"__wrapped__"),P=I&&h.call(t,"__wrapped__");if(S||P){var _=S?e.value():e,N=P?t.value():t;return g||(g=new n),m(_,N,r,y,g)}}return!!w&&(g||(g=new n),a(e,t,r,y,m,g))}},2958:function(e,t,r){var n=r(6384),o=r(939);e.exports=function(e,t,r,i){var a=r.length,s=a,u=!i;if(null==e)return!s;for(e=Object(e);a--;){var l=r[a];if(u&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++a<s;){var c=(l=r[a])[0],f=e[c],p=l[1];if(u&&l[2]){if(void 0===f&&!(c in e))return!1}else{var d=new n;if(i)var h=i(f,p,c,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},8458:function(e,t,r){var n=r(3560),o=r(5346),i=r(3218),a=r(346),s=/^\[object .+?Constructor\]$/,u=Function.prototype,l=Object.prototype,c=u.toString,f=l.hasOwnProperty,p=RegExp("^"+c.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?p:s).test(a(e))}},8749:function(e,t,r){var n=r(4239),o=r(1780),i=r(7005),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},7206:function(e,t,r){var n=r(1573),o=r(6432),i=r(6557),a=r(1469),s=r(9601);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):s(e)}},280:function(e,t,r){var n=r(5726),o=r(6916),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},1573:function(e,t,r){var n=r(2958),o=r(1499),i=r(2634);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},6432:function(e,t,r){var n=r(939),o=r(7361),i=r(9095),a=r(5403),s=r(9162),u=r(2634),l=r(327);e.exports=function(e,t){return a(e)&&s(t)?u(l(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},371:function(e){e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},9152:function(e,t,r){var n=r(7786);e.exports=function(e){return function(t){return n(t,e)}}},7226:function(e,t,r){var n=r(3448),o=Math.floor,i=Math.min;e.exports=function(e,t,r,a){var s=0,u=null==e?0:e.length;if(0===u)return 0;for(var l=(t=r(t))!=t,c=null===t,f=n(t),p=void 0===t;s<u;){var d=o((s+u)/2),h=r(e[d]),y=void 0!==h,m=null===h,g=h==h,O=n(h);if(l)var b=a||g;else b=p?g&&(a||y):c?g&&y&&(a||!m):f?g&&y&&!m&&(a||!O):!m&&!O&&(a?h<=t:h<t);b?s=d+1:u=d}return i(u,4294967294)}},2545:function(e){e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},531:function(e,t,r){var n=r(2705),o=r(9932),i=r(1469),a=r(3448),s=n?n.prototype:void 0,u=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return u?u.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r}},7518:function(e){e.exports=function(e){return function(t){return e(t)}}},4757:function(e){e.exports=function(e,t){return e.has(t)}},1811:function(e,t,r){var n=r(1469),o=r(5403),i=r(5514),a=r(9833);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},4429:function(e,t,r){var n=r(5639)["__core-js_shared__"];e.exports=n},5063:function(e){e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),s=a.length;s--;){var u=a[e?s:++o];if(!1===r(i[u],u,i))break}return t}}},8777:function(e,t,r){var n=r(852),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},7114:function(e,t,r){var n=r(1258),o=r(2908),i=r(4757);e.exports=function(e,t,r,a,s,u){var l=1&r,c=e.length,f=t.length;if(c!=f&&!(l&&f>c))return!1;var p=u.get(e),d=u.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,m=2&r?new n:void 0;for(u.set(e,t),u.set(t,e);++h<c;){var g=e[h],O=t[h];if(a)var b=l?a(O,g,h,t,e,u):a(g,O,h,e,t,u);if(void 0!==b){if(b)continue;y=!1;break}if(m){if(!o(t,(function(e,t){if(!i(m,t)&&(g===e||s(g,e,r,a,u)))return m.push(t)}))){y=!1;break}}else if(g!==O&&!s(g,O,r,a,u)){y=!1;break}}return u.delete(e),u.delete(t),y}},8351:function(e,t,r){var n=r(2705),o=r(1149),i=r(7813),a=r(7114),s=r(8776),u=r(1814),l=n?n.prototype:void 0,c=l?l.valueOf:void 0;e.exports=function(e,t,r,n,l,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!f(new o(e),new o(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=s;case"[object Set]":var h=1&n;if(d||(d=u),e.size!=t.size&&!h)return!1;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var m=a(d(e),d(t),n,l,f,p);return p.delete(e),m;case"[object Symbol]":if(c)return c.call(e)==c.call(t)}return!1}},6096:function(e,t,r){var n=r(8234),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,s){var u=1&r,l=n(e),c=l.length;if(c!=n(t).length&&!u)return!1;for(var f=c;f--;){var p=l[f];if(!(u?p in t:o.call(t,p)))return!1}var d=s.get(e),h=s.get(t);if(d&&h)return d==t&&h==e;var y=!0;s.set(e,t),s.set(t,e);for(var m=u;++f<c;){var g=e[p=l[f]],O=t[p];if(i)var b=u?i(O,g,p,t,e,s):i(g,O,p,e,t,s);if(!(void 0===b?g===O||a(g,O,r,i,s):b)){y=!1;break}m||(m="constructor"==p)}if(y&&!m){var v=e.constructor,E=t.constructor;v==E||!("constructor"in e)||!("constructor"in t)||"function"==typeof v&&v instanceof v&&"function"==typeof E&&E instanceof E||(y=!1)}return s.delete(e),s.delete(t),y}},1957:function(e,t,r){var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},8234:function(e,t,r){var n=r(8866),o=r(9551),i=r(3674);e.exports=function(e){return n(e,i,o)}},5050:function(e,t,r){var n=r(7019);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},1499:function(e,t,r){var n=r(9162),o=r(3674);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},852:function(e,t,r){var n=r(8458),o=r(7801);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},9607:function(e,t,r){var n=r(2705),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[s]=r:delete e[s]),o}},9551:function(e,t,r){var n=r(4963),o=r(479),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,s=a?function(e){return null==e?[]:(e=Object(e),n(a(e),(function(t){return i.call(e,t)})))}:o;e.exports=s},4160:function(e,t,r){var n=r(8552),o=r(7071),i=r(3818),a=r(8525),s=r(577),u=r(4239),l=r(346),c="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=l(n),m=l(o),g=l(i),O=l(a),b=l(s),v=u;(n&&v(new n(new ArrayBuffer(1)))!=h||o&&v(new o)!=c||i&&v(i.resolve())!=f||a&&v(new a)!=p||s&&v(new s)!=d)&&(v=function(e){var t=u(e),r="[object Object]"==t?e.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return h;case m:return c;case g:return f;case O:return p;case b:return d}return t}),e.exports=v},7801:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},222:function(e,t,r){var n=r(1811),o=r(5694),i=r(1469),a=r(5776),s=r(1780),u=r(327);e.exports=function(e,t,r){for(var l=-1,c=(t=n(t,e)).length,f=!1;++l<c;){var p=u(t[l]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++l!=c?f:!!(c=null==e?0:e.length)&&s(c)&&a(p,c)&&(i(e)||o(e))}},1789:function(e,t,r){var n=r(4536);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},401:function(e){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},7667:function(e,t,r){var n=r(4536),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},1327:function(e,t,r){var n=r(4536),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},1866:function(e,t,r){var n=r(4536);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},5776:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},5403:function(e,t,r){var n=r(1469),o=r(3448),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!o(e))||(a.test(e)||!i.test(e)||null!=t&&e in Object(t))}},7019:function(e){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},5346:function(e,t,r){var n,o=r(4429),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},5726:function(e){var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},9162:function(e,t,r){var n=r(3218);e.exports=function(e){return e==e&&!n(e)}},7040:function(e){e.exports=function(){this.__data__=[],this.size=0}},4125:function(e,t,r){var n=r(8470),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},2117:function(e,t,r){var n=r(8470);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},7529:function(e,t,r){var n=r(8470);e.exports=function(e){return n(this.__data__,e)>-1}},4705:function(e,t,r){var n=r(8470);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},4785:function(e,t,r){var n=r(1989),o=r(8407),i=r(7071);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},1285:function(e,t,r){var n=r(5050);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},6e3:function(e,t,r){var n=r(5050);e.exports=function(e){return n(this,e).get(e)}},9916:function(e,t,r){var n=r(5050);e.exports=function(e){return n(this,e).has(e)}},5265:function(e,t,r){var n=r(5050);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},8776:function(e){e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r}},2634:function(e){e.exports=function(e,t){return function(r){return null!=r&&(r[e]===t&&(void 0!==t||e in Object(r)))}}},4569:function(e,t,r){var n=r(8306);e.exports=function(e){var t=n(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}},4536:function(e,t,r){var n=r(852)(Object,"create");e.exports=n},6916:function(e,t,r){var n=r(5569)(Object.keys,Object);e.exports=n},1167:function(e,t,r){e=r.nmd(e);var n=r(1957),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,s=function(){try{var e=i&&i.require&&i.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=s},2333:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},5569:function(e){e.exports=function(e,t){return function(r){return e(t(r))}}},5639:function(e,t,r){var n=r(1957),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},619:function(e){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},2385:function(e){e.exports=function(e){return this.__data__.has(e)}},1814:function(e){e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}},7465:function(e,t,r){var n=r(8407);e.exports=function(){this.__data__=new n,this.size=0}},3779:function(e){e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},7599:function(e){e.exports=function(e){return this.__data__.get(e)}},4758:function(e){e.exports=function(e){return this.__data__.has(e)}},4309:function(e,t,r){var n=r(8407),o=r(7071),i=r(3369);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},5514:function(e,t,r){var n=r(4569),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,(function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)})),t}));e.exports=a},327:function(e,t,r){var n=r(3448);e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},346:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},7813:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},7361:function(e,t,r){var n=r(7786);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},9095:function(e,t,r){var n=r(13),o=r(222);e.exports=function(e,t){return null!=e&&o(e,t,n)}},6557:function(e){e.exports=function(e){return e}},5694:function(e,t,r){var n=r(9454),o=r(7005),i=Object.prototype,a=i.hasOwnProperty,s=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!s.call(e,"callee")};e.exports=u},1469:function(e){var t=Array.isArray;e.exports=t},8612:function(e,t,r){var n=r(3560),o=r(1780);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},4144:function(e,t,r){e=r.nmd(e);var n=r(5639),o=r(5062),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,s=a&&a.exports===i?n.Buffer:void 0,u=(s?s.isBuffer:void 0)||o;e.exports=u},3560:function(e,t,r){var n=r(4239),o=r(3218);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},1780:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},3218:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},7005:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},3448:function(e,t,r){var n=r(4239),o=r(7005);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},6719:function(e,t,r){var n=r(8749),o=r(7518),i=r(1167),a=i&&i.isTypedArray,s=a?o(a):n;e.exports=s},3674:function(e,t,r){var n=r(4636),o=r(280),i=r(8612);e.exports=function(e){return i(e)?n(e):o(e)}},6604:function(e,t,r){var n=r(9465),o=r(7816),i=r(7206);e.exports=function(e,t){var r={};return t=i(t,3),o(e,(function(e,o,i){n(r,o,t(e,o,i))})),r}},8306:function(e,t,r){var n=r(3369);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},9601:function(e,t,r){var n=r(371),o=r(9152),i=r(5403),a=r(327);e.exports=function(e){return i(e)?n(a(e)):o(e)}},1594:function(e,t,r){var n=r(7206),o=r(7226);e.exports=function(e,t,r){return o(e,t,n(r,2),!0)}},479:function(e){e.exports=function(){return[]}},5062:function(e){e.exports=function(){return!1}},9833:function(e,t,r){var n=r(531);e.exports=function(e){return null==e?"":n(e)}},2360:function(e){"use strict";e.exports=function(e){try{var t=void 0;t}catch(e){}return null}},6841:function(){function e(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32}var t=-1;e.Diff,e.prototype.diff_main=function(e,t,r,n){void 0===n&&(n=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout);var o=n;if(null==e||null==t)throw new Error("Null input. (diff_main)");if(e==t)return e?[[0,e]]:[];void 0===r&&(r=!0);var i=r,a=this.diff_commonPrefix(e,t),s=e.substring(0,a);e=e.substring(a),t=t.substring(a),a=this.diff_commonSuffix(e,t);var u=e.substring(e.length-a);e=e.substring(0,e.length-a),t=t.substring(0,t.length-a);var l=this.diff_compute_(e,t,i,o);return s&&l.unshift([0,s]),u&&l.push([0,u]),this.diff_cleanupMerge(l),l},e.prototype.diff_compute_=function(e,r,n,o){var i;if(!e)return[[1,r]];if(!r)return[[t,e]];var a=e.length>r.length?e:r,s=e.length>r.length?r:e,u=a.indexOf(s);if(-1!=u)return i=[[1,a.substring(0,u)],[0,s],[1,a.substring(u+s.length)]],e.length>r.length&&(i[0][0]=i[2][0]=t),i;if(1==s.length)return[[t,e],[1,r]];a=s=null;var l=this.diff_halfMatch_(e,r);if(l){var c=l[0],f=l[1],p=l[2],d=l[3],h=l[4],y=this.diff_main(c,p,n,o),m=this.diff_main(f,d,n,o);return y.concat([[0,h]],m)}return n&&e.length>100&&r.length>100?this.diff_lineMode_(e,r,o):this.diff_bisect_(e,r,o)},e.prototype.diff_lineMode_=function(e,r,n){e=(f=this.diff_linesToChars_(e,r))[0],r=f[1];var o=f[2],i=this.diff_bisect_(e,r,n);this.diff_charsToLines_(i,o),this.diff_cleanupSemantic(i),i.push([0,""]);for(var a=0,s=0,u=0,l="",c="";a<i.length;){switch(i[a][0]){case 1:u++,c+=i[a][1];break;case t:s++,l+=i[a][1];break;case 0:if(s>=1&&u>=1){var f=this.diff_main(l,c,!1,n);i.splice(a-s-u,s+u),a=a-s-u;for(var p=f.length-1;p>=0;p--)i.splice(a,0,f[p]);a+=f.length}u=0,s=0,l="",c=""}a++}return i.pop(),i},e.prototype.diff_bisect_=function(e,r,n){for(var o=e.length,i=r.length,a=Math.ceil((o+i)/2),s=a,u=2*a,l=new Array(u),c=new Array(u),f=0;f<u;f++)l[f]=-1,c[f]=-1;l[s+1]=0,c[s+1]=0;for(var p=o-i,d=p%2!=0,h=0,y=0,m=0,g=0,O=0;O<a&&!((new Date).getTime()>n);O++){for(var b=-O+h;b<=O-y;b+=2){for(var v=s+b,E=(P=b==-O||b!=O&&l[v-1]<l[v+1]?l[v+1]:l[v-1]+1)-b;P<o&&E<i&&e.charAt(P)==r.charAt(E);)P++,E++;if(l[v]=P,P>o)y+=2;else if(E>i)h+=2;else if(d){if((w=s+p-b)>=0&&w<u&&-1!=c[w])if(P>=(I=o-c[w]))return this.diff_bisectSplit_(e,r,P,E,n)}}for(var T=-O+m;T<=O-g;T+=2){for(var I,w=s+T,S=(I=T==-O||T!=O&&c[w-1]<c[w+1]?c[w+1]:c[w-1]+1)-T;I<o&&S<i&&e.charAt(o-I-1)==r.charAt(i-S-1);)I++,S++;if(c[w]=I,I>o)g+=2;else if(S>i)m+=2;else if(!d){if((v=s+p-T)>=0&&v<u&&-1!=l[v]){var P;E=s+(P=l[v])-v;if(P>=(I=o-I))return this.diff_bisectSplit_(e,r,P,E,n)}}}}return[[t,e],[1,r]]},e.prototype.diff_bisectSplit_=function(e,t,r,n,o){var i=e.substring(0,r),a=t.substring(0,n),s=e.substring(r),u=t.substring(n),l=this.diff_main(i,a,!1,o),c=this.diff_main(s,u,!1,o);return l.concat(c)},e.prototype.diff_linesToChars_=function(e,t){var r=[],n={};function o(e){for(var t="",o=0,i=-1,a=r.length;i<e.length-1;){-1==(i=e.indexOf("\n",o))&&(i=e.length-1);var s=e.substring(o,i+1);o=i+1,(n.hasOwnProperty?n.hasOwnProperty(s):void 0!==n[s])?t+=String.fromCharCode(n[s]):(t+=String.fromCharCode(a),n[s]=a,r[a++]=s)}return t}return r[0]="",[o(e),o(t),r]},e.prototype.diff_charsToLines_=function(e,t){for(var r=0;r<e.length;r++){for(var n=e[r][1],o=[],i=0;i<n.length;i++)o[i]=t[n.charCodeAt(i)];e[r][1]=o.join("")}},e.prototype.diff_commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var r=0,n=Math.min(e.length,t.length),o=n,i=0;r<o;)e.substring(i,o)==t.substring(i,o)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return o},e.prototype.diff_commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var r=0,n=Math.min(e.length,t.length),o=n,i=0;r<o;)e.substring(e.length-o,e.length-i)==t.substring(t.length-o,t.length-i)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return o},e.prototype.diff_commonOverlap_=function(e,t){var r=e.length,n=t.length;if(0==r||0==n)return 0;r>n?e=e.substring(r-n):r<n&&(t=t.substring(0,r));var o=Math.min(r,n);if(e==t)return o;for(var i=0,a=1;;){var s=e.substring(o-a),u=t.indexOf(s);if(-1==u)return i;a+=u,0!=u&&e.substring(o-a)!=t.substring(0,a)||(i=a,a++)}},e.prototype.diff_halfMatch_=function(e,t){if(this.Diff_Timeout<=0)return null;var r=e.length>t.length?e:t,n=e.length>t.length?t:e;if(r.length<4||2*n.length<r.length)return null;var o=this;function i(e,t,r){for(var n,i,a,s,u=e.substring(r,r+Math.floor(e.length/4)),l=-1,c="";-1!=(l=t.indexOf(u,l+1));){var f=o.diff_commonPrefix(e.substring(r),t.substring(l)),p=o.diff_commonSuffix(e.substring(0,r),t.substring(0,l));c.length<p+f&&(c=t.substring(l-p,l)+t.substring(l,l+f),n=e.substring(0,r-p),i=e.substring(r+f),a=t.substring(0,l-p),s=t.substring(l+f))}return 2*c.length>=e.length?[n,i,a,s,c]:null}var a,s,u,l,c,f=i(r,n,Math.ceil(r.length/4)),p=i(r,n,Math.ceil(r.length/2));return f||p?(a=p?f&&f[4].length>p[4].length?f:p:f,e.length>t.length?(s=a[0],u=a[1],l=a[2],c=a[3]):(l=a[0],c=a[1],s=a[2],u=a[3]),[s,u,l,c,a[4]]):null},e.prototype.diff_cleanupSemantic=function(e){for(var r=!1,n=[],o=0,i=null,a=0,s=0,u=0,l=0,c=0;a<e.length;)0==e[a][0]?(n[o++]=a,s=l,u=c,l=0,c=0,i=e[a][1]):(1==e[a][0]?l+=e[a][1].length:c+=e[a][1].length,null!==i&&i.length<=Math.max(s,u)&&i.length<=Math.max(l,c)&&(e.splice(n[o-1],0,[t,i]),e[n[o-1]+1][0]=1,o--,a=--o>0?n[o-1]:-1,s=0,u=0,l=0,c=0,i=null,r=!0)),a++;for(r&&this.diff_cleanupMerge(e),this.diff_cleanupSemanticLossless(e),a=1;a<e.length;){if(e[a-1][0]==t&&1==e[a][0]){var f=e[a-1][1],p=e[a][1],d=this.diff_commonOverlap_(f,p);(d>=f.length/2||d>=p.length/2)&&(e.splice(a,0,[0,p.substring(0,d)]),e[a-1][1]=f.substring(0,f.length-d),e[a+1][1]=p.substring(d),a++),a++}a++}},e.prototype.diff_cleanupSemanticLossless=function(e){var t=/[^a-zA-Z0-9]/,r=/\s/,n=/[\r\n]/,o=/\n\r?\n$/,i=/^\r?\n\r?\n/;function a(e,a){if(!e||!a)return 5;var s=0;return(e.charAt(e.length-1).match(t)||a.charAt(0).match(t))&&(s++,(e.charAt(e.length-1).match(r)||a.charAt(0).match(r))&&(s++,(e.charAt(e.length-1).match(n)||a.charAt(0).match(n))&&(s++,(e.match(o)||a.match(i))&&s++))),s}for(var s=1;s<e.length-1;){if(0==e[s-1][0]&&0==e[s+1][0]){var u=e[s-1][1],l=e[s][1],c=e[s+1][1],f=this.diff_commonSuffix(u,l);if(f){var p=l.substring(l.length-f);u=u.substring(0,u.length-f),l=p+l.substring(0,l.length-f),c=p+c}for(var d=u,h=l,y=c,m=a(u,l)+a(l,c);l.charAt(0)===c.charAt(0);){u+=l.charAt(0),l=l.substring(1)+c.charAt(0),c=c.substring(1);var g=a(u,l)+a(l,c);g>=m&&(m=g,d=u,h=l,y=c)}e[s-1][1]!=d&&(d?e[s-1][1]=d:(e.splice(s-1,1),s--),e[s][1]=h,y?e[s+1][1]=y:(e.splice(s+1,1),s--))}s++}},e.prototype.diff_cleanupEfficiency=function(e){for(var r=!1,n=[],o=0,i="",a=0,s=!1,u=!1,l=!1,c=!1;a<e.length;)0==e[a][0]?(e[a][1].length<this.Diff_EditCost&&(l||c)?(n[o++]=a,s=l,u=c,i=e[a][1]):(o=0,i=""),l=c=!1):(e[a][0]==t?c=!0:l=!0,i&&(s&&u&&l&&c||i.length<this.Diff_EditCost/2&&s+u+l+c==3)&&(e.splice(n[o-1],0,[t,i]),e[n[o-1]+1][0]=1,o--,i="",s&&u?(l=c=!0,o=0):(a=--o>0?n[o-1]:-1,l=c=!1),r=!0)),a++;r&&this.diff_cleanupMerge(e)},e.prototype.diff_cleanupMerge=function(e){e.push([0,""]);for(var r,n=0,o=0,i=0,a="",s="";n<e.length;)switch(e[n][0]){case 1:i++,s+=e[n][1],n++;break;case t:o++,a+=e[n][1],n++;break;case 0:o+i>1?(0!==o&&0!==i&&(0!==(r=this.diff_commonPrefix(s,a))&&(n-o-i>0&&0==e[n-o-i-1][0]?e[n-o-i-1][1]+=s.substring(0,r):(e.splice(0,0,[0,s.substring(0,r)]),n++),s=s.substring(r),a=a.substring(r)),0!==(r=this.diff_commonSuffix(s,a))&&(e[n][1]=s.substring(s.length-r)+e[n][1],s=s.substring(0,s.length-r),a=a.substring(0,a.length-r))),0===o?e.splice(n-o-i,o+i,[1,s]):0===i?e.splice(n-o-i,o+i,[t,a]):e.splice(n-o-i,o+i,[t,a],[1,s]),n=n-o-i+(o?1:0)+(i?1:0)+1):0!==n&&0==e[n-1][0]?(e[n-1][1]+=e[n][1],e.splice(n,1)):n++,i=0,o=0,a="",s=""}""===e[e.length-1][1]&&e.pop();var u=!1;for(n=1;n<e.length-1;)0==e[n-1][0]&&0==e[n+1][0]&&(e[n][1].substring(e[n][1].length-e[n-1][1].length)==e[n-1][1]?(e[n][1]=e[n-1][1]+e[n][1].substring(0,e[n][1].length-e[n-1][1].length),e[n+1][1]=e[n-1][1]+e[n+1][1],e.splice(n-1,1),u=!0):e[n][1].substring(0,e[n+1][1].length)==e[n+1][1]&&(e[n-1][1]+=e[n+1][1],e[n][1]=e[n][1].substring(e[n+1][1].length)+e[n+1][1],e.splice(n+1,1),u=!0)),n++;u&&this.diff_cleanupMerge(e)},e.prototype.diff_xIndex=function(e,r){var n,o=0,i=0,a=0,s=0;for(n=0;n<e.length&&(1!==e[n][0]&&(o+=e[n][1].length),e[n][0]!==t&&(i+=e[n][1].length),!(o>r));n++)a=o,s=i;return e.length!=n&&e[n][0]===t?s:s+(r-a)},e.prototype.diff_prettyHtml=function(e){for(var r=[],n=/&/g,o=/</g,i=/>/g,a=/\n/g,s=0;s<e.length;s++){var u=e[s][0],l=e[s][1],c=l.replace(n,"&amp;").replace(o,"&lt;").replace(i,"&gt;").replace(a,"&para;<br>");switch(u){case 1:r[s]='<ins style="background:#e6ffe6;">'+c+"</ins>";break;case t:r[s]='<del style="background:#ffe6e6;">'+c+"</del>";break;case 0:r[s]="<span>"+c+"</span>"}u!==t&&l.length}return r.join("")},e.prototype.diff_text1=function(e){for(var t=[],r=0;r<e.length;r++)1!==e[r][0]&&(t[r]=e[r][1]);return t.join("")},e.prototype.diff_text2=function(e){for(var r=[],n=0;n<e.length;n++)e[n][0]!==t&&(r[n]=e[n][1]);return r.join("")},e.prototype.diff_levenshtein=function(e){for(var r=0,n=0,o=0,i=0;i<e.length;i++){var a=e[i][0],s=e[i][1];switch(a){case 1:n+=s.length;break;case t:o+=s.length;break;case 0:r+=Math.max(n,o),n=0,o=0}}return r+=Math.max(n,o)},e.prototype.diff_toDelta=function(e){for(var r=[],n=0;n<e.length;n++)switch(e[n][0]){case 1:r[n]="+"+encodeURI(e[n][1]);break;case t:r[n]="-"+e[n][1].length;break;case 0:r[n]="="+e[n][1].length}return r.join("\t").replace(/%20/g," ")},e.prototype.diff_fromDelta=function(e,r){for(var n=[],o=0,i=0,a=r.split(/\t/g),s=0;s<a.length;s++){var u=a[s].substring(1);switch(a[s].charAt(0)){case"+":try{n[o++]=[1,decodeURI(u)]}catch(e){throw new Error("Illegal escape in diff_fromDelta: "+u)}break;case"-":case"=":var l=parseInt(u,10);if(isNaN(l)||l<0)throw new Error("Invalid number in diff_fromDelta: "+u);var c=e.substring(i,i+=l);"="==a[s].charAt(0)?n[o++]=[0,c]:n[o++]=[t,c];break;default:if(a[s])throw new Error("Invalid diff operation in diff_fromDelta: "+a[s])}}if(i!=e.length)throw new Error("Delta length ("+i+") does not equal source text length ("+e.length+").");return n},e.prototype.match_main=function(e,t,r){if(null==e||null==t||null==r)throw new Error("Null input. (match_main)");return r=Math.max(0,Math.min(r,e.length)),e==t?0:e.length?e.substring(r,r+t.length)==t?r:this.match_bitap_(e,t,r):-1},e.prototype.match_bitap_=function(e,t,r){if(t.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var n=this.match_alphabet_(t),o=this;function i(e,n){var i=e/t.length,a=Math.abs(r-n);return o.Match_Distance?i+a/o.Match_Distance:a?1:i}var a=this.Match_Threshold,s=e.indexOf(t,r);-1!=s&&(a=Math.min(i(0,s),a),-1!=(s=e.lastIndexOf(t,r+t.length))&&(a=Math.min(i(0,s),a)));var u,l,c=1<<t.length-1;s=-1;for(var f,p=t.length+e.length,d=0;d<t.length;d++){for(u=0,l=p;u<l;)i(d,r+l)<=a?u=l:p=l,l=Math.floor((p-u)/2+u);p=l;var h=Math.max(1,r-l+1),y=Math.min(r+l,e.length)+t.length,m=Array(y+2);m[y+1]=(1<<d)-1;for(var g=y;g>=h;g--){var O=n[e.charAt(g-1)];if(m[g]=0===d?(m[g+1]<<1|1)&O:(m[g+1]<<1|1)&O|(f[g+1]|f[g])<<1|1|f[g+1],m[g]&c){var b=i(d,g-1);if(b<=a){if(a=b,!((s=g-1)>r))break;h=Math.max(1,2*r-s)}}}if(i(d+1,r)>a)break;f=m}return s},e.prototype.match_alphabet_=function(e){for(var t={},r=0;r<e.length;r++)t[e.charAt(r)]=0;for(r=0;r<e.length;r++)t[e.charAt(r)]|=1<<e.length-r-1;return t},e.prototype.patch_addContext_=function(e,t){if(0!=t.length){for(var r=t.substring(e.start2,e.start2+e.length1),n=0;t.indexOf(r)!=t.lastIndexOf(r)&&r.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)n+=this.Patch_Margin,r=t.substring(e.start2-n,e.start2+e.length1+n);n+=this.Patch_Margin;var o=t.substring(e.start2-n,e.start2);o&&e.diffs.unshift([0,o]);var i=t.substring(e.start2+e.length1,e.start2+e.length1+n);i&&e.diffs.push([0,i]),e.start1-=o.length,e.start2-=o.length,e.length1+=o.length+i.length,e.length2+=o.length+i.length}},e.prototype.patch_make=function(r,n,o){var i,a;if("string"==typeof r&&"string"==typeof n&&void 0===o)i=r,(a=this.diff_main(i,n,!0)).length>2&&(this.diff_cleanupSemantic(a),this.diff_cleanupEfficiency(a));else if(r&&"object"==typeof r&&void 0===n&&void 0===o)a=r,i=this.diff_text1(a);else if("string"==typeof r&&n&&"object"==typeof n&&void 0===o)i=r,a=n;else{if("string"!=typeof r||"string"!=typeof n||!o||"object"!=typeof o)throw new Error("Unknown call format to patch_make.");i=r,a=o}if(0===a.length)return[];for(var s=[],u=new e.patch_obj,l=0,c=0,f=0,p=i,d=i,h=0;h<a.length;h++){var y=a[h][0],m=a[h][1];switch(l||0===y||(u.start1=c,u.start2=f),y){case 1:u.diffs[l++]=a[h],u.length2+=m.length,d=d.substring(0,f)+m+d.substring(f);break;case t:u.length1+=m.length,u.diffs[l++]=a[h],d=d.substring(0,f)+d.substring(f+m.length);break;case 0:m.length<=2*this.Patch_Margin&&l&&a.length!=h+1?(u.diffs[l++]=a[h],u.length1+=m.length,u.length2+=m.length):m.length>=2*this.Patch_Margin&&l&&(this.patch_addContext_(u,p),s.push(u),u=new e.patch_obj,l=0,p=d,c=f)}1!==y&&(c+=m.length),y!==t&&(f+=m.length)}return l&&(this.patch_addContext_(u,p),s.push(u)),s},e.prototype.patch_deepCopy=function(t){for(var r=[],n=0;n<t.length;n++){var o=t[n],i=new e.patch_obj;i.diffs=[];for(var a=0;a<o.diffs.length;a++)i.diffs[a]=o.diffs[a].slice();i.start1=o.start1,i.start2=o.start2,i.length1=o.length1,i.length2=o.length2,r[n]=i}return r},e.prototype.patch_apply=function(e,r){if(0==e.length)return[r,[]];e=this.patch_deepCopy(e);var n=this.patch_addPadding(e);r=n+r+n,this.patch_splitMax(e);for(var o=0,i=[],a=0;a<e.length;a++){var s,u,l=e[a].start2+o,c=this.diff_text1(e[a].diffs),f=-1;if(c.length>this.Match_MaxBits?-1!=(s=this.match_main(r,c.substring(0,this.Match_MaxBits),l))&&(-1==(f=this.match_main(r,c.substring(c.length-this.Match_MaxBits),l+c.length-this.Match_MaxBits))||s>=f)&&(s=-1):s=this.match_main(r,c,l),-1==s)i[a]=!1,o-=e[a].length2-e[a].length1;else if(i[a]=!0,o=s-l,c==(u=-1==f?r.substring(s,s+c.length):r.substring(s,f+this.Match_MaxBits)))r=r.substring(0,s)+this.diff_text2(e[a].diffs)+r.substring(s+c.length);else{var p=this.diff_main(c,u,!1);if(c.length>this.Match_MaxBits&&this.diff_levenshtein(p)/c.length>this.Patch_DeleteThreshold)i[a]=!1;else{this.diff_cleanupSemanticLossless(p);for(var d,h=0,y=0;y<e[a].diffs.length;y++){var m=e[a].diffs[y];0!==m[0]&&(d=this.diff_xIndex(p,h)),1===m[0]?r=r.substring(0,s+d)+m[1]+r.substring(s+d):m[0]===t&&(r=r.substring(0,s+d)+r.substring(s+this.diff_xIndex(p,h+m[1].length))),m[0]!==t&&(h+=m[1].length)}}}}return[r=r.substring(n.length,r.length-n.length),i]},e.prototype.patch_addPadding=function(e){for(var t=this.Patch_Margin,r="",n=1;n<=t;n++)r+=String.fromCharCode(n);for(n=0;n<e.length;n++)e[n].start1+=t,e[n].start2+=t;var o=e[0],i=o.diffs;if(0==i.length||0!=i[0][0])i.unshift([0,r]),o.start1-=t,o.start2-=t,o.length1+=t,o.length2+=t;else if(t>i[0][1].length){var a=t-i[0][1].length;i[0][1]=r.substring(i[0][1].length)+i[0][1],o.start1-=a,o.start2-=a,o.length1+=a,o.length2+=a}if(0==(i=(o=e[e.length-1]).diffs).length||0!=i[i.length-1][0])i.push([0,r]),o.length1+=t,o.length2+=t;else if(t>i[i.length-1][1].length){a=t-i[i.length-1][1].length;i[i.length-1][1]+=r.substring(0,a),o.length1+=a,o.length2+=a}return r},e.prototype.patch_splitMax=function(r){for(var n=this.Match_MaxBits,o=0;o<r.length;o++)if(r[o].length1>n){var i=r[o];r.splice(o--,1);for(var a=i.start1,s=i.start2,u="";0!==i.diffs.length;){var l=new e.patch_obj,c=!0;for(l.start1=a-u.length,l.start2=s-u.length,""!==u&&(l.length1=l.length2=u.length,l.diffs.push([0,u]));0!==i.diffs.length&&l.length1<n-this.Patch_Margin;){var f=i.diffs[0][0],p=i.diffs[0][1];1===f?(l.length2+=p.length,s+=p.length,l.diffs.push(i.diffs.shift()),c=!1):f===t&&1==l.diffs.length&&0==l.diffs[0][0]&&p.length>2*n?(l.length1+=p.length,a+=p.length,c=!1,l.diffs.push([f,p]),i.diffs.shift()):(p=p.substring(0,n-l.length1-this.Patch_Margin),l.length1+=p.length,a+=p.length,0===f?(l.length2+=p.length,s+=p.length):c=!1,l.diffs.push([f,p]),p==i.diffs[0][1]?i.diffs.shift():i.diffs[0][1]=i.diffs[0][1].substring(p.length))}u=(u=this.diff_text2(l.diffs)).substring(u.length-this.Patch_Margin);var d=this.diff_text1(i.diffs).substring(0,this.Patch_Margin);""!==d&&(l.length1+=d.length,l.length2+=d.length,0!==l.diffs.length&&0===l.diffs[l.diffs.length-1][0]?l.diffs[l.diffs.length-1][1]+=d:l.diffs.push([0,d])),c||r.splice(++o,0,l)}}},e.prototype.patch_toText=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e[r];return t.join("")},e.prototype.patch_fromText=function(r){var n=[];if(!r)return n;for(var o=r.split("\n"),i=0,a=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;i<o.length;){var s=o[i].match(a);if(!s)throw new Error("Invalid patch string: "+o[i]);var u=new e.patch_obj;for(n.push(u),u.start1=parseInt(s[1],10),""===s[2]?(u.start1--,u.length1=1):"0"==s[2]?u.length1=0:(u.start1--,u.length1=parseInt(s[2],10)),u.start2=parseInt(s[3],10),""===s[4]?(u.start2--,u.length2=1):"0"==s[4]?u.length2=0:(u.start2--,u.length2=parseInt(s[4],10)),i++;i<o.length;){var l=o[i].charAt(0);try{var c=decodeURI(o[i].substring(1))}catch(e){throw new Error("Illegal escape in patch_fromText: "+c)}if("-"==l)u.diffs.push([t,c]);else if("+"==l)u.diffs.push([1,c]);else if(" "==l)u.diffs.push([0,c]);else{if("@"==l)break;if(""!==l)throw new Error('Invalid patch mode "'+l+'" in: '+c)}i++}}return n},e.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},e.patch_obj.prototype.toString=function(){for(var e,r=["@@ -"+(0===this.length1?this.start1+",0":1==this.length1?this.start1+1:this.start1+1+","+this.length1)+" +"+(0===this.length2?this.start2+",0":1==this.length2?this.start2+1:this.start2+1+","+this.length2)+" @@\n"],n=0;n<this.diffs.length;n++){switch(this.diffs[n][0]){case 1:e="+";break;case t:e="-";break;case 0:e=" "}r[n+1]=e+encodeURI(this.diffs[n][1])+"\n"}return r.join("").replace(/%20/g," ")},this.diff_match_patch=e,this.DIFF_DELETE=t,this.DIFF_INSERT=1,this.DIFF_EQUAL=0},3538:function(e,t,r){var n={"./diff_match_patch_uncompressed":6841,"./diff_match_patch_uncompressed.js":6841};function o(e){var t=i(e);return r(t)}function i(e){if(!r.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=i,e.exports=o,o.id=3538},9559:function(e){var t="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array};e.exports=function e(r){if("object"!=typeof r)return r;if(null===r)return null;if(t(r))return r.map(e);if(r instanceof Date)return new Date(r.getTime());if(r instanceof RegExp)return n=/^\/(.*)\/([gimyu]*)$/.exec(r.toString()),new RegExp(n[1],n[2]);var n,o={};for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(o[i]=e(r[i]));return o}},4251:function(e,t,r){var n=r(5820).E,o=function(){};o.prototype.setResult=function(e){return this.result=e,this.hasResult=!0,this},o.prototype.exit=function(){return this.exiting=!0,this},o.prototype.switchTo=function(e,t){return"string"==typeof e||e instanceof n?this.nextPipe=e:(this.next=e,t&&(this.nextPipe=t)),this},o.prototype.push=function(e,t){return e.parent=this,void 0!==t&&(e.childName=t),e.root=this.root||this,e.options=e.options||this.options,this.children?(this.children[this.children.length-1].next=e,this.children.push(e)):(this.children=[e],this.nextAfterChildren=this.next||null,this.next=e),e.next=this,this},t._=o},9637:function(e,t,r){var n=r(4251)._,o=r(9559),i=function(e,t){this.left=e,this.right=t,this.pipe="diff"};(i.prototype=new n).setResult=function(e){if(this.options.cloneDiffValues&&"object"==typeof e){var t="function"==typeof this.options.cloneDiffValues?this.options.cloneDiffValues:o;"object"==typeof e[0]&&(e[0]=t(e[0])),"object"==typeof e[1]&&(e[1]=t(e[1]))}return n.prototype.setResult.apply(this,arguments)},t.A=i},7443:function(e,t,r){var n=r(4251)._,o=function(e,t){this.left=e,this.delta=t,this.pipe="patch"};o.prototype=new n,t.f=o},4153:function(e,t,r){var n=r(4251)._,o=function(e){this.delta=e,this.pipe="reverse"};o.prototype=new n,t.V=o},1390:function(e,t,r){var n=r(7854).w,o=r(5820).E,i=r(9637).A,a=r(7443).f,s=r(4153).V,u=r(9559),l=r(6294),c=r(1104),f=r(107),p=r(1634),d=r(328),h=function(e){this.processor=new n(e),this.processor.pipe(new o("diff").append(c.collectChildrenDiffFilter,l.diffFilter,p.diffFilter,d.diffFilter,c.objectsDiffFilter,f.diffFilter).shouldHaveResult()),this.processor.pipe(new o("patch").append(c.collectChildrenPatchFilter,f.collectChildrenPatchFilter,l.patchFilter,d.patchFilter,c.patchFilter,f.patchFilter).shouldHaveResult()),this.processor.pipe(new o("reverse").append(c.collectChildrenReverseFilter,f.collectChildrenReverseFilter,l.reverseFilter,d.reverseFilter,c.reverseFilter,f.reverseFilter).shouldHaveResult())};h.prototype.options=function(){return this.processor.options.apply(this.processor,arguments)},h.prototype.diff=function(e,t){return this.processor.process(new i(e,t))},h.prototype.patch=function(e,t){return this.processor.process(new a(e,t))},h.prototype.reverse=function(e){return this.processor.process(new s(e))},h.prototype.unpatch=function(e,t){return this.patch(e,this.reverse(t))},h.prototype.clone=function(e){return u(e)},t.DiffPatcher=h},107:function(e,t,r){var n=r(9637).A,o=r(7443).f,i=r(4153).V,a=r(7821),s="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array},u="function"==typeof Array.prototype.indexOf?function(e,t){return e.indexOf(t)}:function(e,t){for(var r=e.length,n=0;n<r;n++)if(e[n]===t)return n;return-1};function l(e,t,r,n,o){var i=e[r],a=t[n];if(i===a)return!0;if("object"!=typeof i||"object"!=typeof a)return!1;var s,u,l=o.objectHash;return l?("number"==typeof r?(o.hashCache1=o.hashCache1||[],void 0===(s=o.hashCache1[r])&&(o.hashCache1[r]=s=l(i,r))):s=l(i),void 0!==s&&("number"==typeof n?(o.hashCache2=o.hashCache2||[],void 0===(u=o.hashCache2[n])&&(o.hashCache2[n]=u=l(a,n))):u=l(a),void 0!==u&&s===u)):o.matchByPosition&&r===n}var c=function(e){if(e.leftIsArray){var t,r,o,i,s,c={objectHash:e.options&&e.options.objectHash,matchByPosition:e.options&&e.options.matchByPosition},f=0,p=0,d=e.left,h=e.right,y=d.length,m=h.length;for(y>0&&m>0&&!c.objectHash&&"boolean"!=typeof c.matchByPosition&&(c.matchByPosition=!function(e,t,r,n){for(var o=0;o<r;o++)for(var i=e[o],a=0;a<n;a++){var s=t[a];if(o!==a&&i===s)return!0}}(d,h,y,m));f<y&&f<m&&l(d,h,f,f,c);)t=f,i=new n(e.left[t],e.right[t]),e.push(i,t),f++;for(;p+f<y&&p+f<m&&l(d,h,y-1-p,m-1-p,c);)r=y-1-p,o=m-1-p,i=new n(e.left[r],e.right[o]),e.push(i,o),p++;if(f+p!==y)if(f+p!==m){delete c.hashCache1,delete c.hashCache2;var g=d.slice(f,y-p),O=h.slice(f,m-p),b=a.get(g,O,l,c),v=[];for(s=s||{_t:"a"},t=f;t<y-p;t++)u(b.indices1,t-f)<0&&(s["_"+t]=[d[t],0,0],v.push(t));var E=!0;e.options&&e.options.arrays&&!1===e.options.arrays.detectMove&&(E=!1);var T=!1;e.options&&e.options.arrays&&e.options.arrays.includeValueOnMove&&(T=!0);var I=v.length;for(t=f;t<m-p;t++){var w=u(b.indices2,t-f);if(w<0){var S=!1;if(E&&I>0)for(var P=0;P<I;P++)if(l(g,O,(r=v[P])-f,t-f,c)){s["_"+r].splice(1,2,t,3),T||(s["_"+r][0]=""),o=t,i=new n(e.left[r],e.right[o]),e.push(i,o),v.splice(P,1),S=!0;break}S||(s[t]=[h[t]])}else r=b.indices1[w]+f,o=b.indices2[w]+f,i=new n(e.left[r],e.right[o]),e.push(i,o)}e.setResult(s).exit()}else{for(s=s||{_t:"a"},t=f;t<y-p;t++)s["_"+t]=[d[t],0,0];e.setResult(s).exit()}else{if(y===m)return void e.setResult(void 0).exit();for(s=s||{_t:"a"},t=f;t<m-p;t++)s[t]=[h[t]];e.setResult(s).exit()}}};c.filterName="arrays";var f=function(e,t){return e-t},p=function(e){return function(t,r){return t[e]-r[e]}},d=function(e){if(e.nested&&"a"===e.delta._t){var t,r,n=e.delta,i=e.left,a=[],s=[],u=[];for(t in n)if("_t"!==t)if("_"===t[0]){if(0!==n[t][2]&&3!==n[t][2])throw new Error("only removal or move can be applied at original array indices, invalid diff type: "+n[t][2]);a.push(parseInt(t.slice(1),10))}else 1===n[t].length?s.push({index:parseInt(t,10),value:n[t][0]}):u.push({index:parseInt(t,10),delta:n[t]});for(t=(a=a.sort(f)).length-1;t>=0;t--){var l=n["_"+(r=a[t])],c=i.splice(r,1)[0];3===l[2]&&s.push({index:l[1],value:c})}var d=(s=s.sort(p("index"))).length;for(t=0;t<d;t++){var h=s[t];i.splice(h.index,0,h.value)}var y,m=u.length;if(m>0)for(t=0;t<m;t++){var g=u[t];y=new o(e.left[g.index],g.delta),e.push(y,g.index)}e.children?e.exit():e.setResult(e.left).exit()}};d.filterName="arrays";var h=function(e){if(e&&e.children&&"a"===e.delta._t){for(var t,r=e.children.length,n=0;n<r;n++)t=e.children[n],e.left[t.childName]=t.result;e.setResult(e.left).exit()}};h.filterName="arraysCollectChildren";var y=function(e){if(e.nested){if("a"===e.delta._t){var t,r;for(t in e.delta)"_t"!==t&&(r=new i(e.delta[t]),e.push(r,t));e.exit()}}else 3===e.delta[2]&&(e.newName="_"+e.delta[1],e.setResult([e.delta[0],parseInt(e.childName.substr(1),10),3]).exit())};y.filterName="arrays";var m=function(e,t,r){if("string"==typeof t&&"_"===t[0])return parseInt(t.substr(1),10);if(s(r)&&0===r[2])return"_"+t;var n=+t;for(var o in e){var i=e[o];if(s(i))if(3===i[2]){var a=parseInt(o.substr(1),10),u=i[1];if(u===+t)return a;a<=n&&u>n?n++:a>=n&&u<n&&n--}else if(0===i[2]){parseInt(o.substr(1),10)<=n&&n++}else 1===i.length&&o<=n&&n--}return n},g=function(e){if(e&&e.children&&"a"===e.delta._t){for(var t,r=e.children.length,n={_t:"a"},o=0;o<r;o++){var i=(t=e.children[o]).newName;void 0===i&&(i=m(e.delta,t.childName,t.result)),n[i]!==t.result&&(n[i]=t.result)}e.setResult(n).exit()}};g.filterName="arraysCollectChildren",t.diffFilter=c,t.patchFilter=d,t.collectChildrenPatchFilter=h,t.reverseFilter=y,t.collectChildrenReverseFilter=g},1634:function(e,t){var r=function(e){e.left instanceof Date?(e.right instanceof Date?e.left.getTime()!==e.right.getTime()?e.setResult([e.left,e.right]):e.setResult(void 0):e.setResult([e.left,e.right]),e.exit()):e.right instanceof Date&&e.setResult([e.left,e.right]).exit()};r.filterName="dates",t.diffFilter=r},7821:function(e,t){var r=function(e,t,r,n){return e[r]===t[n]},n=function(e,t,r,o,i,a){if(0===o||0===i)return{sequence:[],indices1:[],indices2:[]};if(e.match(t,r,o-1,i-1,a)){var s=n(e,t,r,o-1,i-1,a);return s.sequence.push(t[o-1]),s.indices1.push(o-1),s.indices2.push(i-1),s}return e[o][i-1]>e[o-1][i]?n(e,t,r,o,i-1,a):n(e,t,r,o-1,i,a)};t.get=function(e,t,o,i){var a=function(e,t,r,n){var o,i,a=e.length,s=t.length,u=[a+1];for(o=0;o<a+1;o++)for(u[o]=[s+1],i=0;i<s+1;i++)u[o][i]=0;for(u.match=r,o=1;o<a+1;o++)for(i=1;i<s+1;i++)r(e,t,o-1,i-1,n)?u[o][i]=u[o-1][i-1]+1:u[o][i]=Math.max(u[o-1][i],u[o][i-1]);return u}(e,t,o||r,i=i||{}),s=n(a,e,t,e.length,t.length,i);return"string"==typeof e&&"string"==typeof t&&(s.sequence=s.sequence.join("")),s}},1104:function(e,t,r){var n=r(9637).A,o=r(7443).f,i=r(4153).V,a=function(e){if(e&&e.children){for(var t,r=e.children.length,n=e.result,o=0;o<r;o++)void 0!==(t=e.children[o]).result&&((n=n||{})[t.childName]=t.result);n&&e.leftIsArray&&(n._t="a"),e.setResult(n).exit()}};a.filterName="collectChildren";var s=function(e){if(!e.leftIsArray&&"object"===e.leftType){var t,r,o=e.options.propertyFilter;for(t in e.left)Object.prototype.hasOwnProperty.call(e.left,t)&&(o&&!o(t,e)||(r=new n(e.left[t],e.right[t]),e.push(r,t)));for(t in e.right)Object.prototype.hasOwnProperty.call(e.right,t)&&(o&&!o(t,e)||void 0===e.left[t]&&(r=new n(void 0,e.right[t]),e.push(r,t)));e.children&&0!==e.children.length?e.exit():e.setResult(void 0).exit()}};s.filterName="objects";var u=function(e){if(e.nested&&!e.delta._t){var t,r;for(t in e.delta)r=new o(e.left[t],e.delta[t]),e.push(r,t);e.exit()}};u.filterName="objects";var l=function(e){if(e&&e.children&&!e.delta._t){for(var t,r=e.children.length,n=0;n<r;n++)t=e.children[n],Object.prototype.hasOwnProperty.call(e.left,t.childName)&&void 0===t.result?delete e.left[t.childName]:e.left[t.childName]!==t.result&&(e.left[t.childName]=t.result);e.setResult(e.left).exit()}};l.filterName="collectChildren";var c=function(e){if(e.nested&&!e.delta._t){var t,r;for(t in e.delta)r=new i(e.delta[t]),e.push(r,t);e.exit()}};c.filterName="objects";var f=function(e){if(e&&e.children&&!e.delta._t){for(var t,r=e.children.length,n={},o=0;o<r;o++)n[(t=e.children[o]).childName]!==t.result&&(n[t.childName]=t.result);e.setResult(n).exit()}};f.filterName="collectChildren",t.collectChildrenDiffFilter=a,t.objectsDiffFilter=s,t.patchFilter=u,t.collectChildrenPatchFilter=l,t.reverseFilter=c,t.collectChildrenReverseFilter=f},328:function(e,t,r){var n=null,o=function(e){if(!n){var t;if("undefined"!=typeof diff_match_patch)t="function"==typeof diff_match_patch?new diff_match_patch:new diff_match_patch.diff_match_patch;else{try{var o=r(3538)("./diff_match_patch_uncompressed");t=new o.diff_match_patch}catch(e){t=null}}if(!t){if(!e)return null;var i=new Error("text diff_match_patch library not found");throw i.diff_match_patch_not_found=!0,i}n={diff:function(e,r){return t.patch_toText(t.patch_make(e,r))},patch:function(e,r){for(var n=t.patch_apply(t.patch_fromText(r),e),o=0;o<n[1].length;o++){if(!n[1][o])new Error("text patch failed").textPatchFailed=!0}return n[0]}}}return n},i=function(e){if("string"===e.leftType){var t=e.options&&e.options.textDiff&&e.options.textDiff.minLength||60;if(e.left.length<t||e.right.length<t)e.setResult([e.left,e.right]).exit();else{var r=o();if(r){var n=r.diff;e.setResult([n(e.left,e.right),0,2]).exit()}else e.setResult([e.left,e.right]).exit()}}};i.filterName="texts";var a=function(e){if(!e.nested&&2===e.delta[2]){var t=o(!0).patch;e.setResult(t(e.left,e.delta[0])).exit()}};a.filterName="texts";var s=function(e){var t,r,n,o,i,a=null,s=/^@@ +\-(\d+),(\d+) +\+(\d+),(\d+) +@@$/;for(t=0,r=(n=e.split("\n")).length;t<r;t++){var u=(o=n[t]).slice(0,1);"@"===u?(a=s.exec(o),null,null,n[t]="@@ -"+a[3]+","+a[4]+" +"+a[1]+","+a[2]+" @@"):"+"===u?(t,n[t]="-"+n[t].slice(1),"+"===n[t-1].slice(0,1)&&(i=n[t],n[t]=n[t-1],n[t-1]=i)):"-"===u&&(t,n[t]="+"+n[t].slice(1))}return n.join("\n")},u=function(e){e.nested||2===e.delta[2]&&e.setResult([s(e.delta[0]),0,2]).exit()};u.filterName="texts",t.diffFilter=i,t.patchFilter=a,t.reverseFilter=u},6294:function(e,t){var r="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array},n=function(e){if(e.left!==e.right)if(void 0!==e.left)if(void 0!==e.right){if("function"==typeof e.left||"function"==typeof e.right)throw new Error("functions are not supported");if(e.leftType=null===e.left?"null":typeof e.left,e.rightType=null===e.right?"null":typeof e.right,e.leftType===e.rightType)if("boolean"!==e.leftType&&"number"!==e.leftType)if("object"===e.leftType&&(e.leftIsArray=r(e.left)),"object"===e.rightType&&(e.rightIsArray=r(e.right)),e.leftIsArray===e.rightIsArray){if(e.left instanceof RegExp){if(!(e.right instanceof RegExp))return void e.setResult([e.left,e.right]).exit();e.setResult([e.left.toString(),e.right.toString()]).exit()}}else e.setResult([e.left,e.right]).exit();else e.setResult([e.left,e.right]).exit();else e.setResult([e.left,e.right]).exit()}else e.setResult([e.left,0,0]).exit();else{if("function"==typeof e.right)throw new Error("functions are not supported");e.setResult([e.right]).exit()}else e.setResult(void 0).exit()};n.filterName="trivial";var o=function(e){if(void 0!==e.delta){if(e.nested=!r(e.delta),!e.nested)if(1!==e.delta.length)if(2!==e.delta.length)3!==e.delta.length||0!==e.delta[2]||e.setResult(void 0).exit();else{if(e.left instanceof RegExp){var t=/^\/(.*)\/([gimyu]+)$/.exec(e.delta[1]);if(t)return void e.setResult(new RegExp(t[1],t[2])).exit()}e.setResult(e.delta[1]).exit()}else e.setResult(e.delta[0]).exit()}else e.setResult(e.left).exit()};o.filterName="trivial";var i=function(e){void 0!==e.delta?(e.nested=!r(e.delta),e.nested||(1!==e.delta.length?2!==e.delta.length?3!==e.delta.length||0!==e.delta[2]||e.setResult([e.delta[0]]).exit():e.setResult([e.delta[1],e.delta[0]]).exit():e.setResult([e.delta[0],0,0]).exit())):e.setResult(e.delta).exit()};i.filterName="trivial",t.diffFilter=n,t.patchFilter=o,t.reverseFilter=i},5820:function(e,t){var r=function(e){this.name=e,this.filters=[]};r.prototype.process=function(e){if(!this.processor)throw new Error("add this pipe to a processor before using it");for(var t=this.debug,r=this.filters.length,n=e,o=0;o<r;o++){var i=this.filters[o];if(t&&this.log("filter: "+i.filterName),i(n),"object"==typeof n&&n.exiting){n.exiting=!1;break}}!n.next&&this.resultCheck&&this.resultCheck(n)},r.prototype.log=function(e){console.log("[jsondiffpatch] "+this.name+" pipe, "+e)},r.prototype.append=function(){return this.filters.push.apply(this.filters,arguments),this},r.prototype.prepend=function(){return this.filters.unshift.apply(this.filters,arguments),this},r.prototype.indexOf=function(e){if(!e)throw new Error("a filter name is required");for(var t=0;t<this.filters.length;t++){if(this.filters[t].filterName===e)return t}throw new Error("filter not found: "+e)},r.prototype.list=function(){for(var e=[],t=0;t<this.filters.length;t++){var r=this.filters[t];e.push(r.filterName)}return e},r.prototype.after=function(e){var t=this.indexOf(e),r=Array.prototype.slice.call(arguments,1);if(!r.length)throw new Error("a filter is required");return r.unshift(t+1,0),Array.prototype.splice.apply(this.filters,r),this},r.prototype.before=function(e){var t=this.indexOf(e),r=Array.prototype.slice.call(arguments,1);if(!r.length)throw new Error("a filter is required");return r.unshift(t,0),Array.prototype.splice.apply(this.filters,r),this},r.prototype.clear=function(){return this.filters.length=0,this},r.prototype.shouldHaveResult=function(e){if(!1!==e){if(!this.resultCheck){var t=this;return this.resultCheck=function(e){if(!e.hasResult){console.log(e);var r=new Error(t.name+" failed");throw r.noResult=!0,r}},this}}else this.resultCheck=null},t.E=r},7854:function(e,t){var r=function(e){this.selfOptions=e||{},this.pipes={}};r.prototype.options=function(e){return e&&(this.selfOptions=e),this.selfOptions},r.prototype.pipe=function(e,t){if("string"==typeof e){if(void 0===t)return this.pipes[e];this.pipes[e]=t}if(e&&e.name){if((t=e).processor===this)return t;this.pipes[t.name]=t}return t.processor=this,t},r.prototype.process=function(e,t){var r=e;r.options=this.options();for(var n,o,i=t||e.pipe||"default";i;)void 0!==r.nextAfterChildren&&(r.next=r.nextAfterChildren,r.nextAfterChildren=null),"string"==typeof i&&(i=this.pipe(i)),i.process(r),o=r,n=i,i=null,r&&r.next&&(r=r.next,i=o.nextPipe||r.pipe||n);return r.hasResult?r.result:void 0},t.w=r},6739:function(e,t,r){"use strict";var n,o,i,a,s,u,l,c,f,p,d,h,y,m=r(6558),g=m.Writer,O=m.util,b=m.roots.default||(m.roots.default={});b.lr=((y={}).IntercomShow=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.encode=function(e,t){return t||(t=g.create()),t},e.fromObject=function(e){return e instanceof b.lr.IntercomShow?e:new b.lr.IntercomShow},e.toObject=function(){return{}},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.ZendeskShow=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.encode=function(e,t){return t||(t=g.create()),t},e.fromObject=function(e){return e instanceof b.lr.ZendeskShow?e:new b.lr.ZendeskShow},e.toObject=function(){return{}},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.utils=((n={}).Value=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.arson="",e.encode=function(e,t){return t||(t=g.create()),null!=e.arson&&e.hasOwnProperty("arson")&&t.uint32(18).string(e.arson),t},e.fromObject=function(e){if(e instanceof b.lr.utils.Value)return e;var t=new b.lr.utils.Value;return null!=e.arson&&(t.arson=String(e.arson)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.arson=""),null!=e.arson&&e.hasOwnProperty("arson")&&(r.arson=e.arson),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),n),y.core=((o={}).LogEvent=function(){function e(e){if(this.args=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t,r;return e.prototype.logLevel=0,e.prototype.args=O.emptyArray,e.prototype.tag="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.logLevel&&e.hasOwnProperty("logLevel")&&t.uint32(8).int32(e.logLevel),null!=e.args&&e.args.length)for(var r=0;r<e.args.length;++r)b.lr.utils.Value.encode(e.args[r],t.uint32(18).fork()).ldelim();return null!=e.tag&&e.hasOwnProperty("tag")&&t.uint32(26).string(e.tag),t},e.fromObject=function(e){if(e instanceof b.lr.core.LogEvent)return e;var t=new b.lr.core.LogEvent;switch(e.logLevel){case"DEBUG":case 0:t.logLevel=0;break;case"INFO":case 1:t.logLevel=1;break;case"LOG":case 2:t.logLevel=2;break;case"WARN":case 3:t.logLevel=3;break;case"ERROR":case 4:t.logLevel=4}if(e.args){if(!Array.isArray(e.args))throw TypeError(".lr.core.LogEvent.args: array expected");t.args=[];for(var r=0;r<e.args.length;++r){if("object"!=typeof e.args[r])throw TypeError(".lr.core.LogEvent.args: object expected");t.args[r]=b.lr.utils.Value.fromObject(e.args[r])}}return null!=e.tag&&(t.tag=String(e.tag)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.args=[]),t.defaults&&(r.logLevel=t.enums===String?"DEBUG":0,r.tag=""),null!=e.logLevel&&e.hasOwnProperty("logLevel")&&(r.logLevel=t.enums===String?b.lr.core.LogEvent.LogLevel[e.logLevel]:e.logLevel),e.args&&e.args.length){r.args=[];for(var n=0;n<e.args.length;++n)r.args[n]=b.lr.utils.Value.toObject(e.args[n],t)}return null!=e.tag&&e.hasOwnProperty("tag")&&(r.tag=e.tag),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.LogLevel=(t={},(r=Object.create(t))[t[0]="DEBUG"]=0,r[t[1]="INFO"]=1,r[t[2]="LOG"]=2,r[t[3]="WARN"]=3,r[t[4]="ERROR"]=4,r),e}(),o.OldException=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.city="",e.encode=function(e,t){return t||(t=g.create()),null!=e.city&&e.hasOwnProperty("city")&&t.uint32(90).string(e.city),t},e.fromObject=function(e){if(e instanceof b.lr.core.OldException)return e;var t=new b.lr.core.OldException;return null!=e.city&&(t.city=String(e.city)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.city=""),null!=e.city&&e.hasOwnProperty("city")&&(r.city=e.city),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),o.Exception=function(){function e(e){if(this.tags={},this.extra={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t,r;return e.prototype.exceptionType=0,e.prototype.errorType="",e.prototype.message=null,e.prototype.release="",e.prototype.browserHref="",e.prototype.browserUseragent="",e.prototype.userName="",e.prototype.userEmail="",e.prototype.level="",e.prototype.logger="",e.prototype.tags=O.emptyObject,e.prototype.extra=O.emptyObject,e.prototype.ip="",e.prototype.country="",e.prototype.region="",e.prototype.city="",e.prototype.latitude=0,e.prototype.longitude=0,e.prototype.count=0,e.prototype.hashCode="",e.prototype.messageArgs=null,e.prototype.debugModules=null,e.prototype.isCrash=!1,e.prototype.forceIngest=!1,e.encode=function(e,t){if(t||(t=g.create()),null!=e.exceptionType&&e.hasOwnProperty("exceptionType")&&t.uint32(8).int32(e.exceptionType),null!=e.errorType&&e.hasOwnProperty("errorType")&&t.uint32(18).string(e.errorType),null!=e.message&&e.hasOwnProperty("message")&&b.lr.utils.Value.encode(e.message,t.uint32(26).fork()).ldelim(),null!=e.release&&e.hasOwnProperty("release")&&t.uint32(34).string(e.release),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&t.uint32(42).string(e.browserHref),null!=e.browserUseragent&&e.hasOwnProperty("browserUseragent")&&t.uint32(50).string(e.browserUseragent),null!=e.userName&&e.hasOwnProperty("userName")&&t.uint32(58).string(e.userName),null!=e.userEmail&&e.hasOwnProperty("userEmail")&&t.uint32(66).string(e.userEmail),null!=e.level&&e.hasOwnProperty("level")&&t.uint32(74).string(e.level),null!=e.logger&&e.hasOwnProperty("logger")&&t.uint32(82).string(e.logger),null!=e.tags&&e.hasOwnProperty("tags"))for(var r=Object.keys(e.tags),n=0;n<r.length;++n)t.uint32(90).fork().uint32(10).string(r[n]).uint32(18).string(e.tags[r[n]]).ldelim();if(null!=e.extra&&e.hasOwnProperty("extra"))for(r=Object.keys(e.extra),n=0;n<r.length;++n)t.uint32(98).fork().uint32(10).string(r[n]).uint32(18).string(e.extra[r[n]]).ldelim();return null!=e.ip&&e.hasOwnProperty("ip")&&t.uint32(106).string(e.ip),null!=e.country&&e.hasOwnProperty("country")&&t.uint32(114).string(e.country),null!=e.region&&e.hasOwnProperty("region")&&t.uint32(122).string(e.region),null!=e.city&&e.hasOwnProperty("city")&&t.uint32(130).string(e.city),null!=e.latitude&&e.hasOwnProperty("latitude")&&t.uint32(141).float(e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&t.uint32(149).float(e.longitude),null!=e.count&&e.hasOwnProperty("count")&&t.uint32(152).uint32(e.count),null!=e.hashCode&&e.hasOwnProperty("hashCode")&&t.uint32(162).string(e.hashCode),null!=e.messageArgs&&e.hasOwnProperty("messageArgs")&&b.lr.utils.Value.encode(e.messageArgs,t.uint32(170).fork()).ldelim(),null!=e.debugModules&&e.hasOwnProperty("debugModules")&&b.lr.core.Exception.DebugModules.encode(e.debugModules,t.uint32(178).fork()).ldelim(),null!=e.isCrash&&e.hasOwnProperty("isCrash")&&t.uint32(184).bool(e.isCrash),null!=e.forceIngest&&e.hasOwnProperty("forceIngest")&&t.uint32(192).bool(e.forceIngest),t},e.fromObject=function(e){if(e instanceof b.lr.core.Exception)return e;var t=new b.lr.core.Exception;switch(e.exceptionType){case"UNHANDLED_REJECTION":case 0:t.exceptionType=0;break;case"WINDOW":case 1:t.exceptionType=1;break;case"MESSAGE":case 2:t.exceptionType=2;break;case"CONSOLE":case 3:t.exceptionType=3;break;case"ANDROID":case 4:t.exceptionType=4;break;case"IOS":case 5:t.exceptionType=5}if(null!=e.errorType&&(t.errorType=String(e.errorType)),null!=e.message){if("object"!=typeof e.message)throw TypeError(".lr.core.Exception.message: object expected");t.message=b.lr.utils.Value.fromObject(e.message)}if(null!=e.release&&(t.release=String(e.release)),null!=e.browserHref&&(t.browserHref=String(e.browserHref)),null!=e.browserUseragent&&(t.browserUseragent=String(e.browserUseragent)),null!=e.userName&&(t.userName=String(e.userName)),null!=e.userEmail&&(t.userEmail=String(e.userEmail)),null!=e.level&&(t.level=String(e.level)),null!=e.logger&&(t.logger=String(e.logger)),e.tags){if("object"!=typeof e.tags)throw TypeError(".lr.core.Exception.tags: object expected");t.tags={};for(var r=Object.keys(e.tags),n=0;n<r.length;++n)t.tags[r[n]]=String(e.tags[r[n]])}if(e.extra){if("object"!=typeof e.extra)throw TypeError(".lr.core.Exception.extra: object expected");for(t.extra={},r=Object.keys(e.extra),n=0;n<r.length;++n)t.extra[r[n]]=String(e.extra[r[n]])}if(null!=e.ip&&(t.ip=String(e.ip)),null!=e.country&&(t.country=String(e.country)),null!=e.region&&(t.region=String(e.region)),null!=e.city&&(t.city=String(e.city)),null!=e.latitude&&(t.latitude=Number(e.latitude)),null!=e.longitude&&(t.longitude=Number(e.longitude)),null!=e.count&&(t.count=e.count>>>0),null!=e.hashCode&&(t.hashCode=String(e.hashCode)),null!=e.messageArgs){if("object"!=typeof e.messageArgs)throw TypeError(".lr.core.Exception.messageArgs: object expected");t.messageArgs=b.lr.utils.Value.fromObject(e.messageArgs)}if(null!=e.debugModules){if("object"!=typeof e.debugModules)throw TypeError(".lr.core.Exception.debugModules: object expected");t.debugModules=b.lr.core.Exception.DebugModules.fromObject(e.debugModules)}return null!=e.isCrash&&(t.isCrash=Boolean(e.isCrash)),null!=e.forceIngest&&(t.forceIngest=Boolean(e.forceIngest)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.tags={},n.extra={}),t.defaults&&(n.exceptionType=t.enums===String?"UNHANDLED_REJECTION":0,n.errorType="",n.message=null,n.release="",n.browserHref="",n.browserUseragent="",n.userName="",n.userEmail="",n.level="",n.logger="",n.ip="",n.country="",n.region="",n.city="",n.latitude=0,n.longitude=0,n.count=0,n.hashCode="",n.messageArgs=null,n.debugModules=null,n.isCrash=!1,n.forceIngest=!1),null!=e.exceptionType&&e.hasOwnProperty("exceptionType")&&(n.exceptionType=t.enums===String?b.lr.core.Exception.ExceptionType[e.exceptionType]:e.exceptionType),null!=e.errorType&&e.hasOwnProperty("errorType")&&(n.errorType=e.errorType),null!=e.message&&e.hasOwnProperty("message")&&(n.message=b.lr.utils.Value.toObject(e.message,t)),null!=e.release&&e.hasOwnProperty("release")&&(n.release=e.release),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&(n.browserHref=e.browserHref),null!=e.browserUseragent&&e.hasOwnProperty("browserUseragent")&&(n.browserUseragent=e.browserUseragent),null!=e.userName&&e.hasOwnProperty("userName")&&(n.userName=e.userName),null!=e.userEmail&&e.hasOwnProperty("userEmail")&&(n.userEmail=e.userEmail),null!=e.level&&e.hasOwnProperty("level")&&(n.level=e.level),null!=e.logger&&e.hasOwnProperty("logger")&&(n.logger=e.logger),e.tags&&(r=Object.keys(e.tags)).length){n.tags={};for(var o=0;o<r.length;++o)n.tags[r[o]]=e.tags[r[o]]}if(e.extra&&(r=Object.keys(e.extra)).length)for(n.extra={},o=0;o<r.length;++o)n.extra[r[o]]=e.extra[r[o]];return null!=e.ip&&e.hasOwnProperty("ip")&&(n.ip=e.ip),null!=e.country&&e.hasOwnProperty("country")&&(n.country=e.country),null!=e.region&&e.hasOwnProperty("region")&&(n.region=e.region),null!=e.city&&e.hasOwnProperty("city")&&(n.city=e.city),null!=e.latitude&&e.hasOwnProperty("latitude")&&(n.latitude=t.json&&!isFinite(e.latitude)?String(e.latitude):e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&(n.longitude=t.json&&!isFinite(e.longitude)?String(e.longitude):e.longitude),null!=e.count&&e.hasOwnProperty("count")&&(n.count=e.count),null!=e.hashCode&&e.hasOwnProperty("hashCode")&&(n.hashCode=e.hashCode),null!=e.messageArgs&&e.hasOwnProperty("messageArgs")&&(n.messageArgs=b.lr.utils.Value.toObject(e.messageArgs,t)),null!=e.debugModules&&e.hasOwnProperty("debugModules")&&(n.debugModules=b.lr.core.Exception.DebugModules.toObject(e.debugModules,t)),null!=e.isCrash&&e.hasOwnProperty("isCrash")&&(n.isCrash=e.isCrash),null!=e.forceIngest&&e.hasOwnProperty("forceIngest")&&(n.forceIngest=e.forceIngest),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.ExceptionType=(t={},(r=Object.create(t))[t[0]="UNHANDLED_REJECTION"]=0,r[t[1]="WINDOW"]=1,r[t[2]="MESSAGE"]=2,r[t[3]="CONSOLE"]=3,r[t[4]="ANDROID"]=4,r[t[5]="IOS"]=5,r),e.DebugModules=function(){function e(e){if(this.modules=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.type=0,e.prototype.architecture="",e.prototype.modules=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.type&&e.hasOwnProperty("type")&&t.uint32(8).int32(e.type),null!=e.architecture&&e.hasOwnProperty("architecture")&&t.uint32(18).string(e.architecture),null!=e.modules&&e.modules.length)for(var r=0;r<e.modules.length;++r)b.lr.core.Exception.DebugModules.Module.encode(e.modules[r],t.uint32(26).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.core.Exception.DebugModules)return e;var t=new b.lr.core.Exception.DebugModules;switch(e.type){case"macho":case 0:t.type=0;break;case"pe":case 1:t.type=1;break;case"elf":case 2:t.type=2}if(null!=e.architecture&&(t.architecture=String(e.architecture)),e.modules){if(!Array.isArray(e.modules))throw TypeError(".lr.core.Exception.DebugModules.modules: array expected");t.modules=[];for(var r=0;r<e.modules.length;++r){if("object"!=typeof e.modules[r])throw TypeError(".lr.core.Exception.DebugModules.modules: object expected");t.modules[r]=b.lr.core.Exception.DebugModules.Module.fromObject(e.modules[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.modules=[]),t.defaults&&(r.type=t.enums===String?"macho":0,r.architecture=""),null!=e.type&&e.hasOwnProperty("type")&&(r.type=t.enums===String?b.lr.core.Exception.DebugModules.ModuleType[e.type]:e.type),null!=e.architecture&&e.hasOwnProperty("architecture")&&(r.architecture=e.architecture),e.modules&&e.modules.length){r.modules=[];for(var n=0;n<e.modules.length;++n)r.modules[n]=b.lr.core.Exception.DebugModules.Module.toObject(e.modules[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.ModuleType=function(){var e={},t=Object.create(e);return t[e[0]="macho"]=0,t[e[1]="pe"]=1,t[e[2]="elf"]=2,t}(),e.Module=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.debugId="",e.prototype.debugFile="",e.prototype.imageAddress="",e.encode=function(e,t){return t||(t=g.create()),null!=e.debugId&&e.hasOwnProperty("debugId")&&t.uint32(10).string(e.debugId),null!=e.debugFile&&e.hasOwnProperty("debugFile")&&t.uint32(18).string(e.debugFile),null!=e.imageAddress&&e.hasOwnProperty("imageAddress")&&t.uint32(26).string(e.imageAddress),t},e.fromObject=function(e){if(e instanceof b.lr.core.Exception.DebugModules.Module)return e;var t=new b.lr.core.Exception.DebugModules.Module;return null!=e.debugId&&(t.debugId=String(e.debugId)),null!=e.debugFile&&(t.debugFile=String(e.debugFile)),null!=e.imageAddress&&(t.imageAddress=String(e.imageAddress)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.debugId="",r.debugFile="",r.imageAddress=""),null!=e.debugId&&e.hasOwnProperty("debugId")&&(r.debugId=e.debugId),null!=e.debugFile&&e.hasOwnProperty("debugFile")&&(r.debugFile=e.debugFile),null!=e.imageAddress&&e.hasOwnProperty("imageAddress")&&(r.imageAddress=e.imageAddress),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e}(),o.PageTitleChange=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.title="",e.encode=function(e,t){return t||(t=g.create()),null!=e.title&&e.hasOwnProperty("title")&&t.uint32(10).string(e.title),t},e.fromObject=function(e){if(e instanceof b.lr.core.PageTitleChange)return e;var t=new b.lr.core.PageTitleChange;return null!=e.title&&(t.title=String(e.title)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.title=""),null!=e.title&&e.hasOwnProperty("title")&&(r.title=e.title),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),o),y.filter=((i={}).ForceMatch=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.id="",e.encode=function(e,t){return t||(t=g.create()),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(10).string(e.id),t},e.fromObject=function(e){if(e instanceof b.lr.filter.ForceMatch)return e;var t=new b.lr.filter.ForceMatch;return null!=e.id&&(t.id=String(e.id)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.id=""),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i.StartTransaction=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.id="",e.encode=function(e,t){return t||(t=g.create()),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(10).string(e.id),t},e.fromObject=function(e){if(e instanceof b.lr.filter.StartTransaction)return e;var t=new b.lr.filter.StartTransaction;return null!=e.id&&(t.id=String(e.id)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.id=""),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i.EndTransaction=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.id="",e.encode=function(e,t){return t||(t=g.create()),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(10).string(e.id),t},e.fromObject=function(e){if(e instanceof b.lr.filter.EndTransaction)return e;var t=new b.lr.filter.EndTransaction;return null!=e.id&&(t.id=String(e.id)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.id=""),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i.Match=function(){function e(e){if(this.uuids=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.uuid="",e.prototype.duration=0,e.prototype.uuids=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.uuid&&e.hasOwnProperty("uuid")&&t.uint32(10).string(e.uuid),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(17).double(e.duration),null!=e.uuids&&e.uuids.length)for(var r=0;r<e.uuids.length;++r)t.uint32(26).string(e.uuids[r]);return t},e.fromObject=function(e){if(e instanceof b.lr.filter.Match)return e;var t=new b.lr.filter.Match;if(null!=e.uuid&&(t.uuid=String(e.uuid)),null!=e.duration&&(t.duration=Number(e.duration)),e.uuids){if(!Array.isArray(e.uuids))throw TypeError(".lr.filter.Match.uuids: array expected");t.uuids=[];for(var r=0;r<e.uuids.length;++r)t.uuids[r]=String(e.uuids[r])}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.uuids=[]),t.defaults&&(r.uuid="",r.duration=0),null!=e.uuid&&e.hasOwnProperty("uuid")&&(r.uuid=e.uuid),null!=e.duration&&e.hasOwnProperty("duration")&&(r.duration=t.json&&!isFinite(e.duration)?String(e.duration):e.duration),e.uuids&&e.uuids.length){r.uuids=[];for(var n=0;n<e.uuids.length;++n)r.uuids[n]=e.uuids[n]}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i.VisibleElement=function(){function e(e){if(this.uuids=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.uuids=O.emptyArray,e.prototype.textContent="",e.prototype.browserHref="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.uuids&&e.uuids.length)for(var r=0;r<e.uuids.length;++r)t.uint32(10).string(e.uuids[r]);return null!=e.textContent&&e.hasOwnProperty("textContent")&&t.uint32(18).string(e.textContent),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&t.uint32(26).string(e.browserHref),t},e.fromObject=function(e){if(e instanceof b.lr.filter.VisibleElement)return e;var t=new b.lr.filter.VisibleElement;if(e.uuids){if(!Array.isArray(e.uuids))throw TypeError(".lr.filter.VisibleElement.uuids: array expected");t.uuids=[];for(var r=0;r<e.uuids.length;++r)t.uuids[r]=String(e.uuids[r])}return null!=e.textContent&&(t.textContent=String(e.textContent)),null!=e.browserHref&&(t.browserHref=String(e.browserHref)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.uuids=[]),t.defaults&&(r.textContent="",r.browserHref=""),e.uuids&&e.uuids.length){r.uuids=[];for(var n=0;n<e.uuids.length;++n)r.uuids[n]=e.uuids[n]}return null!=e.textContent&&e.hasOwnProperty("textContent")&&(r.textContent=e.textContent),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&(r.browserHref=e.browserHref),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i.ErrorState=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.filterHash="",e.prototype.filterType="",e.prototype.browserHref="",e.prototype.referralHref="",e.prototype.textContent="",e.encode=function(e,t){return t||(t=g.create()),null!=e.filterHash&&e.hasOwnProperty("filterHash")&&t.uint32(10).string(e.filterHash),null!=e.filterType&&e.hasOwnProperty("filterType")&&t.uint32(18).string(e.filterType),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&t.uint32(26).string(e.browserHref),null!=e.referralHref&&e.hasOwnProperty("referralHref")&&t.uint32(34).string(e.referralHref),null!=e.textContent&&e.hasOwnProperty("textContent")&&t.uint32(42).string(e.textContent),t},e.fromObject=function(e){if(e instanceof b.lr.filter.ErrorState)return e;var t=new b.lr.filter.ErrorState;return null!=e.filterHash&&(t.filterHash=String(e.filterHash)),null!=e.filterType&&(t.filterType=String(e.filterType)),null!=e.browserHref&&(t.browserHref=String(e.browserHref)),null!=e.referralHref&&(t.referralHref=String(e.referralHref)),null!=e.textContent&&(t.textContent=String(e.textContent)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.filterHash="",r.filterType="",r.browserHref="",r.referralHref="",r.textContent=""),null!=e.filterHash&&e.hasOwnProperty("filterHash")&&(r.filterHash=e.filterHash),null!=e.filterType&&e.hasOwnProperty("filterType")&&(r.filterType=e.filterType),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&(r.browserHref=e.browserHref),null!=e.referralHref&&e.hasOwnProperty("referralHref")&&(r.referralHref=e.referralHref),null!=e.textContent&&e.hasOwnProperty("textContent")&&(r.textContent=e.textContent),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),i),y.feedback=((a={}).FeedbackResponse=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.guideID="",e.prototype.stepID="",e.prototype.userFeedback="",e.prototype.submissionID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.guideID&&e.hasOwnProperty("guideID")&&t.uint32(10).string(e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&t.uint32(18).string(e.stepID),null!=e.userFeedback&&e.hasOwnProperty("userFeedback")&&t.uint32(26).string(e.userFeedback),null!=e.submissionID&&e.hasOwnProperty("submissionID")&&t.uint32(34).string(e.submissionID),t},e.fromObject=function(e){if(e instanceof b.lr.feedback.FeedbackResponse)return e;var t=new b.lr.feedback.FeedbackResponse;return null!=e.guideID&&(t.guideID=String(e.guideID)),null!=e.stepID&&(t.stepID=String(e.stepID)),null!=e.userFeedback&&(t.userFeedback=String(e.userFeedback)),null!=e.submissionID&&(t.submissionID=String(e.submissionID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.guideID="",r.stepID="",r.userFeedback="",r.submissionID=""),null!=e.guideID&&e.hasOwnProperty("guideID")&&(r.guideID=e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&(r.stepID=e.stepID),null!=e.userFeedback&&e.hasOwnProperty("userFeedback")&&(r.userFeedback=e.userFeedback),null!=e.submissionID&&e.hasOwnProperty("submissionID")&&(r.submissionID=e.submissionID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),a.RatingResponse=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.guideID="",e.prototype.stepID="",e.prototype.rating=0,e.prototype.submissionID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.guideID&&e.hasOwnProperty("guideID")&&t.uint32(10).string(e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&t.uint32(18).string(e.stepID),null!=e.rating&&e.hasOwnProperty("rating")&&t.uint32(24).uint32(e.rating),null!=e.submissionID&&e.hasOwnProperty("submissionID")&&t.uint32(34).string(e.submissionID),t},e.fromObject=function(e){if(e instanceof b.lr.feedback.RatingResponse)return e;var t=new b.lr.feedback.RatingResponse;return null!=e.guideID&&(t.guideID=String(e.guideID)),null!=e.stepID&&(t.stepID=String(e.stepID)),null!=e.rating&&(t.rating=e.rating>>>0),null!=e.submissionID&&(t.submissionID=String(e.submissionID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.guideID="",r.stepID="",r.rating=0,r.submissionID=""),null!=e.guideID&&e.hasOwnProperty("guideID")&&(r.guideID=e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&(r.stepID=e.stepID),null!=e.rating&&e.hasOwnProperty("rating")&&(r.rating=e.rating),null!=e.submissionID&&e.hasOwnProperty("submissionID")&&(r.submissionID=e.submissionID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),a),y.guide=((s={}).GuideStepStart=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.guideID="",e.prototype.stepID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.guideID&&e.hasOwnProperty("guideID")&&t.uint32(10).string(e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&t.uint32(18).string(e.stepID),t},e.fromObject=function(e){if(e instanceof b.lr.guide.GuideStepStart)return e;var t=new b.lr.guide.GuideStepStart;return null!=e.guideID&&(t.guideID=String(e.guideID)),null!=e.stepID&&(t.stepID=String(e.stepID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.guideID="",r.stepID=""),null!=e.guideID&&e.hasOwnProperty("guideID")&&(r.guideID=e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&(r.stepID=e.stepID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),s.GuideStepEnd=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.guideID="",e.prototype.stepID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.guideID&&e.hasOwnProperty("guideID")&&t.uint32(10).string(e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&t.uint32(18).string(e.stepID),t},e.fromObject=function(e){if(e instanceof b.lr.guide.GuideStepEnd)return e;var t=new b.lr.guide.GuideStepEnd;return null!=e.guideID&&(t.guideID=String(e.guideID)),null!=e.stepID&&(t.stepID=String(e.stepID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.guideID="",r.stepID=""),null!=e.guideID&&e.hasOwnProperty("guideID")&&(r.guideID=e.guideID),null!=e.stepID&&e.hasOwnProperty("stepID")&&(r.stepID=e.stepID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),s),y.network=((c={}).MethodType=(u={},(l=Object.create(u))[u[0]="GET"]=0,l[u[1]="HEAD"]=1,l[u[2]="POST"]=2,l[u[3]="PUT"]=3,l[u[4]="DELETE"]=4,l[u[5]="CONNECT"]=5,l[u[6]="OPTIONS"]=6,l[u[7]="TRACE"]=7,l[u[8]="PATCH"]=8,l),c.RequestEvent=function(){function e(e){if(this.headers={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.reqId="",e.prototype.url="",e.prototype.headers=O.emptyObject,e.prototype.body=null,e.prototype.method=0,e.prototype.referrer="",e.prototype.mode="",e.prototype.credentials="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.reqId&&e.hasOwnProperty("reqId")&&t.uint32(10).string(e.reqId),null!=e.url&&e.hasOwnProperty("url")&&t.uint32(18).string(e.url),null!=e.headers&&e.hasOwnProperty("headers"))for(var r=Object.keys(e.headers),n=0;n<r.length;++n)t.uint32(26).fork().uint32(10).string(r[n]).uint32(18).string(e.headers[r[n]]).ldelim();return null!=e.body&&e.hasOwnProperty("body")&&b.lr.utils.Value.encode(e.body,t.uint32(34).fork()).ldelim(),null!=e.method&&e.hasOwnProperty("method")&&t.uint32(40).int32(e.method),null!=e.referrer&&e.hasOwnProperty("referrer")&&t.uint32(50).string(e.referrer),null!=e.mode&&e.hasOwnProperty("mode")&&t.uint32(58).string(e.mode),null!=e.credentials&&e.hasOwnProperty("credentials")&&t.uint32(66).string(e.credentials),t},e.fromObject=function(e){if(e instanceof b.lr.network.RequestEvent)return e;var t=new b.lr.network.RequestEvent;if(null!=e.reqId&&(t.reqId=String(e.reqId)),null!=e.url&&(t.url=String(e.url)),e.headers){if("object"!=typeof e.headers)throw TypeError(".lr.network.RequestEvent.headers: object expected");t.headers={};for(var r=Object.keys(e.headers),n=0;n<r.length;++n)t.headers[r[n]]=String(e.headers[r[n]])}if(null!=e.body){if("object"!=typeof e.body)throw TypeError(".lr.network.RequestEvent.body: object expected");t.body=b.lr.utils.Value.fromObject(e.body)}switch(e.method){case"GET":case 0:t.method=0;break;case"HEAD":case 1:t.method=1;break;case"POST":case 2:t.method=2;break;case"PUT":case 3:t.method=3;break;case"DELETE":case 4:t.method=4;break;case"CONNECT":case 5:t.method=5;break;case"OPTIONS":case 6:t.method=6;break;case"TRACE":case 7:t.method=7;break;case"PATCH":case 8:t.method=8}return null!=e.referrer&&(t.referrer=String(e.referrer)),null!=e.mode&&(t.mode=String(e.mode)),null!=e.credentials&&(t.credentials=String(e.credentials)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.headers={}),t.defaults&&(n.reqId="",n.url="",n.body=null,n.method=t.enums===String?"GET":0,n.referrer="",n.mode="",n.credentials=""),null!=e.reqId&&e.hasOwnProperty("reqId")&&(n.reqId=e.reqId),null!=e.url&&e.hasOwnProperty("url")&&(n.url=e.url),e.headers&&(r=Object.keys(e.headers)).length){n.headers={};for(var o=0;o<r.length;++o)n.headers[r[o]]=e.headers[r[o]]}return null!=e.body&&e.hasOwnProperty("body")&&(n.body=b.lr.utils.Value.toObject(e.body,t)),null!=e.method&&e.hasOwnProperty("method")&&(n.method=t.enums===String?b.lr.network.MethodType[e.method]:e.method),null!=e.referrer&&e.hasOwnProperty("referrer")&&(n.referrer=e.referrer),null!=e.mode&&e.hasOwnProperty("mode")&&(n.mode=e.mode),null!=e.credentials&&e.hasOwnProperty("credentials")&&(n.credentials=e.credentials),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),c.ResponseEvent=function(){function e(e){if(this.headers={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.reqId="",e.prototype.status=0,e.prototype.headers=O.emptyObject,e.prototype.body=null,e.prototype.url="",e.prototype.count=0,e.prototype.release="",e.prototype.browserUseragent="",e.prototype.userName="",e.prototype.userEmail="",e.prototype.ip="",e.prototype.method=0,e.prototype.duration=0,e.prototype.operationName="",e.prototype.operationType="",e.prototype.browserHref="",e.prototype.skipAnalyticsIngestion=!1,e.prototype.forceIngest=!1,e.prototype.responseType=0,e.prototype.isGraphqlError=!1,e.encode=function(e,t){if(t||(t=g.create()),null!=e.reqId&&e.hasOwnProperty("reqId")&&t.uint32(10).string(e.reqId),null!=e.status&&e.hasOwnProperty("status")&&t.uint32(16).uint32(e.status),null!=e.headers&&e.hasOwnProperty("headers"))for(var r=Object.keys(e.headers),n=0;n<r.length;++n)t.uint32(26).fork().uint32(10).string(r[n]).uint32(18).string(e.headers[r[n]]).ldelim();return null!=e.body&&e.hasOwnProperty("body")&&b.lr.utils.Value.encode(e.body,t.uint32(34).fork()).ldelim(),null!=e.url&&e.hasOwnProperty("url")&&t.uint32(42).string(e.url),null!=e.count&&e.hasOwnProperty("count")&&t.uint32(48).uint32(e.count),null!=e.release&&e.hasOwnProperty("release")&&t.uint32(58).string(e.release),null!=e.browserUseragent&&e.hasOwnProperty("browserUseragent")&&t.uint32(66).string(e.browserUseragent),null!=e.userName&&e.hasOwnProperty("userName")&&t.uint32(74).string(e.userName),null!=e.userEmail&&e.hasOwnProperty("userEmail")&&t.uint32(82).string(e.userEmail),null!=e.ip&&e.hasOwnProperty("ip")&&t.uint32(90).string(e.ip),null!=e.method&&e.hasOwnProperty("method")&&t.uint32(96).int32(e.method),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(105).double(e.duration),null!=e.operationName&&e.hasOwnProperty("operationName")&&t.uint32(114).string(e.operationName),null!=e.operationType&&e.hasOwnProperty("operationType")&&t.uint32(122).string(e.operationType),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&t.uint32(130).string(e.browserHref),null!=e.skipAnalyticsIngestion&&e.hasOwnProperty("skipAnalyticsIngestion")&&t.uint32(136).bool(e.skipAnalyticsIngestion),null!=e.forceIngest&&e.hasOwnProperty("forceIngest")&&t.uint32(144).bool(e.forceIngest),null!=e.responseType&&e.hasOwnProperty("responseType")&&t.uint32(152).int32(e.responseType),null!=e.isGraphqlError&&e.hasOwnProperty("isGraphqlError")&&t.uint32(160).bool(e.isGraphqlError),t},e.fromObject=function(e){if(e instanceof b.lr.network.ResponseEvent)return e;var t=new b.lr.network.ResponseEvent;if(null!=e.reqId&&(t.reqId=String(e.reqId)),null!=e.status&&(t.status=e.status>>>0),e.headers){if("object"!=typeof e.headers)throw TypeError(".lr.network.ResponseEvent.headers: object expected");t.headers={};for(var r=Object.keys(e.headers),n=0;n<r.length;++n)t.headers[r[n]]=String(e.headers[r[n]])}if(null!=e.body){if("object"!=typeof e.body)throw TypeError(".lr.network.ResponseEvent.body: object expected");t.body=b.lr.utils.Value.fromObject(e.body)}switch(null!=e.url&&(t.url=String(e.url)),null!=e.count&&(t.count=e.count>>>0),null!=e.release&&(t.release=String(e.release)),null!=e.browserUseragent&&(t.browserUseragent=String(e.browserUseragent)),null!=e.userName&&(t.userName=String(e.userName)),null!=e.userEmail&&(t.userEmail=String(e.userEmail)),null!=e.ip&&(t.ip=String(e.ip)),e.method){case"GET":case 0:t.method=0;break;case"HEAD":case 1:t.method=1;break;case"POST":case 2:t.method=2;break;case"PUT":case 3:t.method=3;break;case"DELETE":case 4:t.method=4;break;case"CONNECT":case 5:t.method=5;break;case"OPTIONS":case 6:t.method=6;break;case"TRACE":case 7:t.method=7;break;case"PATCH":case 8:t.method=8}switch(null!=e.duration&&(t.duration=Number(e.duration)),null!=e.operationName&&(t.operationName=String(e.operationName)),null!=e.operationType&&(t.operationType=String(e.operationType)),null!=e.browserHref&&(t.browserHref=String(e.browserHref)),null!=e.skipAnalyticsIngestion&&(t.skipAnalyticsIngestion=Boolean(e.skipAnalyticsIngestion)),null!=e.forceIngest&&(t.forceIngest=Boolean(e.forceIngest)),e.responseType){case"DEFAULT":case 0:t.responseType=0;break;case"BASIC":case 1:t.responseType=1;break;case"CORS":case 2:t.responseType=2;break;case"ERROR":case 3:t.responseType=3;break;case"OPAQUE":case 4:t.responseType=4;break;case"OPAQUEREDIRECT":case 5:t.responseType=5}return null!=e.isGraphqlError&&(t.isGraphqlError=Boolean(e.isGraphqlError)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.headers={}),t.defaults&&(n.reqId="",n.status=0,n.body=null,n.url="",n.count=0,n.release="",n.browserUseragent="",n.userName="",n.userEmail="",n.ip="",n.method=t.enums===String?"GET":0,n.duration=0,n.operationName="",n.operationType="",n.browserHref="",n.skipAnalyticsIngestion=!1,n.forceIngest=!1,n.responseType=t.enums===String?"DEFAULT":0,n.isGraphqlError=!1),null!=e.reqId&&e.hasOwnProperty("reqId")&&(n.reqId=e.reqId),null!=e.status&&e.hasOwnProperty("status")&&(n.status=e.status),e.headers&&(r=Object.keys(e.headers)).length){n.headers={};for(var o=0;o<r.length;++o)n.headers[r[o]]=e.headers[r[o]]}return null!=e.body&&e.hasOwnProperty("body")&&(n.body=b.lr.utils.Value.toObject(e.body,t)),null!=e.url&&e.hasOwnProperty("url")&&(n.url=e.url),null!=e.count&&e.hasOwnProperty("count")&&(n.count=e.count),null!=e.release&&e.hasOwnProperty("release")&&(n.release=e.release),null!=e.browserUseragent&&e.hasOwnProperty("browserUseragent")&&(n.browserUseragent=e.browserUseragent),null!=e.userName&&e.hasOwnProperty("userName")&&(n.userName=e.userName),null!=e.userEmail&&e.hasOwnProperty("userEmail")&&(n.userEmail=e.userEmail),null!=e.ip&&e.hasOwnProperty("ip")&&(n.ip=e.ip),null!=e.method&&e.hasOwnProperty("method")&&(n.method=t.enums===String?b.lr.network.MethodType[e.method]:e.method),null!=e.duration&&e.hasOwnProperty("duration")&&(n.duration=t.json&&!isFinite(e.duration)?String(e.duration):e.duration),null!=e.operationName&&e.hasOwnProperty("operationName")&&(n.operationName=e.operationName),null!=e.operationType&&e.hasOwnProperty("operationType")&&(n.operationType=e.operationType),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&(n.browserHref=e.browserHref),null!=e.skipAnalyticsIngestion&&e.hasOwnProperty("skipAnalyticsIngestion")&&(n.skipAnalyticsIngestion=e.skipAnalyticsIngestion),null!=e.forceIngest&&e.hasOwnProperty("forceIngest")&&(n.forceIngest=e.forceIngest),null!=e.responseType&&e.hasOwnProperty("responseType")&&(n.responseType=t.enums===String?b.lr.network.ResponseEvent.ResponseType[e.responseType]:e.responseType),null!=e.isGraphqlError&&e.hasOwnProperty("isGraphqlError")&&(n.isGraphqlError=e.isGraphqlError),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.ResponseType=function(){var e={},t=Object.create(e);return t[e[0]="DEFAULT"]=0,t[e[1]="BASIC"]=1,t[e[2]="CORS"]=2,t[e[3]="ERROR"]=3,t[e[4]="OPAQUE"]=4,t[e[5]="OPAQUEREDIRECT"]=5,t}(),e}(),c.PerfResourceEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.url="",e.prototype.startTime=0,e.prototype.duration=0,e.prototype.initiatorType=0,e.prototype.status=0,e.prototype.failed=!1,e.prototype.transferSize=O.Long?O.Long.fromBits(0,0,!0):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.url&&e.hasOwnProperty("url")&&t.uint32(10).string(e.url),null!=e.startTime&&e.hasOwnProperty("startTime")&&t.uint32(17).double(e.startTime),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(25).double(e.duration),null!=e.initiatorType&&e.hasOwnProperty("initiatorType")&&t.uint32(32).int32(e.initiatorType),null!=e.status&&e.hasOwnProperty("status")&&t.uint32(40).uint32(e.status),null!=e.failed&&e.hasOwnProperty("failed")&&t.uint32(48).bool(e.failed),null!=e.transferSize&&e.hasOwnProperty("transferSize")&&t.uint32(56).uint64(e.transferSize),t},e.fromObject=function(e){if(e instanceof b.lr.network.PerfResourceEvent)return e;var t=new b.lr.network.PerfResourceEvent;switch(null!=e.url&&(t.url=String(e.url)),null!=e.startTime&&(t.startTime=Number(e.startTime)),null!=e.duration&&(t.duration=Number(e.duration)),e.initiatorType){case"LINK":case 0:t.initiatorType=0;break;case"SCRIPT":case 1:t.initiatorType=1;break;case"CSS":case 2:t.initiatorType=2;break;case"IMG":case 3:t.initiatorType=3;break;case"IMAGE":case 4:t.initiatorType=4;break;case"OTHER":case 5:t.initiatorType=5;break;case"NAVIGATION":case 6:t.initiatorType=6;break;case"TRACK":case 7:t.initiatorType=7;break;case"VIDEO":case 8:t.initiatorType=8}return null!=e.status&&(t.status=e.status>>>0),null!=e.failed&&(t.failed=Boolean(e.failed)),null!=e.transferSize&&(O.Long?(t.transferSize=O.Long.fromValue(e.transferSize)).unsigned=!0:"string"==typeof e.transferSize?t.transferSize=parseInt(e.transferSize,10):"number"==typeof e.transferSize?t.transferSize=e.transferSize:"object"==typeof e.transferSize&&(t.transferSize=new O.LongBits(e.transferSize.low>>>0,e.transferSize.high>>>0).toNumber(!0))),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults)if(r.url="",r.startTime=0,r.duration=0,r.initiatorType=t.enums===String?"LINK":0,r.status=0,r.failed=!1,O.Long){var n=new O.Long(0,0,!0);r.transferSize=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.transferSize=t.longs===String?"0":0;return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),null!=e.startTime&&e.hasOwnProperty("startTime")&&(r.startTime=t.json&&!isFinite(e.startTime)?String(e.startTime):e.startTime),null!=e.duration&&e.hasOwnProperty("duration")&&(r.duration=t.json&&!isFinite(e.duration)?String(e.duration):e.duration),null!=e.initiatorType&&e.hasOwnProperty("initiatorType")&&(r.initiatorType=t.enums===String?b.lr.network.PerfResourceEvent.InitiatorType[e.initiatorType]:e.initiatorType),null!=e.status&&e.hasOwnProperty("status")&&(r.status=e.status),null!=e.failed&&e.hasOwnProperty("failed")&&(r.failed=e.failed),null!=e.transferSize&&e.hasOwnProperty("transferSize")&&("number"==typeof e.transferSize?r.transferSize=t.longs===String?String(e.transferSize):e.transferSize:r.transferSize=t.longs===String?O.Long.prototype.toString.call(e.transferSize):t.longs===Number?new O.LongBits(e.transferSize.low>>>0,e.transferSize.high>>>0).toNumber(!0):e.transferSize),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.InitiatorType=function(){var e={},t=Object.create(e);return t[e[0]="LINK"]=0,t[e[1]="SCRIPT"]=1,t[e[2]="CSS"]=2,t[e[3]="IMG"]=3,t[e[4]="IMAGE"]=4,t[e[5]="OTHER"]=5,t[e[6]="NAVIGATION"]=6,t[e[7]="TRACK"]=7,t[e[8]="VIDEO"]=8,t}(),e}(),c.NetworkStatusEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.online=!1,e.prototype.effectiveType=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.online&&e.hasOwnProperty("online")&&t.uint32(8).bool(e.online),null!=e.effectiveType&&e.hasOwnProperty("effectiveType")&&t.uint32(16).int32(e.effectiveType),t},e.fromObject=function(e){if(e instanceof b.lr.network.NetworkStatusEvent)return e;var t=new b.lr.network.NetworkStatusEvent;switch(null!=e.online&&(t.online=Boolean(e.online)),e.effectiveType){case"UNKNOWN":case 0:t.effectiveType=0;break;case"NONE":case 1:t.effectiveType=1;break;case"SLOW2G":case 2:t.effectiveType=2;break;case"TWOG":case 3:t.effectiveType=3;break;case"THREEG":case 4:t.effectiveType=4;break;case"FOURG":case 5:t.effectiveType=5}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.online=!1,r.effectiveType=t.enums===String?"UNKNOWN":0),null!=e.online&&e.hasOwnProperty("online")&&(r.online=e.online),null!=e.effectiveType&&e.hasOwnProperty("effectiveType")&&(r.effectiveType=t.enums===String?b.lr.network.NetworkStatusEvent.EffectiveType[e.effectiveType]:e.effectiveType),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.EffectiveType=function(){var e={},t=Object.create(e);return t[e[0]="UNKNOWN"]=0,t[e[1]="NONE"]=1,t[e[2]="SLOW2G"]=2,t[e[3]="TWOG"]=3,t[e[4]="THREEG"]=4,t[e[5]="FOURG"]=5,t}(),e}(),c),y.browser=((f={}).LoadEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.DOM=!1,e.prototype.load=!1,Object.defineProperty(e.prototype,"type",{get:O.oneOfGetter(t=["DOM","load"]),set:O.oneOfSetter(t)}),e.encode=function(e,t){return t||(t=g.create()),null!=e.DOM&&e.hasOwnProperty("DOM")&&t.uint32(8).bool(e.DOM),null!=e.load&&e.hasOwnProperty("load")&&t.uint32(16).bool(e.load),t},e.fromObject=function(e){if(e instanceof b.lr.browser.LoadEvent)return e;var t=new b.lr.browser.LoadEvent;return null!=e.DOM&&(t.DOM=Boolean(e.DOM)),null!=e.load&&(t.load=Boolean(e.load)),t},e.toObject=function(e,t){t||(t={});var r={};return null!=e.DOM&&e.hasOwnProperty("DOM")&&(r.DOM=e.DOM,t.oneofs&&(r.type="DOM")),null!=e.load&&e.hasOwnProperty("load")&&(r.load=e.load,t.oneofs&&(r.type="load")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.UnloadEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.time=0,e.prototype.sessionID=0,e.prototype.tabID="",e.prototype.threadID=0,e.prototype.seqID=0,e.prototype.startTime=0,e.prototype.activityPings=0,e.prototype.eventCount=0,e.prototype.pendingReadFailed=!1,e.prototype.pendingEventCount=0,e.prototype.pendingSeqID=0,e.prototype.didSkipSerializingEvents=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.time&&e.hasOwnProperty("time")&&t.uint32(9).double(e.time),null!=e.sessionID&&e.hasOwnProperty("sessionID")&&t.uint32(16).uint32(e.sessionID),null!=e.tabID&&e.hasOwnProperty("tabID")&&t.uint32(26).string(e.tabID),null!=e.threadID&&e.hasOwnProperty("threadID")&&t.uint32(32).uint32(e.threadID),null!=e.seqID&&e.hasOwnProperty("seqID")&&t.uint32(40).uint32(e.seqID),null!=e.startTime&&e.hasOwnProperty("startTime")&&t.uint32(49).double(e.startTime),null!=e.activityPings&&e.hasOwnProperty("activityPings")&&t.uint32(56).uint32(e.activityPings),null!=e.eventCount&&e.hasOwnProperty("eventCount")&&t.uint32(64).uint32(e.eventCount),null!=e.pendingReadFailed&&e.hasOwnProperty("pendingReadFailed")&&t.uint32(72).bool(e.pendingReadFailed),null!=e.pendingEventCount&&e.hasOwnProperty("pendingEventCount")&&t.uint32(80).int32(e.pendingEventCount),null!=e.pendingSeqID&&e.hasOwnProperty("pendingSeqID")&&t.uint32(88).int32(e.pendingSeqID),null!=e.didSkipSerializingEvents&&e.hasOwnProperty("didSkipSerializingEvents")&&t.uint32(96).bool(e.didSkipSerializingEvents),t},e.fromObject=function(e){if(e instanceof b.lr.browser.UnloadEvent)return e;var t=new b.lr.browser.UnloadEvent;return null!=e.time&&(t.time=Number(e.time)),null!=e.sessionID&&(t.sessionID=e.sessionID>>>0),null!=e.tabID&&(t.tabID=String(e.tabID)),null!=e.threadID&&(t.threadID=e.threadID>>>0),null!=e.seqID&&(t.seqID=e.seqID>>>0),null!=e.startTime&&(t.startTime=Number(e.startTime)),null!=e.activityPings&&(t.activityPings=e.activityPings>>>0),null!=e.eventCount&&(t.eventCount=e.eventCount>>>0),null!=e.pendingReadFailed&&(t.pendingReadFailed=Boolean(e.pendingReadFailed)),null!=e.pendingEventCount&&(t.pendingEventCount=0|e.pendingEventCount),null!=e.pendingSeqID&&(t.pendingSeqID=0|e.pendingSeqID),null!=e.didSkipSerializingEvents&&(t.didSkipSerializingEvents=Boolean(e.didSkipSerializingEvents)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.time=0,r.sessionID=0,r.tabID="",r.threadID=0,r.seqID=0,r.startTime=0,r.activityPings=0,r.eventCount=0,r.pendingReadFailed=!1,r.pendingEventCount=0,r.pendingSeqID=0,r.didSkipSerializingEvents=!1),null!=e.time&&e.hasOwnProperty("time")&&(r.time=t.json&&!isFinite(e.time)?String(e.time):e.time),null!=e.sessionID&&e.hasOwnProperty("sessionID")&&(r.sessionID=e.sessionID),null!=e.tabID&&e.hasOwnProperty("tabID")&&(r.tabID=e.tabID),null!=e.threadID&&e.hasOwnProperty("threadID")&&(r.threadID=e.threadID),null!=e.seqID&&e.hasOwnProperty("seqID")&&(r.seqID=e.seqID),null!=e.startTime&&e.hasOwnProperty("startTime")&&(r.startTime=t.json&&!isFinite(e.startTime)?String(e.startTime):e.startTime),null!=e.activityPings&&e.hasOwnProperty("activityPings")&&(r.activityPings=e.activityPings),null!=e.eventCount&&e.hasOwnProperty("eventCount")&&(r.eventCount=e.eventCount),null!=e.pendingReadFailed&&e.hasOwnProperty("pendingReadFailed")&&(r.pendingReadFailed=e.pendingReadFailed),null!=e.pendingEventCount&&e.hasOwnProperty("pendingEventCount")&&(r.pendingEventCount=e.pendingEventCount),null!=e.pendingSeqID&&e.hasOwnProperty("pendingSeqID")&&(r.pendingSeqID=e.pendingSeqID),null!=e.didSkipSerializingEvents&&e.hasOwnProperty("didSkipSerializingEvents")&&(r.didSkipSerializingEvents=e.didSkipSerializingEvents),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.NavigationEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.href="",e.prototype.navigationType=0,e.prototype.perfNow=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(10).string(e.href),null!=e.navigationType&&e.hasOwnProperty("navigationType")&&t.uint32(16).int32(e.navigationType),null!=e.perfNow&&e.hasOwnProperty("perfNow")&&t.uint32(25).double(e.perfNow),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NavigationEvent)return e;var t=new b.lr.browser.NavigationEvent;switch(null!=e.href&&(t.href=String(e.href)),e.navigationType){case"PAGE_LOAD":case 0:t.navigationType=0;break;case"POP_STATE":case 1:t.navigationType=1;break;case"PUSH_STATE":case 2:t.navigationType=2;break;case"REPLACE_STATE":case 3:t.navigationType=3}return null!=e.perfNow&&(t.perfNow=Number(e.perfNow)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.href="",r.navigationType=t.enums===String?"PAGE_LOAD":0,r.perfNow=0),null!=e.href&&e.hasOwnProperty("href")&&(r.href=e.href),null!=e.navigationType&&e.hasOwnProperty("navigationType")&&(r.navigationType=t.enums===String?b.lr.browser.NavigationEvent.NavigationType[e.navigationType]:e.navigationType),null!=e.perfNow&&e.hasOwnProperty("perfNow")&&(r.perfNow=t.json&&!isFinite(e.perfNow)?String(e.perfNow):e.perfNow),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.NavigationType=function(){var e={},t=Object.create(e);return t[e[0]="PAGE_LOAD"]=0,t[e[1]="POP_STATE"]=1,t[e[2]="PUSH_STATE"]=2,t[e[3]="REPLACE_STATE"]=3,t}(),e}(),f.KeypressEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.key="",e.prototype.domTarget="",e.encode=function(e,t){return t||(t=g.create()),null!=e.key&&e.hasOwnProperty("key")&&t.uint32(10).string(e.key),null!=e.domTarget&&e.hasOwnProperty("domTarget")&&t.uint32(18).string(e.domTarget),t},e.fromObject=function(e){if(e instanceof b.lr.browser.KeypressEvent)return e;var t=new b.lr.browser.KeypressEvent;return null!=e.key&&(t.key=String(e.key)),null!=e.domTarget&&(t.domTarget=String(e.domTarget)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.key="",r.domTarget=""),null!=e.key&&e.hasOwnProperty("key")&&(r.key=e.key),null!=e.domTarget&&e.hasOwnProperty("domTarget")&&(r.domTarget=e.domTarget),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.InputEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.text="",e.prototype.isChecked=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.text&&e.hasOwnProperty("text")&&t.uint32(18).string(e.text),null!=e.isChecked&&e.hasOwnProperty("isChecked")&&t.uint32(24).bool(e.isChecked),t},e.fromObject=function(e){if(e instanceof b.lr.browser.InputEvent)return e;var t=new b.lr.browser.InputEvent;return null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.text&&(t.text=String(e.text)),null!=e.isChecked&&(t.isChecked=Boolean(e.isChecked)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.nodeId=0,r.text="",r.isChecked=!1),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),null!=e.text&&e.hasOwnProperty("text")&&(r.text=e.text),null!=e.isChecked&&e.hasOwnProperty("isChecked")&&(r.isChecked=e.isChecked),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.Selector=function(){function e(e){if(this.classList=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeName="",e.prototype.id="",e.prototype.classList=O.emptyArray,e.prototype.nthChild=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeName&&e.hasOwnProperty("nodeName")&&t.uint32(10).string(e.nodeName),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(18).string(e.id),null!=e.classList&&e.classList.length)for(var r=0;r<e.classList.length;++r)t.uint32(26).string(e.classList[r]);return null!=e.nthChild&&e.hasOwnProperty("nthChild")&&t.uint32(32).int32(e.nthChild),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Selector)return e;var t=new b.lr.browser.Selector;if(null!=e.nodeName&&(t.nodeName=String(e.nodeName)),null!=e.id&&(t.id=String(e.id)),e.classList){if(!Array.isArray(e.classList))throw TypeError(".lr.browser.Selector.classList: array expected");t.classList=[];for(var r=0;r<e.classList.length;++r)t.classList[r]=String(e.classList[r])}return null!=e.nthChild&&(t.nthChild=0|e.nthChild),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.classList=[]),t.defaults&&(r.nodeName="",r.id="",r.nthChild=0),null!=e.nodeName&&e.hasOwnProperty("nodeName")&&(r.nodeName=e.nodeName),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),e.classList&&e.classList.length){r.classList=[];for(var n=0;n<e.classList.length;++n)r.classList[n]=e.classList[n]}return null!=e.nthChild&&e.hasOwnProperty("nthChild")&&(r.nthChild=e.nthChild),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.InputChangeEvent=function(){function e(e){if(this.nodePath=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.text="",e.prototype.nodePath=O.emptyArray,e.prototype.isChecked=!1,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.text&&e.hasOwnProperty("text")&&t.uint32(18).string(e.text),null!=e.nodePath&&e.nodePath.length)for(var r=0;r<e.nodePath.length;++r)b.lr.browser.Selector.encode(e.nodePath[r],t.uint32(26).fork()).ldelim();return null!=e.isChecked&&e.hasOwnProperty("isChecked")&&t.uint32(32).bool(e.isChecked),t},e.fromObject=function(e){if(e instanceof b.lr.browser.InputChangeEvent)return e;var t=new b.lr.browser.InputChangeEvent;if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.text&&(t.text=String(e.text)),e.nodePath){if(!Array.isArray(e.nodePath))throw TypeError(".lr.browser.InputChangeEvent.nodePath: array expected");t.nodePath=[];for(var r=0;r<e.nodePath.length;++r){if("object"!=typeof e.nodePath[r])throw TypeError(".lr.browser.InputChangeEvent.nodePath: object expected");t.nodePath[r]=b.lr.browser.Selector.fromObject(e.nodePath[r])}}return null!=e.isChecked&&(t.isChecked=Boolean(e.isChecked)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.nodePath=[]),t.defaults&&(r.nodeId=0,r.text="",r.isChecked=!1),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),null!=e.text&&e.hasOwnProperty("text")&&(r.text=e.text),e.nodePath&&e.nodePath.length){r.nodePath=[];for(var n=0;n<e.nodePath.length;++n)r.nodePath[n]=b.lr.browser.Selector.toObject(e.nodePath[n],t)}return null!=e.isChecked&&e.hasOwnProperty("isChecked")&&(r.isChecked=e.isChecked),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.MouseEvent=function(){function e(e){if(this.componentTree=[],this.nodePath=[],this.moves=[],this.hovers=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.eventType=0,e.prototype.domTarget="",e.prototype.button=0,e.prototype.clientX=0,e.prototype.clientY=0,e.prototype.nodeId=0,e.prototype.isRageClick=!1,e.prototype.text="",e.prototype.componentTree=O.emptyArray,e.prototype.nodePath=O.emptyArray,e.prototype.moves=O.emptyArray,e.prototype.url="",e.prototype.hovers=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.eventType&&e.hasOwnProperty("eventType")&&t.uint32(8).int32(e.eventType),null!=e.domTarget&&e.hasOwnProperty("domTarget")&&t.uint32(18).string(e.domTarget),null!=e.button&&e.hasOwnProperty("button")&&t.uint32(24).uint32(e.button),null!=e.clientX&&e.hasOwnProperty("clientX")&&t.uint32(32).int32(e.clientX),null!=e.clientY&&e.hasOwnProperty("clientY")&&t.uint32(40).int32(e.clientY),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(48).uint32(e.nodeId),null!=e.isRageClick&&e.hasOwnProperty("isRageClick")&&t.uint32(56).bool(e.isRageClick),null!=e.text&&e.hasOwnProperty("text")&&t.uint32(66).string(e.text),null!=e.componentTree&&e.componentTree.length)for(var r=0;r<e.componentTree.length;++r)t.uint32(74).string(e.componentTree[r]);if(null!=e.nodePath&&e.nodePath.length)for(r=0;r<e.nodePath.length;++r)b.lr.browser.Selector.encode(e.nodePath[r],t.uint32(82).fork()).ldelim();if(null!=e.moves&&e.moves.length)for(r=0;r<e.moves.length;++r)b.lr.browser.MouseMove.encode(e.moves[r],t.uint32(90).fork()).ldelim();if(null!=e.url&&e.hasOwnProperty("url")&&t.uint32(98).string(e.url),null!=e.hovers&&e.hovers.length)for(r=0;r<e.hovers.length;++r)b.lr.browser.MouseHover.encode(e.hovers[r],t.uint32(106).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.MouseEvent)return e;var t=new b.lr.browser.MouseEvent;switch(e.eventType){case"MOUSEOVER":case 0:t.eventType=0;break;case"MOUSEOUT":case 1:t.eventType=1;break;case"MOUSEUP":case 2:t.eventType=2;break;case"MOUSEDOWN":case 3:t.eventType=3;break;case"MOUSELEAVE":case 4:t.eventType=4;break;case"MOUSEENTER":case 5:t.eventType=5;break;case"DRAGSTART":case 6:t.eventType=6;break;case"DRAGEND":case 7:t.eventType=7;break;case"DRAGLEAVE":case 8:t.eventType=8;break;case"CLICK":case 9:t.eventType=9;break;case"CONTEXTMENU":case 10:t.eventType=10;break;case"DBLCLICK":case 11:t.eventType=11;break;case"DROP":case 12:t.eventType=12;break;case"MOUSEMOVE":case 13:t.eventType=13;break;case"DRAGOVER":case 14:t.eventType=14;break;case"DRAGENTER":case 15:t.eventType=15;break;case"DRAG":case 16:t.eventType=16;break;case"FOCUS":case 17:t.eventType=17;break;case"BLUR":case 18:t.eventType=18;break;case"TOUCHSTART":case 19:t.eventType=19;break;case"TOUCHMOVE":case 20:t.eventType=20;break;case"TOUCHEND":case 21:t.eventType=21;break;case"MOUSEHOVER":case 22:t.eventType=22}if(null!=e.domTarget&&(t.domTarget=String(e.domTarget)),null!=e.button&&(t.button=e.button>>>0),null!=e.clientX&&(t.clientX=0|e.clientX),null!=e.clientY&&(t.clientY=0|e.clientY),null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.isRageClick&&(t.isRageClick=Boolean(e.isRageClick)),null!=e.text&&(t.text=String(e.text)),e.componentTree){if(!Array.isArray(e.componentTree))throw TypeError(".lr.browser.MouseEvent.componentTree: array expected");t.componentTree=[];for(var r=0;r<e.componentTree.length;++r)t.componentTree[r]=String(e.componentTree[r])}if(e.nodePath){if(!Array.isArray(e.nodePath))throw TypeError(".lr.browser.MouseEvent.nodePath: array expected");for(t.nodePath=[],r=0;r<e.nodePath.length;++r){if("object"!=typeof e.nodePath[r])throw TypeError(".lr.browser.MouseEvent.nodePath: object expected");t.nodePath[r]=b.lr.browser.Selector.fromObject(e.nodePath[r])}}if(e.moves){if(!Array.isArray(e.moves))throw TypeError(".lr.browser.MouseEvent.moves: array expected");for(t.moves=[],r=0;r<e.moves.length;++r){if("object"!=typeof e.moves[r])throw TypeError(".lr.browser.MouseEvent.moves: object expected");t.moves[r]=b.lr.browser.MouseMove.fromObject(e.moves[r])}}if(null!=e.url&&(t.url=String(e.url)),e.hovers){if(!Array.isArray(e.hovers))throw TypeError(".lr.browser.MouseEvent.hovers: array expected");for(t.hovers=[],r=0;r<e.hovers.length;++r){if("object"!=typeof e.hovers[r])throw TypeError(".lr.browser.MouseEvent.hovers: object expected");t.hovers[r]=b.lr.browser.MouseHover.fromObject(e.hovers[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.componentTree=[],r.nodePath=[],r.moves=[],r.hovers=[]),t.defaults&&(r.eventType=t.enums===String?"MOUSEOVER":0,r.domTarget="",r.button=0,r.clientX=0,r.clientY=0,r.nodeId=0,r.isRageClick=!1,r.text="",r.url=""),null!=e.eventType&&e.hasOwnProperty("eventType")&&(r.eventType=t.enums===String?b.lr.browser.MouseEvent.EventType[e.eventType]:e.eventType),null!=e.domTarget&&e.hasOwnProperty("domTarget")&&(r.domTarget=e.domTarget),null!=e.button&&e.hasOwnProperty("button")&&(r.button=e.button),null!=e.clientX&&e.hasOwnProperty("clientX")&&(r.clientX=e.clientX),null!=e.clientY&&e.hasOwnProperty("clientY")&&(r.clientY=e.clientY),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),null!=e.isRageClick&&e.hasOwnProperty("isRageClick")&&(r.isRageClick=e.isRageClick),null!=e.text&&e.hasOwnProperty("text")&&(r.text=e.text),e.componentTree&&e.componentTree.length){r.componentTree=[];for(var n=0;n<e.componentTree.length;++n)r.componentTree[n]=e.componentTree[n]}if(e.nodePath&&e.nodePath.length)for(r.nodePath=[],n=0;n<e.nodePath.length;++n)r.nodePath[n]=b.lr.browser.Selector.toObject(e.nodePath[n],t);if(e.moves&&e.moves.length)for(r.moves=[],n=0;n<e.moves.length;++n)r.moves[n]=b.lr.browser.MouseMove.toObject(e.moves[n],t);if(null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),e.hovers&&e.hovers.length)for(r.hovers=[],n=0;n<e.hovers.length;++n)r.hovers[n]=b.lr.browser.MouseHover.toObject(e.hovers[n],t);return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.EventType=function(){var e={},t=Object.create(e);return t[e[0]="MOUSEOVER"]=0,t[e[1]="MOUSEOUT"]=1,t[e[2]="MOUSEUP"]=2,t[e[3]="MOUSEDOWN"]=3,t[e[4]="MOUSELEAVE"]=4,t[e[5]="MOUSEENTER"]=5,t[e[6]="DRAGSTART"]=6,t[e[7]="DRAGEND"]=7,t[e[8]="DRAGLEAVE"]=8,t[e[9]="CLICK"]=9,t[e[10]="CONTEXTMENU"]=10,t[e[11]="DBLCLICK"]=11,t[e[12]="DROP"]=12,t[e[13]="MOUSEMOVE"]=13,t[e[14]="DRAGOVER"]=14,t[e[15]="DRAGENTER"]=15,t[e[16]="DRAG"]=16,t[e[17]="FOCUS"]=17,t[e[18]="BLUR"]=18,t[e[19]="TOUCHSTART"]=19,t[e[20]="TOUCHMOVE"]=20,t[e[21]="TOUCHEND"]=21,t[e[22]="MOUSEHOVER"]=22,t}(),e}(),f.DeadClick=function(){function e(e){if(this.componentTree=[],this.nodePath=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.clickSeqID=0,e.prototype.text="",e.prototype.href="",e.prototype.componentTree=O.emptyArray,e.prototype.nodePath=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.clickSeqID&&e.hasOwnProperty("clickSeqID")&&t.uint32(9).double(e.clickSeqID),null!=e.text&&e.hasOwnProperty("text")&&t.uint32(18).string(e.text),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(26).string(e.href),null!=e.componentTree&&e.componentTree.length)for(var r=0;r<e.componentTree.length;++r)t.uint32(34).string(e.componentTree[r]);if(null!=e.nodePath&&e.nodePath.length)for(r=0;r<e.nodePath.length;++r)b.lr.browser.Selector.encode(e.nodePath[r],t.uint32(42).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.DeadClick)return e;var t=new b.lr.browser.DeadClick;if(null!=e.clickSeqID&&(t.clickSeqID=Number(e.clickSeqID)),null!=e.text&&(t.text=String(e.text)),null!=e.href&&(t.href=String(e.href)),e.componentTree){if(!Array.isArray(e.componentTree))throw TypeError(".lr.browser.DeadClick.componentTree: array expected");t.componentTree=[];for(var r=0;r<e.componentTree.length;++r)t.componentTree[r]=String(e.componentTree[r])}if(e.nodePath){if(!Array.isArray(e.nodePath))throw TypeError(".lr.browser.DeadClick.nodePath: array expected");for(t.nodePath=[],r=0;r<e.nodePath.length;++r){if("object"!=typeof e.nodePath[r])throw TypeError(".lr.browser.DeadClick.nodePath: object expected");t.nodePath[r]=b.lr.browser.Selector.fromObject(e.nodePath[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.componentTree=[],r.nodePath=[]),t.defaults&&(r.clickSeqID=0,r.text="",r.href=""),null!=e.clickSeqID&&e.hasOwnProperty("clickSeqID")&&(r.clickSeqID=t.json&&!isFinite(e.clickSeqID)?String(e.clickSeqID):e.clickSeqID),null!=e.text&&e.hasOwnProperty("text")&&(r.text=e.text),null!=e.href&&e.hasOwnProperty("href")&&(r.href=e.href),e.componentTree&&e.componentTree.length){r.componentTree=[];for(var n=0;n<e.componentTree.length;++n)r.componentTree[n]=e.componentTree[n]}if(e.nodePath&&e.nodePath.length)for(r.nodePath=[],n=0;n<e.nodePath.length;++n)r.nodePath[n]=b.lr.browser.Selector.toObject(e.nodePath[n],t);return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.MouseMove=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.clientX=0,e.prototype.clientY=0,e.prototype.timeOffset=0,e.prototype.nodeId=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.clientX&&e.hasOwnProperty("clientX")&&t.uint32(8).int32(e.clientX),null!=e.clientY&&e.hasOwnProperty("clientY")&&t.uint32(16).int32(e.clientY),null!=e.timeOffset&&e.hasOwnProperty("timeOffset")&&t.uint32(24).uint32(e.timeOffset),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(32).uint32(e.nodeId),t},e.fromObject=function(e){if(e instanceof b.lr.browser.MouseMove)return e;var t=new b.lr.browser.MouseMove;return null!=e.clientX&&(t.clientX=0|e.clientX),null!=e.clientY&&(t.clientY=0|e.clientY),null!=e.timeOffset&&(t.timeOffset=e.timeOffset>>>0),null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.clientX=0,r.clientY=0,r.timeOffset=0,r.nodeId=0),null!=e.clientX&&e.hasOwnProperty("clientX")&&(r.clientX=e.clientX),null!=e.clientY&&e.hasOwnProperty("clientY")&&(r.clientY=e.clientY),null!=e.timeOffset&&e.hasOwnProperty("timeOffset")&&(r.timeOffset=e.timeOffset),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.MouseHover=function(){function e(e){if(this.nodePath=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodePath=O.emptyArray,e.prototype.timeSpent=0,e.prototype.offsetX=0,e.prototype.offsetY=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodePath&&e.nodePath.length)for(var r=0;r<e.nodePath.length;++r)b.lr.browser.Selector.encode(e.nodePath[r],t.uint32(10).fork()).ldelim();return null!=e.timeSpent&&e.hasOwnProperty("timeSpent")&&t.uint32(16).uint32(e.timeSpent),null!=e.offsetX&&e.hasOwnProperty("offsetX")&&t.uint32(24).int32(e.offsetX),null!=e.offsetY&&e.hasOwnProperty("offsetY")&&t.uint32(32).int32(e.offsetY),t},e.fromObject=function(e){if(e instanceof b.lr.browser.MouseHover)return e;var t=new b.lr.browser.MouseHover;if(e.nodePath){if(!Array.isArray(e.nodePath))throw TypeError(".lr.browser.MouseHover.nodePath: array expected");t.nodePath=[];for(var r=0;r<e.nodePath.length;++r){if("object"!=typeof e.nodePath[r])throw TypeError(".lr.browser.MouseHover.nodePath: object expected");t.nodePath[r]=b.lr.browser.Selector.fromObject(e.nodePath[r])}}return null!=e.timeSpent&&(t.timeSpent=e.timeSpent>>>0),null!=e.offsetX&&(t.offsetX=0|e.offsetX),null!=e.offsetY&&(t.offsetY=0|e.offsetY),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.nodePath=[]),t.defaults&&(r.timeSpent=0,r.offsetX=0,r.offsetY=0),e.nodePath&&e.nodePath.length){r.nodePath=[];for(var n=0;n<e.nodePath.length;++n)r.nodePath[n]=b.lr.browser.Selector.toObject(e.nodePath[n],t)}return null!=e.timeSpent&&e.hasOwnProperty("timeSpent")&&(r.timeSpent=e.timeSpent),null!=e.offsetX&&e.hasOwnProperty("offsetX")&&(r.offsetX=e.offsetX),null!=e.offsetY&&e.hasOwnProperty("offsetY")&&(r.offsetY=e.offsetY),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.ScrollEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.top=0,e.prototype.left=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.top&&e.hasOwnProperty("top")&&t.uint32(21).float(e.top),null!=e.left&&e.hasOwnProperty("left")&&t.uint32(29).float(e.left),t},e.fromObject=function(e){if(e instanceof b.lr.browser.ScrollEvent)return e;var t=new b.lr.browser.ScrollEvent;return null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.top&&(t.top=Number(e.top)),null!=e.left&&(t.left=Number(e.left)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.nodeId=0,r.top=0,r.left=0),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),null!=e.top&&e.hasOwnProperty("top")&&(r.top=t.json&&!isFinite(e.top)?String(e.top):e.top),null!=e.left&&e.hasOwnProperty("left")&&(r.left=t.json&&!isFinite(e.left)?String(e.left):e.left),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.ViewportResizeEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.width=0,e.prototype.height=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.width&&e.hasOwnProperty("width")&&t.uint32(8).uint32(e.width),null!=e.height&&e.hasOwnProperty("height")&&t.uint32(16).uint32(e.height),t},e.fromObject=function(e){if(e instanceof b.lr.browser.ViewportResizeEvent)return e;var t=new b.lr.browser.ViewportResizeEvent;return null!=e.width&&(t.width=e.width>>>0),null!=e.height&&(t.height=e.height>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.width=0,r.height=0),null!=e.width&&e.hasOwnProperty("width")&&(r.width=e.width),null!=e.height&&e.hasOwnProperty("height")&&(r.height=e.height),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.Node=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.id=0,e.prototype.documentTypeInfo=null,e.prototype.commentInfo=null,e.prototype.textInfo=null,e.prototype.elementInfo=null,e.prototype.documentInfo=null,e.prototype.documentFragmentInfo=null,e.prototype.isTainted=!1,Object.defineProperty(e.prototype,"info",{get:O.oneOfGetter(t=["documentTypeInfo","commentInfo","textInfo","elementInfo","documentInfo","documentFragmentInfo"]),set:O.oneOfSetter(t)}),e.encode=function(e,t){return t||(t=g.create()),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(8).uint32(e.id),null!=e.documentTypeInfo&&e.hasOwnProperty("documentTypeInfo")&&b.lr.browser.Node.DocumentTypeInfo.encode(e.documentTypeInfo,t.uint32(18).fork()).ldelim(),null!=e.commentInfo&&e.hasOwnProperty("commentInfo")&&b.lr.browser.Node.CommentInfo.encode(e.commentInfo,t.uint32(26).fork()).ldelim(),null!=e.textInfo&&e.hasOwnProperty("textInfo")&&b.lr.browser.Node.TextInfo.encode(e.textInfo,t.uint32(34).fork()).ldelim(),null!=e.elementInfo&&e.hasOwnProperty("elementInfo")&&b.lr.browser.Node.ElementInfo.encode(e.elementInfo,t.uint32(42).fork()).ldelim(),null!=e.documentInfo&&e.hasOwnProperty("documentInfo")&&b.lr.browser.Node.DocumentInfo.encode(e.documentInfo,t.uint32(50).fork()).ldelim(),null!=e.isTainted&&e.hasOwnProperty("isTainted")&&t.uint32(56).bool(e.isTainted),null!=e.documentFragmentInfo&&e.hasOwnProperty("documentFragmentInfo")&&b.lr.browser.Node.DocumentFragmentInfo.encode(e.documentFragmentInfo,t.uint32(66).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node)return e;var t=new b.lr.browser.Node;if(null!=e.id&&(t.id=e.id>>>0),null!=e.documentTypeInfo){if("object"!=typeof e.documentTypeInfo)throw TypeError(".lr.browser.Node.documentTypeInfo: object expected");t.documentTypeInfo=b.lr.browser.Node.DocumentTypeInfo.fromObject(e.documentTypeInfo)}if(null!=e.commentInfo){if("object"!=typeof e.commentInfo)throw TypeError(".lr.browser.Node.commentInfo: object expected");t.commentInfo=b.lr.browser.Node.CommentInfo.fromObject(e.commentInfo)}if(null!=e.textInfo){if("object"!=typeof e.textInfo)throw TypeError(".lr.browser.Node.textInfo: object expected");t.textInfo=b.lr.browser.Node.TextInfo.fromObject(e.textInfo)}if(null!=e.elementInfo){if("object"!=typeof e.elementInfo)throw TypeError(".lr.browser.Node.elementInfo: object expected");t.elementInfo=b.lr.browser.Node.ElementInfo.fromObject(e.elementInfo)}if(null!=e.documentInfo){if("object"!=typeof e.documentInfo)throw TypeError(".lr.browser.Node.documentInfo: object expected");t.documentInfo=b.lr.browser.Node.DocumentInfo.fromObject(e.documentInfo)}if(null!=e.documentFragmentInfo){if("object"!=typeof e.documentFragmentInfo)throw TypeError(".lr.browser.Node.documentFragmentInfo: object expected");t.documentFragmentInfo=b.lr.browser.Node.DocumentFragmentInfo.fromObject(e.documentFragmentInfo)}return null!=e.isTainted&&(t.isTainted=Boolean(e.isTainted)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.id=0,r.isTainted=!1),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),null!=e.documentTypeInfo&&e.hasOwnProperty("documentTypeInfo")&&(r.documentTypeInfo=b.lr.browser.Node.DocumentTypeInfo.toObject(e.documentTypeInfo,t),t.oneofs&&(r.info="documentTypeInfo")),null!=e.commentInfo&&e.hasOwnProperty("commentInfo")&&(r.commentInfo=b.lr.browser.Node.CommentInfo.toObject(e.commentInfo,t),t.oneofs&&(r.info="commentInfo")),null!=e.textInfo&&e.hasOwnProperty("textInfo")&&(r.textInfo=b.lr.browser.Node.TextInfo.toObject(e.textInfo,t),t.oneofs&&(r.info="textInfo")),null!=e.elementInfo&&e.hasOwnProperty("elementInfo")&&(r.elementInfo=b.lr.browser.Node.ElementInfo.toObject(e.elementInfo,t),t.oneofs&&(r.info="elementInfo")),null!=e.documentInfo&&e.hasOwnProperty("documentInfo")&&(r.documentInfo=b.lr.browser.Node.DocumentInfo.toObject(e.documentInfo,t),t.oneofs&&(r.info="documentInfo")),null!=e.isTainted&&e.hasOwnProperty("isTainted")&&(r.isTainted=e.isTainted),null!=e.documentFragmentInfo&&e.hasOwnProperty("documentFragmentInfo")&&(r.documentFragmentInfo=b.lr.browser.Node.DocumentFragmentInfo.toObject(e.documentFragmentInfo,t),t.oneofs&&(r.info="documentFragmentInfo")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.DocumentTypeInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.name="",e.prototype.publicId="",e.prototype.systemId="",e.encode=function(e,t){return t||(t=g.create()),null!=e.name&&e.hasOwnProperty("name")&&t.uint32(10).string(e.name),null!=e.publicId&&e.hasOwnProperty("publicId")&&t.uint32(18).string(e.publicId),null!=e.systemId&&e.hasOwnProperty("systemId")&&t.uint32(26).string(e.systemId),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.DocumentTypeInfo)return e;var t=new b.lr.browser.Node.DocumentTypeInfo;return null!=e.name&&(t.name=String(e.name)),null!=e.publicId&&(t.publicId=String(e.publicId)),null!=e.systemId&&(t.systemId=String(e.systemId)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.name="",r.publicId="",r.systemId=""),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),null!=e.publicId&&e.hasOwnProperty("publicId")&&(r.publicId=e.publicId),null!=e.systemId&&e.hasOwnProperty("systemId")&&(r.systemId=e.systemId),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.DocumentInfo=function(){function e(e){if(this.childNodes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.childNodes=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.childNodes&&e.childNodes.length)for(var r=0;r<e.childNodes.length;++r)b.lr.browser.Node.encode(e.childNodes[r],t.uint32(10).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.DocumentInfo)return e;var t=new b.lr.browser.Node.DocumentInfo;if(e.childNodes){if(!Array.isArray(e.childNodes))throw TypeError(".lr.browser.Node.DocumentInfo.childNodes: array expected");t.childNodes=[];for(var r=0;r<e.childNodes.length;++r){if("object"!=typeof e.childNodes[r])throw TypeError(".lr.browser.Node.DocumentInfo.childNodes: object expected");t.childNodes[r]=b.lr.browser.Node.fromObject(e.childNodes[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.childNodes=[]),e.childNodes&&e.childNodes.length){r.childNodes=[];for(var n=0;n<e.childNodes.length;++n)r.childNodes[n]=b.lr.browser.Node.toObject(e.childNodes[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.CommentInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.textContent="",e.encode=function(e,t){return t||(t=g.create()),null!=e.textContent&&e.hasOwnProperty("textContent")&&t.uint32(10).string(e.textContent),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.CommentInfo)return e;var t=new b.lr.browser.Node.CommentInfo;return null!=e.textContent&&(t.textContent=String(e.textContent)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.textContent=""),null!=e.textContent&&e.hasOwnProperty("textContent")&&(r.textContent=e.textContent),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.TextInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.textContent="",e.prototype.isStyleNode=!1,e.prototype.boundingRect=null,e.encode=function(e,t){return t||(t=g.create()),null!=e.textContent&&e.hasOwnProperty("textContent")&&t.uint32(10).string(e.textContent),null!=e.isStyleNode&&e.hasOwnProperty("isStyleNode")&&t.uint32(16).bool(e.isStyleNode),null!=e.boundingRect&&e.hasOwnProperty("boundingRect")&&b.lr.browser.Node.TextInfo.Rectangle.encode(e.boundingRect,t.uint32(26).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.TextInfo)return e;var t=new b.lr.browser.Node.TextInfo;if(null!=e.textContent&&(t.textContent=String(e.textContent)),null!=e.isStyleNode&&(t.isStyleNode=Boolean(e.isStyleNode)),null!=e.boundingRect){if("object"!=typeof e.boundingRect)throw TypeError(".lr.browser.Node.TextInfo.boundingRect: object expected");t.boundingRect=b.lr.browser.Node.TextInfo.Rectangle.fromObject(e.boundingRect)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.textContent="",r.isStyleNode=!1,r.boundingRect=null),null!=e.textContent&&e.hasOwnProperty("textContent")&&(r.textContent=e.textContent),null!=e.isStyleNode&&e.hasOwnProperty("isStyleNode")&&(r.isStyleNode=e.isStyleNode),null!=e.boundingRect&&e.hasOwnProperty("boundingRect")&&(r.boundingRect=b.lr.browser.Node.TextInfo.Rectangle.toObject(e.boundingRect,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Rectangle=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.width=0,e.prototype.height=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.width&&e.hasOwnProperty("width")&&t.uint32(13).float(e.width),null!=e.height&&e.hasOwnProperty("height")&&t.uint32(21).float(e.height),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.TextInfo.Rectangle)return e;var t=new b.lr.browser.Node.TextInfo.Rectangle;return null!=e.width&&(t.width=Number(e.width)),null!=e.height&&(t.height=Number(e.height)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.width=0,r.height=0),null!=e.width&&e.hasOwnProperty("width")&&(r.width=t.json&&!isFinite(e.width)?String(e.width):e.width),null!=e.height&&e.hasOwnProperty("height")&&(r.height=t.json&&!isFinite(e.height)?String(e.height):e.height),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e.ElementInfo=function(){function e(e){if(this.attributes={},this.childNodes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.tagName="",e.prototype.attributes=O.emptyObject,e.prototype.childNodes=O.emptyArray,e.prototype.localContents="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.tagName&&e.hasOwnProperty("tagName")&&t.uint32(10).string(e.tagName),null!=e.attributes&&e.hasOwnProperty("attributes"))for(var r=Object.keys(e.attributes),n=0;n<r.length;++n)t.uint32(18).fork().uint32(10).string(r[n]),b.lr.utils.Value.encode(e.attributes[r[n]],t.uint32(18).fork()).ldelim().ldelim();if(null!=e.childNodes&&e.childNodes.length)for(n=0;n<e.childNodes.length;++n)b.lr.browser.Node.encode(e.childNodes[n],t.uint32(34).fork()).ldelim();return null!=e.localContents&&e.hasOwnProperty("localContents")&&t.uint32(42).string(e.localContents),t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.ElementInfo)return e;var t=new b.lr.browser.Node.ElementInfo;if(null!=e.tagName&&(t.tagName=String(e.tagName)),e.attributes){if("object"!=typeof e.attributes)throw TypeError(".lr.browser.Node.ElementInfo.attributes: object expected");t.attributes={};for(var r=Object.keys(e.attributes),n=0;n<r.length;++n){if("object"!=typeof e.attributes[r[n]])throw TypeError(".lr.browser.Node.ElementInfo.attributes: object expected");t.attributes[r[n]]=b.lr.utils.Value.fromObject(e.attributes[r[n]])}}if(e.childNodes){if(!Array.isArray(e.childNodes))throw TypeError(".lr.browser.Node.ElementInfo.childNodes: array expected");for(t.childNodes=[],n=0;n<e.childNodes.length;++n){if("object"!=typeof e.childNodes[n])throw TypeError(".lr.browser.Node.ElementInfo.childNodes: object expected");t.childNodes[n]=b.lr.browser.Node.fromObject(e.childNodes[n])}}return null!=e.localContents&&(t.localContents=String(e.localContents)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.childNodes=[]),(t.objects||t.defaults)&&(n.attributes={}),t.defaults&&(n.tagName="",n.localContents=""),null!=e.tagName&&e.hasOwnProperty("tagName")&&(n.tagName=e.tagName),e.attributes&&(r=Object.keys(e.attributes)).length){n.attributes={};for(var o=0;o<r.length;++o)n.attributes[r[o]]=b.lr.utils.Value.toObject(e.attributes[r[o]],t)}if(e.childNodes&&e.childNodes.length)for(n.childNodes=[],o=0;o<e.childNodes.length;++o)n.childNodes[o]=b.lr.browser.Node.toObject(e.childNodes[o],t);return null!=e.localContents&&e.hasOwnProperty("localContents")&&(n.localContents=e.localContents),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.DocumentFragmentInfo=function(){function e(e){if(this.childNodes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.childNodes=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.childNodes&&e.childNodes.length)for(var r=0;r<e.childNodes.length;++r)b.lr.browser.Node.encode(e.childNodes[r],t.uint32(10).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.Node.DocumentFragmentInfo)return e;var t=new b.lr.browser.Node.DocumentFragmentInfo;if(e.childNodes){if(!Array.isArray(e.childNodes))throw TypeError(".lr.browser.Node.DocumentFragmentInfo.childNodes: array expected");t.childNodes=[];for(var r=0;r<e.childNodes.length;++r){if("object"!=typeof e.childNodes[r])throw TypeError(".lr.browser.Node.DocumentFragmentInfo.childNodes: object expected");t.childNodes[r]=b.lr.browser.Node.fromObject(e.childNodes[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.childNodes=[]),e.childNodes&&e.childNodes.length){r.childNodes=[];for(var n=0;n<e.childNodes.length;++n)r.childNodes[n]=b.lr.browser.Node.toObject(e.childNodes[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),f.NodeInitEvent=function(){function e(e){if(this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.rootNode=null,e.prototype.baseHref="",e.prototype.cacheCookies=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.rootNode&&e.hasOwnProperty("rootNode")&&b.lr.browser.Node.encode(e.rootNode,t.uint32(18).fork()).ldelim(),null!=e.baseHref&&e.hasOwnProperty("baseHref")&&t.uint32(26).string(e.baseHref),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies"))for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.uint32(34).fork().uint32(10).string(r[n]).uint32(18).string(e.cacheCookies[r[n]]).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeInitEvent)return e;var t=new b.lr.browser.NodeInitEvent;if(null!=e.rootNode){if("object"!=typeof e.rootNode)throw TypeError(".lr.browser.NodeInitEvent.rootNode: object expected");t.rootNode=b.lr.browser.Node.fromObject(e.rootNode)}if(null!=e.baseHref&&(t.baseHref=String(e.baseHref)),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.NodeInitEvent.cacheCookies: object expected");t.cacheCookies={};for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.cacheCookies[r[n]]=String(e.cacheCookies[r[n]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.rootNode=null,n.baseHref=""),null!=e.rootNode&&e.hasOwnProperty("rootNode")&&(n.rootNode=b.lr.browser.Node.toObject(e.rootNode,t)),null!=e.baseHref&&e.hasOwnProperty("baseHref")&&(n.baseHref=e.baseHref),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length){n.cacheCookies={};for(var o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]]}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.ShadowInitEvent=function(){function e(e){if(this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.shadowRootNode=null,e.prototype.parentNodeId=0,e.prototype.cacheCookies=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.shadowRootNode&&e.hasOwnProperty("shadowRootNode")&&b.lr.browser.Node.encode(e.shadowRootNode,t.uint32(10).fork()).ldelim(),null!=e.parentNodeId&&e.hasOwnProperty("parentNodeId")&&t.uint32(16).uint32(e.parentNodeId),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies"))for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.uint32(26).fork().uint32(10).string(r[n]).uint32(18).string(e.cacheCookies[r[n]]).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.ShadowInitEvent)return e;var t=new b.lr.browser.ShadowInitEvent;if(null!=e.shadowRootNode){if("object"!=typeof e.shadowRootNode)throw TypeError(".lr.browser.ShadowInitEvent.shadowRootNode: object expected");t.shadowRootNode=b.lr.browser.Node.fromObject(e.shadowRootNode)}if(null!=e.parentNodeId&&(t.parentNodeId=e.parentNodeId>>>0),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.ShadowInitEvent.cacheCookies: object expected");t.cacheCookies={};for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.cacheCookies[r[n]]=String(e.cacheCookies[r[n]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.shadowRootNode=null,n.parentNodeId=0),null!=e.shadowRootNode&&e.hasOwnProperty("shadowRootNode")&&(n.shadowRootNode=b.lr.browser.Node.toObject(e.shadowRootNode,t)),null!=e.parentNodeId&&e.hasOwnProperty("parentNodeId")&&(n.parentNodeId=e.parentNodeId),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length){n.cacheCookies={};for(var o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]]}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.NodeChangeEvent=function(){function e(e){if(this.removed=[],this.addedOrMoved=[],this.attributes=[],this.text=[],this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.removed=O.emptyArray,e.prototype.addedOrMoved=O.emptyArray,e.prototype.attributes=O.emptyArray,e.prototype.text=O.emptyArray,e.prototype.baseHref="",e.prototype.version=0,e.prototype.cacheCookies=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.removed&&e.removed.length)for(var r=0;r<e.removed.length;++r)b.lr.browser.NodeChangeEvent.RemoveEvent.encode(e.removed[r],t.uint32(10).fork()).ldelim();if(null!=e.addedOrMoved&&e.addedOrMoved.length)for(r=0;r<e.addedOrMoved.length;++r)b.lr.browser.NodeChangeEvent.AddOrMoveEvent.encode(e.addedOrMoved[r],t.uint32(18).fork()).ldelim();if(null!=e.attributes&&e.attributes.length)for(r=0;r<e.attributes.length;++r)b.lr.browser.NodeChangeEvent.AttributeEvent.encode(e.attributes[r],t.uint32(26).fork()).ldelim();if(null!=e.text&&e.text.length)for(r=0;r<e.text.length;++r)b.lr.browser.NodeChangeEvent.TextEvent.encode(e.text[r],t.uint32(34).fork()).ldelim();if(null!=e.baseHref&&e.hasOwnProperty("baseHref")&&t.uint32(42).string(e.baseHref),null!=e.version&&e.hasOwnProperty("version")&&t.uint32(48).uint32(e.version),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies")){var n=Object.keys(e.cacheCookies);for(r=0;r<n.length;++r)t.uint32(58).fork().uint32(10).string(n[r]).uint32(18).string(e.cacheCookies[n[r]]).ldelim()}return t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent)return e;var t=new b.lr.browser.NodeChangeEvent;if(e.removed){if(!Array.isArray(e.removed))throw TypeError(".lr.browser.NodeChangeEvent.removed: array expected");t.removed=[];for(var r=0;r<e.removed.length;++r){if("object"!=typeof e.removed[r])throw TypeError(".lr.browser.NodeChangeEvent.removed: object expected");t.removed[r]=b.lr.browser.NodeChangeEvent.RemoveEvent.fromObject(e.removed[r])}}if(e.addedOrMoved){if(!Array.isArray(e.addedOrMoved))throw TypeError(".lr.browser.NodeChangeEvent.addedOrMoved: array expected");for(t.addedOrMoved=[],r=0;r<e.addedOrMoved.length;++r){if("object"!=typeof e.addedOrMoved[r])throw TypeError(".lr.browser.NodeChangeEvent.addedOrMoved: object expected");t.addedOrMoved[r]=b.lr.browser.NodeChangeEvent.AddOrMoveEvent.fromObject(e.addedOrMoved[r])}}if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".lr.browser.NodeChangeEvent.attributes: array expected");for(t.attributes=[],r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".lr.browser.NodeChangeEvent.attributes: object expected");t.attributes[r]=b.lr.browser.NodeChangeEvent.AttributeEvent.fromObject(e.attributes[r])}}if(e.text){if(!Array.isArray(e.text))throw TypeError(".lr.browser.NodeChangeEvent.text: array expected");for(t.text=[],r=0;r<e.text.length;++r){if("object"!=typeof e.text[r])throw TypeError(".lr.browser.NodeChangeEvent.text: object expected");t.text[r]=b.lr.browser.NodeChangeEvent.TextEvent.fromObject(e.text[r])}}if(null!=e.baseHref&&(t.baseHref=String(e.baseHref)),null!=e.version&&(t.version=e.version>>>0),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.NodeChangeEvent.cacheCookies: object expected");t.cacheCookies={};var n=Object.keys(e.cacheCookies);for(r=0;r<n.length;++r)t.cacheCookies[n[r]]=String(e.cacheCookies[n[r]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.removed=[],n.addedOrMoved=[],n.attributes=[],n.text=[]),(t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.baseHref="",n.version=0),e.removed&&e.removed.length){n.removed=[];for(var o=0;o<e.removed.length;++o)n.removed[o]=b.lr.browser.NodeChangeEvent.RemoveEvent.toObject(e.removed[o],t)}if(e.addedOrMoved&&e.addedOrMoved.length)for(n.addedOrMoved=[],o=0;o<e.addedOrMoved.length;++o)n.addedOrMoved[o]=b.lr.browser.NodeChangeEvent.AddOrMoveEvent.toObject(e.addedOrMoved[o],t);if(e.attributes&&e.attributes.length)for(n.attributes=[],o=0;o<e.attributes.length;++o)n.attributes[o]=b.lr.browser.NodeChangeEvent.AttributeEvent.toObject(e.attributes[o],t);if(e.text&&e.text.length)for(n.text=[],o=0;o<e.text.length;++o)n.text[o]=b.lr.browser.NodeChangeEvent.TextEvent.toObject(e.text[o],t);if(null!=e.baseHref&&e.hasOwnProperty("baseHref")&&(n.baseHref=e.baseHref),null!=e.version&&e.hasOwnProperty("version")&&(n.version=e.version),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length)for(n.cacheCookies={},o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]];return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.RemoveEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent.RemoveEvent)return e;var t=new b.lr.browser.NodeChangeEvent.RemoveEvent;return null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.nodeId=0),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.AddOrMoveEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeData=null,e.prototype.nextSiblingId=0,e.prototype.parentNodeId=0,e.prototype.isRemoved=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.nodeData&&e.hasOwnProperty("nodeData")&&b.lr.browser.Node.encode(e.nodeData,t.uint32(10).fork()).ldelim(),null!=e.nextSiblingId&&e.hasOwnProperty("nextSiblingId")&&t.uint32(16).uint32(e.nextSiblingId),null!=e.parentNodeId&&e.hasOwnProperty("parentNodeId")&&t.uint32(24).uint32(e.parentNodeId),null!=e.isRemoved&&e.hasOwnProperty("isRemoved")&&t.uint32(32).bool(e.isRemoved),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent.AddOrMoveEvent)return e;var t=new b.lr.browser.NodeChangeEvent.AddOrMoveEvent;if(null!=e.nodeData){if("object"!=typeof e.nodeData)throw TypeError(".lr.browser.NodeChangeEvent.AddOrMoveEvent.nodeData: object expected");t.nodeData=b.lr.browser.Node.fromObject(e.nodeData)}return null!=e.nextSiblingId&&(t.nextSiblingId=e.nextSiblingId>>>0),null!=e.parentNodeId&&(t.parentNodeId=e.parentNodeId>>>0),null!=e.isRemoved&&(t.isRemoved=Boolean(e.isRemoved)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.nodeData=null,r.nextSiblingId=0,r.parentNodeId=0,r.isRemoved=!1),null!=e.nodeData&&e.hasOwnProperty("nodeData")&&(r.nodeData=b.lr.browser.Node.toObject(e.nodeData,t)),null!=e.nextSiblingId&&e.hasOwnProperty("nextSiblingId")&&(r.nextSiblingId=e.nextSiblingId),null!=e.parentNodeId&&e.hasOwnProperty("parentNodeId")&&(r.parentNodeId=e.parentNodeId),null!=e.isRemoved&&e.hasOwnProperty("isRemoved")&&(r.isRemoved=e.isRemoved),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.AttributeEvent=function(){function e(e){if(this.attributes={},this.removedAttributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.attributes=O.emptyObject,e.prototype.removedAttributes=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.attributes&&e.hasOwnProperty("attributes"))for(var r=Object.keys(e.attributes),n=0;n<r.length;++n)t.uint32(18).fork().uint32(10).string(r[n]),b.lr.utils.Value.encode(e.attributes[r[n]],t.uint32(18).fork()).ldelim().ldelim();if(null!=e.removedAttributes&&e.removedAttributes.length)for(n=0;n<e.removedAttributes.length;++n)t.uint32(26).string(e.removedAttributes[n]);return t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent.AttributeEvent)return e;var t=new b.lr.browser.NodeChangeEvent.AttributeEvent;if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),e.attributes){if("object"!=typeof e.attributes)throw TypeError(".lr.browser.NodeChangeEvent.AttributeEvent.attributes: object expected");t.attributes={};for(var r=Object.keys(e.attributes),n=0;n<r.length;++n){if("object"!=typeof e.attributes[r[n]])throw TypeError(".lr.browser.NodeChangeEvent.AttributeEvent.attributes: object expected");t.attributes[r[n]]=b.lr.utils.Value.fromObject(e.attributes[r[n]])}}if(e.removedAttributes){if(!Array.isArray(e.removedAttributes))throw TypeError(".lr.browser.NodeChangeEvent.AttributeEvent.removedAttributes: array expected");for(t.removedAttributes=[],n=0;n<e.removedAttributes.length;++n)t.removedAttributes[n]=String(e.removedAttributes[n])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.removedAttributes=[]),(t.objects||t.defaults)&&(n.attributes={}),t.defaults&&(n.nodeId=0),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(n.nodeId=e.nodeId),e.attributes&&(r=Object.keys(e.attributes)).length){n.attributes={};for(var o=0;o<r.length;++o)n.attributes[r[o]]=b.lr.utils.Value.toObject(e.attributes[r[o]],t)}if(e.removedAttributes&&e.removedAttributes.length)for(n.removedAttributes=[],o=0;o<e.removedAttributes.length;++o)n.removedAttributes[o]=e.removedAttributes[o];return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.TextEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.textContent="",e.prototype.boundingRect=null,e.encode=function(e,t){return t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.textContent&&e.hasOwnProperty("textContent")&&t.uint32(18).string(e.textContent),null!=e.boundingRect&&e.hasOwnProperty("boundingRect")&&b.lr.browser.NodeChangeEvent.TextEvent.Rectangle.encode(e.boundingRect,t.uint32(26).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent.TextEvent)return e;var t=new b.lr.browser.NodeChangeEvent.TextEvent;if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.textContent&&(t.textContent=String(e.textContent)),null!=e.boundingRect){if("object"!=typeof e.boundingRect)throw TypeError(".lr.browser.NodeChangeEvent.TextEvent.boundingRect: object expected");t.boundingRect=b.lr.browser.NodeChangeEvent.TextEvent.Rectangle.fromObject(e.boundingRect)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.nodeId=0,r.textContent="",r.boundingRect=null),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),null!=e.textContent&&e.hasOwnProperty("textContent")&&(r.textContent=e.textContent),null!=e.boundingRect&&e.hasOwnProperty("boundingRect")&&(r.boundingRect=b.lr.browser.NodeChangeEvent.TextEvent.Rectangle.toObject(e.boundingRect,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Rectangle=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.width=0,e.prototype.height=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.width&&e.hasOwnProperty("width")&&t.uint32(13).float(e.width),null!=e.height&&e.hasOwnProperty("height")&&t.uint32(21).float(e.height),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NodeChangeEvent.TextEvent.Rectangle)return e;var t=new b.lr.browser.NodeChangeEvent.TextEvent.Rectangle;return null!=e.width&&(t.width=Number(e.width)),null!=e.height&&(t.height=Number(e.height)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.width=0,r.height=0),null!=e.width&&e.hasOwnProperty("width")&&(r.width=t.json&&!isFinite(e.width)?String(e.width):e.width),null!=e.height&&e.hasOwnProperty("height")&&(r.height=t.json&&!isFinite(e.height)?String(e.height):e.height),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e}(),f.StyleContents=function(){function e(e){if(this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.localContents="",e.prototype.href="",e.prototype.snapshot="",e.prototype.cacheCookies=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.localContents&&e.hasOwnProperty("localContents")&&t.uint32(18).string(e.localContents),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(26).string(e.href),null!=e.snapshot&&e.hasOwnProperty("snapshot")&&t.uint32(34).string(e.snapshot),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies"))for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.uint32(42).fork().uint32(10).string(r[n]).uint32(18).string(e.cacheCookies[r[n]]).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.StyleContents)return e;var t=new b.lr.browser.StyleContents;if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.localContents&&(t.localContents=String(e.localContents)),null!=e.href&&(t.href=String(e.href)),null!=e.snapshot&&(t.snapshot=String(e.snapshot)),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.StyleContents.cacheCookies: object expected");t.cacheCookies={};for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.cacheCookies[r[n]]=String(e.cacheCookies[r[n]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.nodeId=0,n.localContents="",n.href="",n.snapshot=""),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(n.nodeId=e.nodeId),null!=e.localContents&&e.hasOwnProperty("localContents")&&(n.localContents=e.localContents),null!=e.href&&e.hasOwnProperty("href")&&(n.href=e.href),null!=e.snapshot&&e.hasOwnProperty("snapshot")&&(n.snapshot=e.snapshot),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length){n.cacheCookies={};for(var o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]]}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.StyleChangeEvent=function(){function e(e){if(this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.eventType=0,e.prototype.nodeId=0,e.prototype.ruleIndex=0,e.prototype.ruleText="",e.prototype.sheetId=0,e.prototype.parentGroupId=0,e.prototype.cacheCookies=O.emptyObject,e.prototype.propertyName="",e.prototype.propertyValue="",e.prototype.priority="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.eventType&&e.hasOwnProperty("eventType")&&t.uint32(8).int32(e.eventType),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(16).uint32(e.nodeId),null!=e.ruleIndex&&e.hasOwnProperty("ruleIndex")&&t.uint32(24).uint32(e.ruleIndex),null!=e.ruleText&&e.hasOwnProperty("ruleText")&&t.uint32(34).string(e.ruleText),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&t.uint32(40).uint32(e.sheetId),null!=e.parentGroupId&&e.hasOwnProperty("parentGroupId")&&t.uint32(48).uint32(e.parentGroupId),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies"))for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.uint32(58).fork().uint32(10).string(r[n]).uint32(18).string(e.cacheCookies[r[n]]).ldelim();return null!=e.propertyName&&e.hasOwnProperty("propertyName")&&t.uint32(66).string(e.propertyName),null!=e.propertyValue&&e.hasOwnProperty("propertyValue")&&t.uint32(74).string(e.propertyValue),null!=e.priority&&e.hasOwnProperty("priority")&&t.uint32(82).string(e.priority),t},e.fromObject=function(e){if(e instanceof b.lr.browser.StyleChangeEvent)return e;var t=new b.lr.browser.StyleChangeEvent;switch(e.eventType){case"INSERT_RULE":case 0:t.eventType=0;break;case"DELETE_RULE":case 1:t.eventType=1;break;case"SET_PROPERTY":case 2:t.eventType=2;break;case"REMOVE_PROPERTY":case 3:t.eventType=3}if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),null!=e.ruleIndex&&(t.ruleIndex=e.ruleIndex>>>0),null!=e.ruleText&&(t.ruleText=String(e.ruleText)),null!=e.sheetId&&(t.sheetId=e.sheetId>>>0),null!=e.parentGroupId&&(t.parentGroupId=e.parentGroupId>>>0),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.StyleChangeEvent.cacheCookies: object expected");t.cacheCookies={};for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.cacheCookies[r[n]]=String(e.cacheCookies[r[n]])}return null!=e.propertyName&&(t.propertyName=String(e.propertyName)),null!=e.propertyValue&&(t.propertyValue=String(e.propertyValue)),null!=e.priority&&(t.priority=String(e.priority)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.eventType=t.enums===String?"INSERT_RULE":0,n.nodeId=0,n.ruleIndex=0,n.ruleText="",n.sheetId=0,n.parentGroupId=0,n.propertyName="",n.propertyValue="",n.priority=""),null!=e.eventType&&e.hasOwnProperty("eventType")&&(n.eventType=t.enums===String?b.lr.browser.StyleChangeEvent.EventType[e.eventType]:e.eventType),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(n.nodeId=e.nodeId),null!=e.ruleIndex&&e.hasOwnProperty("ruleIndex")&&(n.ruleIndex=e.ruleIndex),null!=e.ruleText&&e.hasOwnProperty("ruleText")&&(n.ruleText=e.ruleText),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&(n.sheetId=e.sheetId),null!=e.parentGroupId&&e.hasOwnProperty("parentGroupId")&&(n.parentGroupId=e.parentGroupId),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length){n.cacheCookies={};for(var o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]]}return null!=e.propertyName&&e.hasOwnProperty("propertyName")&&(n.propertyName=e.propertyName),null!=e.propertyValue&&e.hasOwnProperty("propertyValue")&&(n.propertyValue=e.propertyValue),null!=e.priority&&e.hasOwnProperty("priority")&&(n.priority=e.priority),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.EventType=function(){var e={},t=Object.create(e);return t[e[0]="INSERT_RULE"]=0,t[e[1]="DELETE_RULE"]=1,t[e[2]="SET_PROPERTY"]=2,t[e[3]="REMOVE_PROPERTY"]=3,t}(),e}(),f.ConstructedStyleSheetEvent=function(){function e(e){if(this.cacheCookies={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.sheetId=0,e.prototype.sheetContents="",e.prototype.cacheCookies=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&t.uint32(8).uint32(e.sheetId),null!=e.sheetContents&&e.hasOwnProperty("sheetContents")&&t.uint32(18).string(e.sheetContents),null!=e.cacheCookies&&e.hasOwnProperty("cacheCookies"))for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.uint32(26).fork().uint32(10).string(r[n]).uint32(18).string(e.cacheCookies[r[n]]).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.browser.ConstructedStyleSheetEvent)return e;var t=new b.lr.browser.ConstructedStyleSheetEvent;if(null!=e.sheetId&&(t.sheetId=e.sheetId>>>0),null!=e.sheetContents&&(t.sheetContents=String(e.sheetContents)),e.cacheCookies){if("object"!=typeof e.cacheCookies)throw TypeError(".lr.browser.ConstructedStyleSheetEvent.cacheCookies: object expected");t.cacheCookies={};for(var r=Object.keys(e.cacheCookies),n=0;n<r.length;++n)t.cacheCookies[r[n]]=String(e.cacheCookies[r[n]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.cacheCookies={}),t.defaults&&(n.sheetId=0,n.sheetContents=""),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&(n.sheetId=e.sheetId),null!=e.sheetContents&&e.hasOwnProperty("sheetContents")&&(n.sheetContents=e.sheetContents),e.cacheCookies&&(r=Object.keys(e.cacheCookies)).length){n.cacheCookies={};for(var o=0;o<r.length;++o)n.cacheCookies[r[o]]=e.cacheCookies[r[o]]}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.AdoptedStyleSheetsEvent=function(){function e(e){if(this.sheetIds=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeId=0,e.prototype.sheetIds=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&t.uint32(8).uint32(e.nodeId),null!=e.sheetIds&&e.sheetIds.length){t.uint32(18).fork();for(var r=0;r<e.sheetIds.length;++r)t.uint32(e.sheetIds[r]);t.ldelim()}return t},e.fromObject=function(e){if(e instanceof b.lr.browser.AdoptedStyleSheetsEvent)return e;var t=new b.lr.browser.AdoptedStyleSheetsEvent;if(null!=e.nodeId&&(t.nodeId=e.nodeId>>>0),e.sheetIds){if(!Array.isArray(e.sheetIds))throw TypeError(".lr.browser.AdoptedStyleSheetsEvent.sheetIds: array expected");t.sheetIds=[];for(var r=0;r<e.sheetIds.length;++r)t.sheetIds[r]=e.sheetIds[r]>>>0}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.sheetIds=[]),t.defaults&&(r.nodeId=0),null!=e.nodeId&&e.hasOwnProperty("nodeId")&&(r.nodeId=e.nodeId),e.sheetIds&&e.sheetIds.length){r.sheetIds=[];for(var n=0;n<e.sheetIds.length;++n)r.sheetIds[n]=e.sheetIds[n]}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.StyleSheetDisabledEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.sheetId=0,e.prototype.disabled=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&t.uint32(8).uint32(e.sheetId),null!=e.disabled&&e.hasOwnProperty("disabled")&&t.uint32(16).bool(e.disabled),t},e.fromObject=function(e){if(e instanceof b.lr.browser.StyleSheetDisabledEvent)return e;var t=new b.lr.browser.StyleSheetDisabledEvent;return null!=e.sheetId&&(t.sheetId=e.sheetId>>>0),null!=e.disabled&&(t.disabled=Boolean(e.disabled)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.sheetId=0,r.disabled=!1),null!=e.sheetId&&e.hasOwnProperty("sheetId")&&(r.sheetId=e.sheetId),null!=e.disabled&&e.hasOwnProperty("disabled")&&(r.disabled=e.disabled),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.IframeInitEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.iframeNodeID=0,e.prototype.tabID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.iframeNodeID&&e.hasOwnProperty("iframeNodeID")&&t.uint32(8).uint32(e.iframeNodeID),null!=e.tabID&&e.hasOwnProperty("tabID")&&t.uint32(18).string(e.tabID),t},e.fromObject=function(e){if(e instanceof b.lr.browser.IframeInitEvent)return e;var t=new b.lr.browser.IframeInitEvent;return null!=e.iframeNodeID&&(t.iframeNodeID=e.iframeNodeID>>>0),null!=e.tabID&&(t.tabID=String(e.tabID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.iframeNodeID=0,r.tabID=""),null!=e.iframeNodeID&&e.hasOwnProperty("iframeNodeID")&&(r.iframeNodeID=e.iframeNodeID),null!=e.tabID&&e.hasOwnProperty("tabID")&&(r.tabID=e.tabID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.PIIExposureEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.redactedInputContext=null,Object.defineProperty(e.prototype,"context",{get:O.oneOfGetter(t=["redactedInputContext"]),set:O.oneOfSetter(t)}),e.encode=function(e,t){return t||(t=g.create()),null!=e.redactedInputContext&&e.hasOwnProperty("redactedInputContext")&&b.lr.browser.PIIExposureEvent.RedactedInputContext.encode(e.redactedInputContext,t.uint32(10).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.browser.PIIExposureEvent)return e;var t=new b.lr.browser.PIIExposureEvent;if(null!=e.redactedInputContext){if("object"!=typeof e.redactedInputContext)throw TypeError(".lr.browser.PIIExposureEvent.redactedInputContext: object expected");t.redactedInputContext=b.lr.browser.PIIExposureEvent.RedactedInputContext.fromObject(e.redactedInputContext)}return t},e.toObject=function(e,t){t||(t={});var r={};return null!=e.redactedInputContext&&e.hasOwnProperty("redactedInputContext")&&(r.redactedInputContext=b.lr.browser.PIIExposureEvent.RedactedInputContext.toObject(e.redactedInputContext,t),t.oneofs&&(r.context="redactedInputContext")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.RedactedInputContext=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.matchedItem="",e.prototype.matchIndex=0,e.prototype.source="",e.prototype.reqId="",e.encode=function(e,t){return t||(t=g.create()),null!=e.matchedItem&&e.hasOwnProperty("matchedItem")&&t.uint32(10).string(e.matchedItem),null!=e.matchIndex&&e.hasOwnProperty("matchIndex")&&t.uint32(16).uint32(e.matchIndex),null!=e.source&&e.hasOwnProperty("source")&&t.uint32(26).string(e.source),null!=e.reqId&&e.hasOwnProperty("reqId")&&t.uint32(34).string(e.reqId),t},e.fromObject=function(e){if(e instanceof b.lr.browser.PIIExposureEvent.RedactedInputContext)return e;var t=new b.lr.browser.PIIExposureEvent.RedactedInputContext;return null!=e.matchedItem&&(t.matchedItem=String(e.matchedItem)),null!=e.matchIndex&&(t.matchIndex=e.matchIndex>>>0),null!=e.source&&(t.source=String(e.source)),null!=e.reqId&&(t.reqId=String(e.reqId)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.matchedItem="",r.matchIndex=0,r.source="",r.reqId=""),null!=e.matchedItem&&e.hasOwnProperty("matchedItem")&&(r.matchedItem=e.matchedItem),null!=e.matchIndex&&e.hasOwnProperty("matchIndex")&&(r.matchIndex=e.matchIndex),null!=e.source&&e.hasOwnProperty("source")&&(r.source=e.source),null!=e.reqId&&e.hasOwnProperty("reqId")&&(r.reqId=e.reqId),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),f.NPSEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.surveyID="",e.prototype.score=0,e.prototype.feedback="",e.prototype.provider=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.surveyID&&e.hasOwnProperty("surveyID")&&t.uint32(10).string(e.surveyID),null!=e.score&&e.hasOwnProperty("score")&&t.uint32(16).uint32(e.score),null!=e.feedback&&e.hasOwnProperty("feedback")&&t.uint32(26).string(e.feedback),null!=e.provider&&e.hasOwnProperty("provider")&&t.uint32(32).int32(e.provider),t},e.fromObject=function(e){if(e instanceof b.lr.browser.NPSEvent)return e;var t=new b.lr.browser.NPSEvent;switch(null!=e.surveyID&&(t.surveyID=String(e.surveyID)),null!=e.score&&(t.score=e.score>>>0),null!=e.feedback&&(t.feedback=String(e.feedback)),e.provider){case"WOOTRIC":case 0:t.provider=0;break;case"DELIGHTED":case 1:t.provider=1}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.surveyID="",r.score=0,r.feedback="",r.provider=t.enums===String?"WOOTRIC":0),null!=e.surveyID&&e.hasOwnProperty("surveyID")&&(r.surveyID=e.surveyID),null!=e.score&&e.hasOwnProperty("score")&&(r.score=e.score),null!=e.feedback&&e.hasOwnProperty("feedback")&&(r.feedback=e.feedback),null!=e.provider&&e.hasOwnProperty("provider")&&(r.provider=t.enums===String?b.lr.browser.NPSEvent.NPSProvider[e.provider]:e.provider),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.NPSProvider=function(){var e={},t=Object.create(e);return t[e[0]="WOOTRIC"]=0,t[e[1]="DELIGHTED"]=1,t}(),e}(),f.FrustratingNetworkEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.url="",e.prototype.method="",e.prototype.graphqlName="",e.prototype.graphqlType="",e.prototype.networkRequestEventId="",e.prototype.browserHref="",e.prototype.frustrationTime=O.Long?O.Long.fromBits(0,0,!0):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.url&&e.hasOwnProperty("url")&&t.uint32(10).string(e.url),null!=e.method&&e.hasOwnProperty("method")&&t.uint32(18).string(e.method),null!=e.graphqlName&&e.hasOwnProperty("graphqlName")&&t.uint32(26).string(e.graphqlName),null!=e.graphqlType&&e.hasOwnProperty("graphqlType")&&t.uint32(34).string(e.graphqlType),null!=e.networkRequestEventId&&e.hasOwnProperty("networkRequestEventId")&&t.uint32(42).string(e.networkRequestEventId),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&t.uint32(50).string(e.browserHref),null!=e.frustrationTime&&e.hasOwnProperty("frustrationTime")&&t.uint32(56).uint64(e.frustrationTime),t},e.fromObject=function(e){if(e instanceof b.lr.browser.FrustratingNetworkEvent)return e;var t=new b.lr.browser.FrustratingNetworkEvent;return null!=e.url&&(t.url=String(e.url)),null!=e.method&&(t.method=String(e.method)),null!=e.graphqlName&&(t.graphqlName=String(e.graphqlName)),null!=e.graphqlType&&(t.graphqlType=String(e.graphqlType)),null!=e.networkRequestEventId&&(t.networkRequestEventId=String(e.networkRequestEventId)),null!=e.browserHref&&(t.browserHref=String(e.browserHref)),null!=e.frustrationTime&&(O.Long?(t.frustrationTime=O.Long.fromValue(e.frustrationTime)).unsigned=!0:"string"==typeof e.frustrationTime?t.frustrationTime=parseInt(e.frustrationTime,10):"number"==typeof e.frustrationTime?t.frustrationTime=e.frustrationTime:"object"==typeof e.frustrationTime&&(t.frustrationTime=new O.LongBits(e.frustrationTime.low>>>0,e.frustrationTime.high>>>0).toNumber(!0))),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults)if(r.url="",r.method="",r.graphqlName="",r.graphqlType="",r.networkRequestEventId="",r.browserHref="",O.Long){var n=new O.Long(0,0,!0);r.frustrationTime=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.frustrationTime=t.longs===String?"0":0;return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),null!=e.method&&e.hasOwnProperty("method")&&(r.method=e.method),null!=e.graphqlName&&e.hasOwnProperty("graphqlName")&&(r.graphqlName=e.graphqlName),null!=e.graphqlType&&e.hasOwnProperty("graphqlType")&&(r.graphqlType=e.graphqlType),null!=e.networkRequestEventId&&e.hasOwnProperty("networkRequestEventId")&&(r.networkRequestEventId=e.networkRequestEventId),null!=e.browserHref&&e.hasOwnProperty("browserHref")&&(r.browserHref=e.browserHref),null!=e.frustrationTime&&e.hasOwnProperty("frustrationTime")&&("number"==typeof e.frustrationTime?r.frustrationTime=t.longs===String?String(e.frustrationTime):e.frustrationTime:r.frustrationTime=t.longs===String?O.Long.prototype.toString.call(e.frustrationTime):t.longs===Number?new O.LongBits(e.frustrationTime.low>>>0,e.frustrationTime.high>>>0).toNumber(!0):e.frustrationTime),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f.UTMParamsEvent=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.href="",e.prototype.utmSource="",e.prototype.utmMedium="",e.prototype.utmCampaign="",e.prototype.utmTerm="",e.prototype.utmContent="",e.encode=function(e,t){return t||(t=g.create()),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(10).string(e.href),null!=e.utmSource&&e.hasOwnProperty("utmSource")&&t.uint32(18).string(e.utmSource),null!=e.utmMedium&&e.hasOwnProperty("utmMedium")&&t.uint32(26).string(e.utmMedium),null!=e.utmCampaign&&e.hasOwnProperty("utmCampaign")&&t.uint32(34).string(e.utmCampaign),null!=e.utmTerm&&e.hasOwnProperty("utmTerm")&&t.uint32(42).string(e.utmTerm),null!=e.utmContent&&e.hasOwnProperty("utmContent")&&t.uint32(50).string(e.utmContent),t},e.fromObject=function(e){if(e instanceof b.lr.browser.UTMParamsEvent)return e;var t=new b.lr.browser.UTMParamsEvent;return null!=e.href&&(t.href=String(e.href)),null!=e.utmSource&&(t.utmSource=String(e.utmSource)),null!=e.utmMedium&&(t.utmMedium=String(e.utmMedium)),null!=e.utmCampaign&&(t.utmCampaign=String(e.utmCampaign)),null!=e.utmTerm&&(t.utmTerm=String(e.utmTerm)),null!=e.utmContent&&(t.utmContent=String(e.utmContent)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.href="",r.utmSource="",r.utmMedium="",r.utmCampaign="",r.utmTerm="",r.utmContent=""),null!=e.href&&e.hasOwnProperty("href")&&(r.href=e.href),null!=e.utmSource&&e.hasOwnProperty("utmSource")&&(r.utmSource=e.utmSource),null!=e.utmMedium&&e.hasOwnProperty("utmMedium")&&(r.utmMedium=e.utmMedium),null!=e.utmCampaign&&e.hasOwnProperty("utmCampaign")&&(r.utmCampaign=e.utmCampaign),null!=e.utmTerm&&e.hasOwnProperty("utmTerm")&&(r.utmTerm=e.utmTerm),null!=e.utmContent&&e.hasOwnProperty("utmContent")&&(r.utmContent=e.utmContent),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),f),y.performance=function(){var e={};return e.BusyFrames=function(){function e(e){if(this.measurements=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.measurements=O.emptyArray,e.prototype.url="",e.prototype.elapsedTime=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.performance.BusyFrames.Measurement.encode(e.measurements[r],t.uint32(10).fork()).ldelim();return null!=e.url&&e.hasOwnProperty("url")&&t.uint32(18).string(e.url),null!=e.elapsedTime&&e.hasOwnProperty("elapsedTime")&&t.uint32(29).float(e.elapsedTime),t},e.fromObject=function(e){if(e instanceof b.lr.performance.BusyFrames)return e;var t=new b.lr.performance.BusyFrames;if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.performance.BusyFrames.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.performance.BusyFrames.measurements: object expected");t.measurements[r]=b.lr.performance.BusyFrames.Measurement.fromObject(e.measurements[r])}}return null!=e.url&&(t.url=String(e.url)),null!=e.elapsedTime&&(t.elapsedTime=Number(e.elapsedTime)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.measurements=[]),t.defaults&&(r.url="",r.elapsedTime=0),e.measurements&&e.measurements.length){r.measurements=[];for(var n=0;n<e.measurements.length;++n)r.measurements[n]=b.lr.performance.BusyFrames.Measurement.toObject(e.measurements[n],t)}return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),null!=e.elapsedTime&&e.hasOwnProperty("elapsedTime")&&(r.elapsedTime=t.json&&!isFinite(e.elapsedTime)?String(e.elapsedTime):e.elapsedTime),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.busyFrames=0,e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.busyFrames&&e.hasOwnProperty("busyFrames")&&t.uint32(8).uint32(e.busyFrames),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(16).int64(e.timestamp),t},e.fromObject=function(e){if(e instanceof b.lr.performance.BusyFrames.Measurement)return e;var t=new b.lr.performance.BusyFrames.Measurement;return null!=e.busyFrames&&(t.busyFrames=e.busyFrames>>>0),null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults)if(r.busyFrames=0,O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;return null!=e.busyFrames&&e.hasOwnProperty("busyFrames")&&(r.busyFrames=e.busyFrames),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e.FirstInputDelay=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.duration=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(8).uint32(e.duration),t},e.fromObject=function(e){if(e instanceof b.lr.performance.FirstInputDelay)return e;var t=new b.lr.performance.FirstInputDelay;return null!=e.duration&&(t.duration=e.duration>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.duration=0),null!=e.duration&&e.hasOwnProperty("duration")&&(r.duration=e.duration),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.LongTasks=function(){function e(e){if(this.longTasks=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.longTasks=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.longTasks&&e.longTasks.length)for(var r=0;r<e.longTasks.length;++r)b.lr.performance.LongTasks.LongTask.encode(e.longTasks[r],t.uint32(10).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.performance.LongTasks)return e;var t=new b.lr.performance.LongTasks;if(e.longTasks){if(!Array.isArray(e.longTasks))throw TypeError(".lr.performance.LongTasks.longTasks: array expected");t.longTasks=[];for(var r=0;r<e.longTasks.length;++r){if("object"!=typeof e.longTasks[r])throw TypeError(".lr.performance.LongTasks.longTasks: object expected");t.longTasks[r]=b.lr.performance.LongTasks.LongTask.fromObject(e.longTasks[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.longTasks=[]),e.longTasks&&e.longTasks.length){r.longTasks=[];for(var n=0;n<e.longTasks.length;++n)r.longTasks[n]=b.lr.performance.LongTasks.LongTask.toObject(e.longTasks[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.LongTask=function(){function e(e){if(this.attribution=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.name="",e.prototype.startTime=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.duration=0,e.prototype.attribution=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.name&&e.hasOwnProperty("name")&&t.uint32(10).string(e.name),null!=e.startTime&&e.hasOwnProperty("startTime")&&t.uint32(16).int64(e.startTime),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(29).float(e.duration),null!=e.attribution&&e.attribution.length)for(var r=0;r<e.attribution.length;++r)b.lr.performance.LongTasks.LongTask.Attribution.encode(e.attribution[r],t.uint32(34).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.performance.LongTasks.LongTask)return e;var t=new b.lr.performance.LongTasks.LongTask;if(null!=e.name&&(t.name=String(e.name)),null!=e.startTime&&(O.Long?(t.startTime=O.Long.fromValue(e.startTime)).unsigned=!1:"string"==typeof e.startTime?t.startTime=parseInt(e.startTime,10):"number"==typeof e.startTime?t.startTime=e.startTime:"object"==typeof e.startTime&&(t.startTime=new O.LongBits(e.startTime.low>>>0,e.startTime.high>>>0).toNumber())),null!=e.duration&&(t.duration=Number(e.duration)),e.attribution){if(!Array.isArray(e.attribution))throw TypeError(".lr.performance.LongTasks.LongTask.attribution: array expected");t.attribution=[];for(var r=0;r<e.attribution.length;++r){if("object"!=typeof e.attribution[r])throw TypeError(".lr.performance.LongTasks.LongTask.attribution: object expected");t.attribution[r]=b.lr.performance.LongTasks.LongTask.Attribution.fromObject(e.attribution[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attribution=[]),t.defaults){if(r.name="",O.Long){var n=new O.Long(0,0,!1);r.startTime=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTime=t.longs===String?"0":0;r.duration=0}if(null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),null!=e.startTime&&e.hasOwnProperty("startTime")&&("number"==typeof e.startTime?r.startTime=t.longs===String?String(e.startTime):e.startTime:r.startTime=t.longs===String?O.Long.prototype.toString.call(e.startTime):t.longs===Number?new O.LongBits(e.startTime.low>>>0,e.startTime.high>>>0).toNumber():e.startTime),null!=e.duration&&e.hasOwnProperty("duration")&&(r.duration=t.json&&!isFinite(e.duration)?String(e.duration):e.duration),e.attribution&&e.attribution.length){r.attribution=[];for(var o=0;o<e.attribution.length;++o)r.attribution[o]=b.lr.performance.LongTasks.LongTask.Attribution.toObject(e.attribution[o],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Attribution=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.containerType="",e.prototype.containerName="",e.prototype.containerId="",e.prototype.containerSrc="",e.encode=function(e,t){return t||(t=g.create()),null!=e.containerType&&e.hasOwnProperty("containerType")&&t.uint32(10).string(e.containerType),null!=e.containerName&&e.hasOwnProperty("containerName")&&t.uint32(18).string(e.containerName),null!=e.containerId&&e.hasOwnProperty("containerId")&&t.uint32(26).string(e.containerId),null!=e.containerSrc&&e.hasOwnProperty("containerSrc")&&t.uint32(34).string(e.containerSrc),t},e.fromObject=function(e){if(e instanceof b.lr.performance.LongTasks.LongTask.Attribution)return e;var t=new b.lr.performance.LongTasks.LongTask.Attribution;return null!=e.containerType&&(t.containerType=String(e.containerType)),null!=e.containerName&&(t.containerName=String(e.containerName)),null!=e.containerId&&(t.containerId=String(e.containerId)),null!=e.containerSrc&&(t.containerSrc=String(e.containerSrc)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.containerType="",r.containerName="",r.containerId="",r.containerSrc=""),null!=e.containerType&&e.hasOwnProperty("containerType")&&(r.containerType=e.containerType),null!=e.containerName&&e.hasOwnProperty("containerName")&&(r.containerName=e.containerName),null!=e.containerId&&e.hasOwnProperty("containerId")&&(r.containerId=e.containerId),null!=e.containerSrc&&e.hasOwnProperty("containerSrc")&&(r.containerSrc=e.containerSrc),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e}(),e.Memory=function(){function e(e){if(this.measurements=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.measurements=O.emptyArray,e.prototype.url="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.performance.Memory.Measurement.encode(e.measurements[r],t.uint32(10).fork()).ldelim();return null!=e.url&&e.hasOwnProperty("url")&&t.uint32(18).string(e.url),t},e.fromObject=function(e){if(e instanceof b.lr.performance.Memory)return e;var t=new b.lr.performance.Memory;if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.performance.Memory.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.performance.Memory.measurements: object expected");t.measurements[r]=b.lr.performance.Memory.Measurement.fromObject(e.measurements[r])}}return null!=e.url&&(t.url=String(e.url)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.measurements=[]),t.defaults&&(r.url=""),e.measurements&&e.measurements.length){r.measurements=[];for(var n=0;n<e.measurements.length;++n)r.measurements[n]=b.lr.performance.Memory.Measurement.toObject(e.measurements[n],t)}return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.usedHeap=0,e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.usedHeap&&e.hasOwnProperty("usedHeap")&&t.uint32(13).float(e.usedHeap),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(16).int64(e.timestamp),t},e.fromObject=function(e){if(e instanceof b.lr.performance.Memory.Measurement)return e;var t=new b.lr.performance.Memory.Measurement;return null!=e.usedHeap&&(t.usedHeap=Number(e.usedHeap)),null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults)if(r.usedHeap=0,O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;return null!=e.usedHeap&&e.hasOwnProperty("usedHeap")&&(r.usedHeap=t.json&&!isFinite(e.usedHeap)?String(e.usedHeap):e.usedHeap),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e.CpuUsage=function(){function e(e){if(this.measurements=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.measurements=O.emptyArray,e.prototype.url="",e.prototype.numberOfCpuCores=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.performance.CpuUsage.Measurement.encode(e.measurements[r],t.uint32(10).fork()).ldelim();return null!=e.url&&e.hasOwnProperty("url")&&t.uint32(18).string(e.url),null!=e.numberOfCpuCores&&e.hasOwnProperty("numberOfCpuCores")&&t.uint32(24).uint32(e.numberOfCpuCores),t},e.fromObject=function(e){if(e instanceof b.lr.performance.CpuUsage)return e;var t=new b.lr.performance.CpuUsage;if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.performance.CpuUsage.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.performance.CpuUsage.measurements: object expected");t.measurements[r]=b.lr.performance.CpuUsage.Measurement.fromObject(e.measurements[r])}}return null!=e.url&&(t.url=String(e.url)),null!=e.numberOfCpuCores&&(t.numberOfCpuCores=e.numberOfCpuCores>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.measurements=[]),t.defaults&&(r.url="",r.numberOfCpuCores=0),e.measurements&&e.measurements.length){r.measurements=[];for(var n=0;n<e.measurements.length;++n)r.measurements[n]=b.lr.performance.CpuUsage.Measurement.toObject(e.measurements[n],t)}return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),null!=e.numberOfCpuCores&&e.hasOwnProperty("numberOfCpuCores")&&(r.numberOfCpuCores=e.numberOfCpuCores),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.kernelTimePercent=0,e.prototype.userTimePercent=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(8).int64(e.timestamp),null!=e.kernelTimePercent&&e.hasOwnProperty("kernelTimePercent")&&t.uint32(29).float(e.kernelTimePercent),null!=e.userTimePercent&&e.hasOwnProperty("userTimePercent")&&t.uint32(37).float(e.userTimePercent),t},e.fromObject=function(e){if(e instanceof b.lr.performance.CpuUsage.Measurement)return e;var t=new b.lr.performance.CpuUsage.Measurement;return null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),null!=e.kernelTimePercent&&(t.kernelTimePercent=Number(e.kernelTimePercent)),null!=e.userTimePercent&&(t.userTimePercent=Number(e.userTimePercent)),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if(O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;r.kernelTimePercent=0,r.userTimePercent=0}return null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),null!=e.kernelTimePercent&&e.hasOwnProperty("kernelTimePercent")&&(r.kernelTimePercent=t.json&&!isFinite(e.kernelTimePercent)?String(e.kernelTimePercent):e.kernelTimePercent),null!=e.userTimePercent&&e.hasOwnProperty("userTimePercent")&&(r.userTimePercent=t.json&&!isFinite(e.userTimePercent)?String(e.userTimePercent):e.userTimePercent),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e.NetworkThroughput=function(){function e(e){if(this.measurements=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.measurements=O.emptyArray,e.prototype.url="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.performance.NetworkThroughput.Measurement.encode(e.measurements[r],t.uint32(10).fork()).ldelim();return null!=e.url&&e.hasOwnProperty("url")&&t.uint32(18).string(e.url),t},e.fromObject=function(e){if(e instanceof b.lr.performance.NetworkThroughput)return e;var t=new b.lr.performance.NetworkThroughput;if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.performance.NetworkThroughput.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.performance.NetworkThroughput.measurements: object expected");t.measurements[r]=b.lr.performance.NetworkThroughput.Measurement.fromObject(e.measurements[r])}}return null!=e.url&&(t.url=String(e.url)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.measurements=[]),t.defaults&&(r.url=""),e.measurements&&e.measurements.length){r.measurements=[];for(var n=0;n<e.measurements.length;++n)r.measurements[n]=b.lr.performance.NetworkThroughput.Measurement.toObject(e.measurements[n],t)}return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.deviceMobileBytes=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.deviceTotalBytes=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.appTotalBytes=O.Long?O.Long.fromBits(0,0,!1):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(8).int64(e.timestamp),null!=e.deviceMobileBytes&&e.hasOwnProperty("deviceMobileBytes")&&t.uint32(16).int64(e.deviceMobileBytes),null!=e.deviceTotalBytes&&e.hasOwnProperty("deviceTotalBytes")&&t.uint32(24).int64(e.deviceTotalBytes),null!=e.appTotalBytes&&e.hasOwnProperty("appTotalBytes")&&t.uint32(32).int64(e.appTotalBytes),t},e.fromObject=function(e){if(e instanceof b.lr.performance.NetworkThroughput.Measurement)return e;var t=new b.lr.performance.NetworkThroughput.Measurement;return null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),null!=e.deviceMobileBytes&&(O.Long?(t.deviceMobileBytes=O.Long.fromValue(e.deviceMobileBytes)).unsigned=!1:"string"==typeof e.deviceMobileBytes?t.deviceMobileBytes=parseInt(e.deviceMobileBytes,10):"number"==typeof e.deviceMobileBytes?t.deviceMobileBytes=e.deviceMobileBytes:"object"==typeof e.deviceMobileBytes&&(t.deviceMobileBytes=new O.LongBits(e.deviceMobileBytes.low>>>0,e.deviceMobileBytes.high>>>0).toNumber())),null!=e.deviceTotalBytes&&(O.Long?(t.deviceTotalBytes=O.Long.fromValue(e.deviceTotalBytes)).unsigned=!1:"string"==typeof e.deviceTotalBytes?t.deviceTotalBytes=parseInt(e.deviceTotalBytes,10):"number"==typeof e.deviceTotalBytes?t.deviceTotalBytes=e.deviceTotalBytes:"object"==typeof e.deviceTotalBytes&&(t.deviceTotalBytes=new O.LongBits(e.deviceTotalBytes.low>>>0,e.deviceTotalBytes.high>>>0).toNumber())),null!=e.appTotalBytes&&(O.Long?(t.appTotalBytes=O.Long.fromValue(e.appTotalBytes)).unsigned=!1:"string"==typeof e.appTotalBytes?t.appTotalBytes=parseInt(e.appTotalBytes,10):"number"==typeof e.appTotalBytes?t.appTotalBytes=e.appTotalBytes:"object"==typeof e.appTotalBytes&&(t.appTotalBytes=new O.LongBits(e.appTotalBytes.low>>>0,e.appTotalBytes.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if(O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;O.Long?(n=new O.Long(0,0,!1),r.deviceMobileBytes=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.deviceMobileBytes=t.longs===String?"0":0,O.Long?(n=new O.Long(0,0,!1),r.deviceTotalBytes=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.deviceTotalBytes=t.longs===String?"0":0,O.Long?(n=new O.Long(0,0,!1),r.appTotalBytes=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.appTotalBytes=t.longs===String?"0":0}return null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),null!=e.deviceMobileBytes&&e.hasOwnProperty("deviceMobileBytes")&&("number"==typeof e.deviceMobileBytes?r.deviceMobileBytes=t.longs===String?String(e.deviceMobileBytes):e.deviceMobileBytes:r.deviceMobileBytes=t.longs===String?O.Long.prototype.toString.call(e.deviceMobileBytes):t.longs===Number?new O.LongBits(e.deviceMobileBytes.low>>>0,e.deviceMobileBytes.high>>>0).toNumber():e.deviceMobileBytes),null!=e.deviceTotalBytes&&e.hasOwnProperty("deviceTotalBytes")&&("number"==typeof e.deviceTotalBytes?r.deviceTotalBytes=t.longs===String?String(e.deviceTotalBytes):e.deviceTotalBytes:r.deviceTotalBytes=t.longs===String?O.Long.prototype.toString.call(e.deviceTotalBytes):t.longs===Number?new O.LongBits(e.deviceTotalBytes.low>>>0,e.deviceTotalBytes.high>>>0).toNumber():e.deviceTotalBytes),null!=e.appTotalBytes&&e.hasOwnProperty("appTotalBytes")&&("number"==typeof e.appTotalBytes?r.appTotalBytes=t.longs===String?String(e.appTotalBytes):e.appTotalBytes:r.appTotalBytes=t.longs===String?O.Long.prototype.toString.call(e.appTotalBytes):t.longs===Number?new O.LongBits(e.appTotalBytes.low>>>0,e.appTotalBytes.high>>>0).toNumber():e.appTotalBytes),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e.AppStartTiming=function(){function e(e){if(this.measurements=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.type=0,e.prototype.measurements=O.emptyArray,e.prototype.url="",e.encode=function(e,t){if(t||(t=g.create()),null!=e.type&&e.hasOwnProperty("type")&&t.uint32(8).int32(e.type),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.performance.AppStartTiming.Measurement.encode(e.measurements[r],t.uint32(18).fork()).ldelim();return null!=e.url&&e.hasOwnProperty("url")&&t.uint32(26).string(e.url),t},e.fromObject=function(e){if(e instanceof b.lr.performance.AppStartTiming)return e;var t=new b.lr.performance.AppStartTiming;switch(e.type){case"APP_COLD_START":case 0:t.type=0;break;case"APP_WARM_START":case 1:t.type=1;break;case"APP_HOT_START":case 2:t.type=2}if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.performance.AppStartTiming.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.performance.AppStartTiming.measurements: object expected");t.measurements[r]=b.lr.performance.AppStartTiming.Measurement.fromObject(e.measurements[r])}}return null!=e.url&&(t.url=String(e.url)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.measurements=[]),t.defaults&&(r.type=t.enums===String?"APP_COLD_START":0,r.url=""),null!=e.type&&e.hasOwnProperty("type")&&(r.type=t.enums===String?b.lr.performance.AppStartTiming.AppStartType[e.type]:e.type),e.measurements&&e.measurements.length){r.measurements=[];for(var n=0;n<e.measurements.length;++n)r.measurements[n]=b.lr.performance.AppStartTiming.Measurement.toObject(e.measurements[n],t)}return null!=e.url&&e.hasOwnProperty("url")&&(r.url=e.url),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.AppStartType=function(){var e={},t=Object.create(e);return t[e[0]="APP_COLD_START"]=0,t[e[1]="APP_WARM_START"]=1,t[e[2]="APP_HOT_START"]=2,t}(),e.Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.type=0,e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.encode=function(e,t){return t||(t=g.create()),null!=e.type&&e.hasOwnProperty("type")&&t.uint32(8).int32(e.type),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(16).int64(e.timestamp),t},e.fromObject=function(e){if(e instanceof b.lr.performance.AppStartTiming.Measurement)return e;var t=new b.lr.performance.AppStartTiming.Measurement;switch(e.type){case"APPLICATION_INITIALIZED":case 0:t.type=0;break;case"ACTIVITY_STARTED":case 1:t.type=1;break;case"ACTIVITY_RESUMED":case 2:t.type=2;break;case"CONTENT_PROVIDER_CREATED":case 3:t.type=3;break;case"ACTIVITY_CREATED":case 4:t.type=4;break;case"APPLICATION_RUNTIME_INITIALIZED":case 5:t.type=5;break;case"DID_FINISH_LAUNCHING":case 6:t.type=6;break;case"WINDOW_DID_BECOME_VISIBLE":case 7:t.type=7}return null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults)if(r.type=t.enums===String?"APPLICATION_INITIALIZED":0,O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;return null!=e.type&&e.hasOwnProperty("type")&&(r.type=t.enums===String?b.lr.performance.AppStartTiming.Measurement.MeasurementType[e.type]:e.type),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.MeasurementType=function(){var e={},t=Object.create(e);return t[e[0]="APPLICATION_INITIALIZED"]=0,t[e[1]="ACTIVITY_STARTED"]=1,t[e[2]="ACTIVITY_RESUMED"]=2,t[e[3]="CONTENT_PROVIDER_CREATED"]=3,t[e[4]="ACTIVITY_CREATED"]=4,t[e[5]="APPLICATION_RUNTIME_INITIALIZED"]=5,t[e[6]="DID_FINISH_LAUNCHING"]=6,t[e[7]="WINDOW_DID_BECOME_VISIBLE"]=7,t}(),e}(),e}(),e}(),y.metrics=((p={}).Measurement=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.timestamp=O.Long?O.Long.fromBits(0,0,!1):0,e.prototype.value=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&t.uint32(8).int64(e.timestamp),null!=e.value&&e.hasOwnProperty("value")&&t.uint32(17).double(e.value),t},e.fromObject=function(e){if(e instanceof b.lr.metrics.Measurement)return e;var t=new b.lr.metrics.Measurement;return null!=e.timestamp&&(O.Long?(t.timestamp=O.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),null!=e.value&&(t.value=Number(e.value)),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if(O.Long){var n=new O.Long(0,0,!1);r.timestamp=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timestamp=t.longs===String?"0":0;r.value=0}return null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?r.timestamp=t.longs===String?String(e.timestamp):e.timestamp:r.timestamp=t.longs===String?O.Long.prototype.toString.call(e.timestamp):t.longs===Number?new O.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),null!=e.value&&e.hasOwnProperty("value")&&(r.value=t.json&&!isFinite(e.value)?String(e.value):e.value),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),p.Metric=function(){function e(e){if(this.measurements=[],this.extra={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.metricType=0,e.prototype.measurements=O.emptyArray,e.prototype.extra=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.metricType&&e.hasOwnProperty("metricType")&&t.uint32(8).int32(e.metricType),null!=e.measurements&&e.measurements.length)for(var r=0;r<e.measurements.length;++r)b.lr.metrics.Measurement.encode(e.measurements[r],t.uint32(18).fork()).ldelim();if(null!=e.extra&&e.hasOwnProperty("extra")){var n=Object.keys(e.extra);for(r=0;r<n.length;++r)t.uint32(26).fork().uint32(10).string(n[r]).uint32(18).string(e.extra[n[r]]).ldelim()}return t},e.fromObject=function(e){if(e instanceof b.lr.metrics.Metric)return e;var t=new b.lr.metrics.Metric;switch(e.metricType){case"timeToFirstByte":case 0:t.metricType=0;break;case"largestContentfulPaintTime":case 1:t.metricType=1;break;case"initialPageLoadTime":case 2:t.metricType=2;break;case"firstInputDelay":case 3:t.metricType=3;break;case"cumulativeLayoutShift":case 4:t.metricType=4;break;case"mobileFrameRenderTime":case 5:t.metricType=5}if(e.measurements){if(!Array.isArray(e.measurements))throw TypeError(".lr.metrics.Metric.measurements: array expected");t.measurements=[];for(var r=0;r<e.measurements.length;++r){if("object"!=typeof e.measurements[r])throw TypeError(".lr.metrics.Metric.measurements: object expected");t.measurements[r]=b.lr.metrics.Measurement.fromObject(e.measurements[r])}}if(e.extra){if("object"!=typeof e.extra)throw TypeError(".lr.metrics.Metric.extra: object expected");t.extra={};var n=Object.keys(e.extra);for(r=0;r<n.length;++r)t.extra[n[r]]=String(e.extra[n[r]])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.measurements=[]),(t.objects||t.defaults)&&(n.extra={}),t.defaults&&(n.metricType=t.enums===String?"timeToFirstByte":0),null!=e.metricType&&e.hasOwnProperty("metricType")&&(n.metricType=t.enums===String?b.lr.metrics.Metric.MetricType[e.metricType]:e.metricType),e.measurements&&e.measurements.length){n.measurements=[];for(var o=0;o<e.measurements.length;++o)n.measurements[o]=b.lr.metrics.Measurement.toObject(e.measurements[o],t)}if(e.extra&&(r=Object.keys(e.extra)).length)for(n.extra={},o=0;o<r.length;++o)n.extra[r[o]]=e.extra[r[o]];return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.MetricType=function(){var e={},t=Object.create(e);return t[e[0]="timeToFirstByte"]=0,t[e[1]="largestContentfulPaintTime"]=1,t[e[2]="initialPageLoadTime"]=2,t[e[3]="firstInputDelay"]=3,t[e[4]="cumulativeLayoutShift"]=4,t[e[5]="mobileFrameRenderTime"]=5,t}(),e}(),p),y.redux=((d={}).InitialState=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.storeId=0,e.prototype.state=null,e.encode=function(e,t){return t||(t=g.create()),null!=e.storeId&&e.hasOwnProperty("storeId")&&t.uint32(8).uint32(e.storeId),null!=e.state&&e.hasOwnProperty("state")&&b.lr.utils.Value.encode(e.state,t.uint32(18).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.redux.InitialState)return e;var t=new b.lr.redux.InitialState;if(null!=e.storeId&&(t.storeId=e.storeId>>>0),null!=e.state){if("object"!=typeof e.state)throw TypeError(".lr.redux.InitialState.state: object expected");t.state=b.lr.utils.Value.fromObject(e.state)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.storeId=0,r.state=null),null!=e.storeId&&e.hasOwnProperty("storeId")&&(r.storeId=e.storeId),null!=e.state&&e.hasOwnProperty("state")&&(r.state=b.lr.utils.Value.toObject(e.state,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),d.ReduxAction=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.action=null,e.prototype.duration=0,e.prototype.storeId=0,e.prototype.stateDelta=null,e.prototype.state=null,e.prototype.count=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.action&&e.hasOwnProperty("action")&&b.lr.utils.Value.encode(e.action,t.uint32(10).fork()).ldelim(),null!=e.duration&&e.hasOwnProperty("duration")&&t.uint32(21).float(e.duration),null!=e.storeId&&e.hasOwnProperty("storeId")&&t.uint32(24).uint32(e.storeId),null!=e.stateDelta&&e.hasOwnProperty("stateDelta")&&b.lr.utils.Value.encode(e.stateDelta,t.uint32(34).fork()).ldelim(),null!=e.state&&e.hasOwnProperty("state")&&b.lr.utils.Value.encode(e.state,t.uint32(42).fork()).ldelim(),null!=e.count&&e.hasOwnProperty("count")&&t.uint32(48).uint32(e.count),t},e.fromObject=function(e){if(e instanceof b.lr.redux.ReduxAction)return e;var t=new b.lr.redux.ReduxAction;if(null!=e.action){if("object"!=typeof e.action)throw TypeError(".lr.redux.ReduxAction.action: object expected");t.action=b.lr.utils.Value.fromObject(e.action)}if(null!=e.duration&&(t.duration=Number(e.duration)),null!=e.storeId&&(t.storeId=e.storeId>>>0),null!=e.stateDelta){if("object"!=typeof e.stateDelta)throw TypeError(".lr.redux.ReduxAction.stateDelta: object expected");t.stateDelta=b.lr.utils.Value.fromObject(e.stateDelta)}if(null!=e.state){if("object"!=typeof e.state)throw TypeError(".lr.redux.ReduxAction.state: object expected");t.state=b.lr.utils.Value.fromObject(e.state)}return null!=e.count&&(t.count=e.count>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.action=null,r.duration=0,r.storeId=0,r.stateDelta=null,r.state=null,r.count=0),null!=e.action&&e.hasOwnProperty("action")&&(r.action=b.lr.utils.Value.toObject(e.action,t)),null!=e.duration&&e.hasOwnProperty("duration")&&(r.duration=t.json&&!isFinite(e.duration)?String(e.duration):e.duration),null!=e.storeId&&e.hasOwnProperty("storeId")&&(r.storeId=e.storeId),null!=e.stateDelta&&e.hasOwnProperty("stateDelta")&&(r.stateDelta=b.lr.utils.Value.toObject(e.stateDelta,t)),null!=e.state&&e.hasOwnProperty("state")&&(r.state=b.lr.utils.Value.toObject(e.state,t)),null!=e.count&&e.hasOwnProperty("count")&&(r.count=e.count),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),d),y.error=((h={}).Truncated=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.reason="",e.encode=function(e,t){return t||(t=g.create()),null!=e.reason&&e.hasOwnProperty("reason")&&t.uint32(10).string(e.reason),t},e.fromObject=function(e){if(e instanceof b.lr.error.Truncated)return e;var t=new b.lr.error.Truncated;return null!=e.reason&&(t.reason=String(e.reason)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.reason=""),null!=e.reason&&e.hasOwnProperty("reason")&&(r.reason=e.reason),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),h),y.EventList=function(){function e(e){if(this.events=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.events=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.events&&e.events.length)for(var r=0;r<e.events.length;++r)b.lr.Event.encode(e.events[r],t.uint32(10).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.EventList)return e;var t=new b.lr.EventList;if(e.events){if(!Array.isArray(e.events))throw TypeError(".lr.EventList.events: array expected");t.events=[];for(var r=0;r<e.events.length;++r){if("object"!=typeof e.events[r])throw TypeError(".lr.EventList.events: object expected");t.events[r]=b.lr.Event.fromObject(e.events[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.events=[]),e.events&&e.events.length){r.events=[];for(var n=0;n<e.events.length;++n)r.events[n]=b.lr.Event.toObject(e.events[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.InitialPageLoadMetrics=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.initialPageLoadTime=0,e.prototype.timeToFirstByte=0,e.prototype.largestContentfulPaintTime=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.initialPageLoadTime&&e.hasOwnProperty("initialPageLoadTime")&&t.uint32(8).uint32(e.initialPageLoadTime),null!=e.timeToFirstByte&&e.hasOwnProperty("timeToFirstByte")&&t.uint32(16).uint32(e.timeToFirstByte),null!=e.largestContentfulPaintTime&&e.hasOwnProperty("largestContentfulPaintTime")&&t.uint32(24).uint32(e.largestContentfulPaintTime),t},e.fromObject=function(e){if(e instanceof b.lr.InitialPageLoadMetrics)return e;var t=new b.lr.InitialPageLoadMetrics;return null!=e.initialPageLoadTime&&(t.initialPageLoadTime=e.initialPageLoadTime>>>0),null!=e.timeToFirstByte&&(t.timeToFirstByte=e.timeToFirstByte>>>0),null!=e.largestContentfulPaintTime&&(t.largestContentfulPaintTime=e.largestContentfulPaintTime>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.initialPageLoadTime=0,r.timeToFirstByte=0,r.largestContentfulPaintTime=0),null!=e.initialPageLoadTime&&e.hasOwnProperty("initialPageLoadTime")&&(r.initialPageLoadTime=e.initialPageLoadTime),null!=e.timeToFirstByte&&e.hasOwnProperty("timeToFirstByte")&&(r.timeToFirstByte=e.timeToFirstByte),null!=e.largestContentfulPaintTime&&e.hasOwnProperty("largestContentfulPaintTime")&&(r.largestContentfulPaintTime=e.largestContentfulPaintTime),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.Metadata=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.release="",e.prototype.username="",e.prototype.browserInfo=null,e.prototype.deviceInfo=null,e.prototype.sdkVersion="",e.prototype.scriptVersion="",e.prototype.country="",e.prototype.region="",e.prototype.city="",e.prototype.latitude=0,e.prototype.longitude=0,e.prototype.ip="",e.prototype.isInactive=!1,e.prototype.outsideIntegrations=null,e.prototype.initialPageLoadMetrics=null,e.prototype.scriptSrc="",e.prototype.appInfo=null,e.prototype.replayType=0,e.prototype.sdkOptions=null,e.prototype.sdkState=null,Object.defineProperty(e.prototype,"platform",{get:O.oneOfGetter(t=["browserInfo","deviceInfo"]),set:O.oneOfSetter(t)}),e.encode=function(e,t){return t||(t=g.create()),null!=e.release&&e.hasOwnProperty("release")&&t.uint32(10).string(e.release),null!=e.username&&e.hasOwnProperty("username")&&t.uint32(18).string(e.username),null!=e.browserInfo&&e.hasOwnProperty("browserInfo")&&b.lr.Metadata.BrowserInfo.encode(e.browserInfo,t.uint32(26).fork()).ldelim(),null!=e.sdkVersion&&e.hasOwnProperty("sdkVersion")&&t.uint32(34).string(e.sdkVersion),null!=e.scriptVersion&&e.hasOwnProperty("scriptVersion")&&t.uint32(42).string(e.scriptVersion),null!=e.country&&e.hasOwnProperty("country")&&t.uint32(50).string(e.country),null!=e.region&&e.hasOwnProperty("region")&&t.uint32(58).string(e.region),null!=e.city&&e.hasOwnProperty("city")&&t.uint32(66).string(e.city),null!=e.latitude&&e.hasOwnProperty("latitude")&&t.uint32(77).float(e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&t.uint32(85).float(e.longitude),null!=e.ip&&e.hasOwnProperty("ip")&&t.uint32(90).string(e.ip),null!=e.isInactive&&e.hasOwnProperty("isInactive")&&t.uint32(96).bool(e.isInactive),null!=e.outsideIntegrations&&e.hasOwnProperty("outsideIntegrations")&&b.lr.Metadata.OutsideIntegrations.encode(e.outsideIntegrations,t.uint32(106).fork()).ldelim(),null!=e.initialPageLoadMetrics&&e.hasOwnProperty("initialPageLoadMetrics")&&b.lr.InitialPageLoadMetrics.encode(e.initialPageLoadMetrics,t.uint32(122).fork()).ldelim(),null!=e.deviceInfo&&e.hasOwnProperty("deviceInfo")&&b.lr.Metadata.DeviceInfo.encode(e.deviceInfo,t.uint32(130).fork()).ldelim(),null!=e.scriptSrc&&e.hasOwnProperty("scriptSrc")&&t.uint32(138).string(e.scriptSrc),null!=e.appInfo&&e.hasOwnProperty("appInfo")&&b.lr.Metadata.AppInfo.encode(e.appInfo,t.uint32(146).fork()).ldelim(),null!=e.replayType&&e.hasOwnProperty("replayType")&&t.uint32(152).int32(e.replayType),null!=e.sdkOptions&&e.hasOwnProperty("sdkOptions")&&b.lr.Metadata.SDKOptions.encode(e.sdkOptions,t.uint32(162).fork()).ldelim(),null!=e.sdkState&&e.hasOwnProperty("sdkState")&&b.lr.Metadata.SDKState.encode(e.sdkState,t.uint32(170).fork()).ldelim(),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata)return e;var t=new b.lr.Metadata;if(null!=e.release&&(t.release=String(e.release)),null!=e.username&&(t.username=String(e.username)),null!=e.browserInfo){if("object"!=typeof e.browserInfo)throw TypeError(".lr.Metadata.browserInfo: object expected");t.browserInfo=b.lr.Metadata.BrowserInfo.fromObject(e.browserInfo)}if(null!=e.deviceInfo){if("object"!=typeof e.deviceInfo)throw TypeError(".lr.Metadata.deviceInfo: object expected");t.deviceInfo=b.lr.Metadata.DeviceInfo.fromObject(e.deviceInfo)}if(null!=e.sdkVersion&&(t.sdkVersion=String(e.sdkVersion)),null!=e.scriptVersion&&(t.scriptVersion=String(e.scriptVersion)),null!=e.country&&(t.country=String(e.country)),null!=e.region&&(t.region=String(e.region)),null!=e.city&&(t.city=String(e.city)),null!=e.latitude&&(t.latitude=Number(e.latitude)),null!=e.longitude&&(t.longitude=Number(e.longitude)),null!=e.ip&&(t.ip=String(e.ip)),null!=e.isInactive&&(t.isInactive=Boolean(e.isInactive)),null!=e.outsideIntegrations){if("object"!=typeof e.outsideIntegrations)throw TypeError(".lr.Metadata.outsideIntegrations: object expected");t.outsideIntegrations=b.lr.Metadata.OutsideIntegrations.fromObject(e.outsideIntegrations)}if(null!=e.initialPageLoadMetrics){if("object"!=typeof e.initialPageLoadMetrics)throw TypeError(".lr.Metadata.initialPageLoadMetrics: object expected");t.initialPageLoadMetrics=b.lr.InitialPageLoadMetrics.fromObject(e.initialPageLoadMetrics)}if(null!=e.scriptSrc&&(t.scriptSrc=String(e.scriptSrc)),null!=e.appInfo){if("object"!=typeof e.appInfo)throw TypeError(".lr.Metadata.appInfo: object expected");t.appInfo=b.lr.Metadata.AppInfo.fromObject(e.appInfo)}switch(e.replayType){case"UNKNOWN":case 0:t.replayType=0;break;case"DOM":case 1:t.replayType=1;break;case"SKIA":case 2:t.replayType=2;break;case"PDF":case 3:t.replayType=3;break;case"PIXEL":case 4:t.replayType=4}if(null!=e.sdkOptions){if("object"!=typeof e.sdkOptions)throw TypeError(".lr.Metadata.sdkOptions: object expected");t.sdkOptions=b.lr.Metadata.SDKOptions.fromObject(e.sdkOptions)}if(null!=e.sdkState){if("object"!=typeof e.sdkState)throw TypeError(".lr.Metadata.sdkState: object expected");t.sdkState=b.lr.Metadata.SDKState.fromObject(e.sdkState)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.release="",r.username="",r.sdkVersion="",r.scriptVersion="",r.country="",r.region="",r.city="",r.latitude=0,r.longitude=0,r.ip="",r.isInactive=!1,r.outsideIntegrations=null,r.initialPageLoadMetrics=null,r.scriptSrc="",r.appInfo=null,r.replayType=t.enums===String?"UNKNOWN":0,r.sdkOptions=null,r.sdkState=null),null!=e.release&&e.hasOwnProperty("release")&&(r.release=e.release),null!=e.username&&e.hasOwnProperty("username")&&(r.username=e.username),null!=e.browserInfo&&e.hasOwnProperty("browserInfo")&&(r.browserInfo=b.lr.Metadata.BrowserInfo.toObject(e.browserInfo,t),t.oneofs&&(r.platform="browserInfo")),null!=e.sdkVersion&&e.hasOwnProperty("sdkVersion")&&(r.sdkVersion=e.sdkVersion),null!=e.scriptVersion&&e.hasOwnProperty("scriptVersion")&&(r.scriptVersion=e.scriptVersion),null!=e.country&&e.hasOwnProperty("country")&&(r.country=e.country),null!=e.region&&e.hasOwnProperty("region")&&(r.region=e.region),null!=e.city&&e.hasOwnProperty("city")&&(r.city=e.city),null!=e.latitude&&e.hasOwnProperty("latitude")&&(r.latitude=t.json&&!isFinite(e.latitude)?String(e.latitude):e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&(r.longitude=t.json&&!isFinite(e.longitude)?String(e.longitude):e.longitude),null!=e.ip&&e.hasOwnProperty("ip")&&(r.ip=e.ip),null!=e.isInactive&&e.hasOwnProperty("isInactive")&&(r.isInactive=e.isInactive),null!=e.outsideIntegrations&&e.hasOwnProperty("outsideIntegrations")&&(r.outsideIntegrations=b.lr.Metadata.OutsideIntegrations.toObject(e.outsideIntegrations,t)),null!=e.initialPageLoadMetrics&&e.hasOwnProperty("initialPageLoadMetrics")&&(r.initialPageLoadMetrics=b.lr.InitialPageLoadMetrics.toObject(e.initialPageLoadMetrics,t)),null!=e.deviceInfo&&e.hasOwnProperty("deviceInfo")&&(r.deviceInfo=b.lr.Metadata.DeviceInfo.toObject(e.deviceInfo,t),t.oneofs&&(r.platform="deviceInfo")),null!=e.scriptSrc&&e.hasOwnProperty("scriptSrc")&&(r.scriptSrc=e.scriptSrc),null!=e.appInfo&&e.hasOwnProperty("appInfo")&&(r.appInfo=b.lr.Metadata.AppInfo.toObject(e.appInfo,t)),null!=e.replayType&&e.hasOwnProperty("replayType")&&(r.replayType=t.enums===String?b.lr.Metadata.ReplayType[e.replayType]:e.replayType),null!=e.sdkOptions&&e.hasOwnProperty("sdkOptions")&&(r.sdkOptions=b.lr.Metadata.SDKOptions.toObject(e.sdkOptions,t)),null!=e.sdkState&&e.hasOwnProperty("sdkState")&&(r.sdkState=b.lr.Metadata.SDKState.toObject(e.sdkState,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.ReplayType=function(){var e={},t=Object.create(e);return t[e[0]="UNKNOWN"]=0,t[e[1]="DOM"]=1,t[e[2]="SKIA"]=2,t[e[3]="PDF"]=3,t[e[4]="PIXEL"]=4,t}(),e.BrowserInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.href="",e.prototype.language="",e.prototype.platform="",e.prototype.useragent="",e.prototype.version="",e.prototype.referrer="",e.prototype.isLocalhost=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(10).string(e.href),null!=e.language&&e.hasOwnProperty("language")&&t.uint32(26).string(e.language),null!=e.platform&&e.hasOwnProperty("platform")&&t.uint32(42).string(e.platform),null!=e.useragent&&e.hasOwnProperty("useragent")&&t.uint32(50).string(e.useragent),null!=e.version&&e.hasOwnProperty("version")&&t.uint32(58).string(e.version),null!=e.referrer&&e.hasOwnProperty("referrer")&&t.uint32(66).string(e.referrer),null!=e.isLocalhost&&e.hasOwnProperty("isLocalhost")&&t.uint32(72).bool(e.isLocalhost),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.BrowserInfo)return e;var t=new b.lr.Metadata.BrowserInfo;return null!=e.href&&(t.href=String(e.href)),null!=e.language&&(t.language=String(e.language)),null!=e.platform&&(t.platform=String(e.platform)),null!=e.useragent&&(t.useragent=String(e.useragent)),null!=e.version&&(t.version=String(e.version)),null!=e.referrer&&(t.referrer=String(e.referrer)),null!=e.isLocalhost&&(t.isLocalhost=Boolean(e.isLocalhost)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.href="",r.language="",r.platform="",r.useragent="",r.version="",r.referrer="",r.isLocalhost=!1),null!=e.href&&e.hasOwnProperty("href")&&(r.href=e.href),null!=e.language&&e.hasOwnProperty("language")&&(r.language=e.language),null!=e.platform&&e.hasOwnProperty("platform")&&(r.platform=e.platform),null!=e.useragent&&e.hasOwnProperty("useragent")&&(r.useragent=e.useragent),null!=e.version&&e.hasOwnProperty("version")&&(r.version=e.version),null!=e.referrer&&e.hasOwnProperty("referrer")&&(r.referrer=e.referrer),null!=e.isLocalhost&&e.hasOwnProperty("isLocalhost")&&(r.isLocalhost=e.isLocalhost),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.DeviceInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.deviceType=0,e.prototype.deviceName="",e.prototype.manufacturer="",e.prototype.release="",e.prototype.numberOfCpuCores=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.deviceType&&e.hasOwnProperty("deviceType")&&t.uint32(8).int32(e.deviceType),null!=e.deviceName&&e.hasOwnProperty("deviceName")&&t.uint32(18).string(e.deviceName),null!=e.manufacturer&&e.hasOwnProperty("manufacturer")&&t.uint32(26).string(e.manufacturer),null!=e.release&&e.hasOwnProperty("release")&&t.uint32(34).string(e.release),null!=e.numberOfCpuCores&&e.hasOwnProperty("numberOfCpuCores")&&t.uint32(40).uint32(e.numberOfCpuCores),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.DeviceInfo)return e;var t=new b.lr.Metadata.DeviceInfo;switch(e.deviceType){case"ANDROID":case 0:t.deviceType=0;break;case"IOS":case 1:t.deviceType=1}return null!=e.deviceName&&(t.deviceName=String(e.deviceName)),null!=e.manufacturer&&(t.manufacturer=String(e.manufacturer)),null!=e.release&&(t.release=String(e.release)),null!=e.numberOfCpuCores&&(t.numberOfCpuCores=e.numberOfCpuCores>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.deviceType=t.enums===String?"ANDROID":0,r.deviceName="",r.manufacturer="",r.release="",r.numberOfCpuCores=0),null!=e.deviceType&&e.hasOwnProperty("deviceType")&&(r.deviceType=t.enums===String?b.lr.Metadata.DeviceInfo.DeviceType[e.deviceType]:e.deviceType),null!=e.deviceName&&e.hasOwnProperty("deviceName")&&(r.deviceName=e.deviceName),null!=e.manufacturer&&e.hasOwnProperty("manufacturer")&&(r.manufacturer=e.manufacturer),null!=e.release&&e.hasOwnProperty("release")&&(r.release=e.release),null!=e.numberOfCpuCores&&e.hasOwnProperty("numberOfCpuCores")&&(r.numberOfCpuCores=e.numberOfCpuCores),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.DeviceType=function(){var e={},t=Object.create(e);return t[e[0]="ANDROID"]=0,t[e[1]="IOS"]=1,t}(),e}(),e.AppInfo=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.appName="",e.encode=function(e,t){return t||(t=g.create()),null!=e.appName&&e.hasOwnProperty("appName")&&t.uint32(10).string(e.appName),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.AppInfo)return e;var t=new b.lr.Metadata.AppInfo;return null!=e.appName&&(t.appName=String(e.appName)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.appName=""),null!=e.appName&&e.hasOwnProperty("appName")&&(r.appName=e.appName),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.OutsideIntegrations=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.hasIntercom=!1,e.prototype.hasZendesk=!1,e.prototype.hasDesk=!1,e.prototype.hasZendeskChat=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.hasIntercom&&e.hasOwnProperty("hasIntercom")&&t.uint32(8).bool(e.hasIntercom),null!=e.hasZendesk&&e.hasOwnProperty("hasZendesk")&&t.uint32(16).bool(e.hasZendesk),null!=e.hasDesk&&e.hasOwnProperty("hasDesk")&&t.uint32(24).bool(e.hasDesk),null!=e.hasZendeskChat&&e.hasOwnProperty("hasZendeskChat")&&t.uint32(32).bool(e.hasZendeskChat),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.OutsideIntegrations)return e;var t=new b.lr.Metadata.OutsideIntegrations;return null!=e.hasIntercom&&(t.hasIntercom=Boolean(e.hasIntercom)),null!=e.hasZendesk&&(t.hasZendesk=Boolean(e.hasZendesk)),null!=e.hasDesk&&(t.hasDesk=Boolean(e.hasDesk)),null!=e.hasZendeskChat&&(t.hasZendeskChat=Boolean(e.hasZendeskChat)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.hasIntercom=!1,r.hasZendesk=!1,r.hasDesk=!1,r.hasZendeskChat=!1),null!=e.hasIntercom&&e.hasOwnProperty("hasIntercom")&&(r.hasIntercom=e.hasIntercom),null!=e.hasZendesk&&e.hasOwnProperty("hasZendesk")&&(r.hasZendesk=e.hasZendesk),null!=e.hasDesk&&e.hasOwnProperty("hasDesk")&&(r.hasDesk=e.hasDesk),null!=e.hasZendeskChat&&e.hasOwnProperty("hasZendeskChat")&&(r.hasZendeskChat=e.hasZendeskChat),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.SDKOptions=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.shouldAggregateConsoleErrors=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.shouldAggregateConsoleErrors&&e.hasOwnProperty("shouldAggregateConsoleErrors")&&t.uint32(8).bool(e.shouldAggregateConsoleErrors),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.SDKOptions)return e;var t=new b.lr.Metadata.SDKOptions;return null!=e.shouldAggregateConsoleErrors&&(t.shouldAggregateConsoleErrors=Boolean(e.shouldAggregateConsoleErrors)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.shouldAggregateConsoleErrors=!1),null!=e.shouldAggregateConsoleErrors&&e.hasOwnProperty("shouldAggregateConsoleErrors")&&(r.shouldAggregateConsoleErrors=e.shouldAggregateConsoleErrors),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.SDKState=function(){function e(e){if(this.logs=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.config="",e.prototype.isIframe=!1,e.prototype.isCrossDomainIframe=!1,e.prototype.isWebView=!1,e.prototype.skippedExistingPersistedData=!1,e.prototype.startTime=0,e.prototype.originalStartTime=0,e.prototype.loadTime=0,e.prototype.logs=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.config&&e.hasOwnProperty("config")&&t.uint32(10).string(e.config),null!=e.isIframe&&e.hasOwnProperty("isIframe")&&t.uint32(16).bool(e.isIframe),null!=e.isCrossDomainIframe&&e.hasOwnProperty("isCrossDomainIframe")&&t.uint32(24).bool(e.isCrossDomainIframe),null!=e.skippedExistingPersistedData&&e.hasOwnProperty("skippedExistingPersistedData")&&t.uint32(32).bool(e.skippedExistingPersistedData),null!=e.startTime&&e.hasOwnProperty("startTime")&&t.uint32(41).double(e.startTime),null!=e.originalStartTime&&e.hasOwnProperty("originalStartTime")&&t.uint32(49).double(e.originalStartTime),null!=e.loadTime&&e.hasOwnProperty("loadTime")&&t.uint32(57).double(e.loadTime),null!=e.logs&&e.logs.length)for(var r=0;r<e.logs.length;++r)b.lr.Metadata.SDKState.StateLog.encode(e.logs[r],t.uint32(66).fork()).ldelim();return null!=e.isWebView&&e.hasOwnProperty("isWebView")&&t.uint32(72).bool(e.isWebView),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.SDKState)return e;var t=new b.lr.Metadata.SDKState;if(null!=e.config&&(t.config=String(e.config)),null!=e.isIframe&&(t.isIframe=Boolean(e.isIframe)),null!=e.isCrossDomainIframe&&(t.isCrossDomainIframe=Boolean(e.isCrossDomainIframe)),null!=e.isWebView&&(t.isWebView=Boolean(e.isWebView)),null!=e.skippedExistingPersistedData&&(t.skippedExistingPersistedData=Boolean(e.skippedExistingPersistedData)),null!=e.startTime&&(t.startTime=Number(e.startTime)),null!=e.originalStartTime&&(t.originalStartTime=Number(e.originalStartTime)),null!=e.loadTime&&(t.loadTime=Number(e.loadTime)),e.logs){if(!Array.isArray(e.logs))throw TypeError(".lr.Metadata.SDKState.logs: array expected");t.logs=[];for(var r=0;r<e.logs.length;++r){if("object"!=typeof e.logs[r])throw TypeError(".lr.Metadata.SDKState.logs: object expected");t.logs[r]=b.lr.Metadata.SDKState.StateLog.fromObject(e.logs[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.logs=[]),t.defaults&&(r.config="",r.isIframe=!1,r.isCrossDomainIframe=!1,r.skippedExistingPersistedData=!1,r.startTime=0,r.originalStartTime=0,r.loadTime=0,r.isWebView=!1),null!=e.config&&e.hasOwnProperty("config")&&(r.config=e.config),null!=e.isIframe&&e.hasOwnProperty("isIframe")&&(r.isIframe=e.isIframe),null!=e.isCrossDomainIframe&&e.hasOwnProperty("isCrossDomainIframe")&&(r.isCrossDomainIframe=e.isCrossDomainIframe),null!=e.skippedExistingPersistedData&&e.hasOwnProperty("skippedExistingPersistedData")&&(r.skippedExistingPersistedData=e.skippedExistingPersistedData),null!=e.startTime&&e.hasOwnProperty("startTime")&&(r.startTime=t.json&&!isFinite(e.startTime)?String(e.startTime):e.startTime),null!=e.originalStartTime&&e.hasOwnProperty("originalStartTime")&&(r.originalStartTime=t.json&&!isFinite(e.originalStartTime)?String(e.originalStartTime):e.originalStartTime),null!=e.loadTime&&e.hasOwnProperty("loadTime")&&(r.loadTime=t.json&&!isFinite(e.loadTime)?String(e.loadTime):e.loadTime),e.logs&&e.logs.length){r.logs=[];for(var n=0;n<e.logs.length;++n)r.logs[n]=b.lr.Metadata.SDKState.StateLog.toObject(e.logs[n],t)}return null!=e.isWebView&&e.hasOwnProperty("isWebView")&&(r.isWebView=e.isWebView),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.StateLog=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.time=0,e.prototype.message="",e.encode=function(e,t){return t||(t=g.create()),null!=e.time&&e.hasOwnProperty("time")&&t.uint32(9).double(e.time),null!=e.message&&e.hasOwnProperty("message")&&t.uint32(18).string(e.message),t},e.fromObject=function(e){if(e instanceof b.lr.Metadata.SDKState.StateLog)return e;var t=new b.lr.Metadata.SDKState.StateLog;return null!=e.time&&(t.time=Number(e.time)),null!=e.message&&(t.message=String(e.message)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.time=0,r.message=""),null!=e.time&&e.hasOwnProperty("time")&&(r.time=t.json&&!isFinite(e.time)?String(e.time):e.time),null!=e.message&&e.hasOwnProperty("message")&&(r.message=e.message),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),e}(),y.Event=function(){function e(e){if(this.stackTrace=[],this.fileMap={},this.causalSeqIDs=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.time=0,e.prototype.type="",e.prototype.data=O.newBuffer([]),e.prototype.threadID=0,e.prototype.seqID=0,e.prototype.stackTrace=O.emptyArray,e.prototype.fileMap=O.emptyObject,e.prototype.sessionID=0,e.prototype.timeOffset=0,e.prototype.platformType=0,e.prototype.canSkipAssetCache=!1,e.prototype.causalSeqIDs=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.time&&e.hasOwnProperty("time")&&t.uint32(9).double(e.time),null!=e.type&&e.hasOwnProperty("type")&&t.uint32(18).string(e.type),null!=e.data&&e.hasOwnProperty("data")&&t.uint32(26).bytes(e.data),null!=e.threadID&&e.hasOwnProperty("threadID")&&t.uint32(32).uint32(e.threadID),null!=e.seqID&&e.hasOwnProperty("seqID")&&t.uint32(40).uint32(e.seqID),null!=e.stackTrace&&e.stackTrace.length)for(var r=0;r<e.stackTrace.length;++r)b.lr.Event.StackFrame.encode(e.stackTrace[r],t.uint32(50).fork()).ldelim();if(null!=e.fileMap&&e.hasOwnProperty("fileMap")){var n=Object.keys(e.fileMap);for(r=0;r<n.length;++r)t.uint32(74).fork().uint32(10).string(n[r]).uint32(18).string(e.fileMap[n[r]]).ldelim()}if(null!=e.sessionID&&e.hasOwnProperty("sessionID")&&t.uint32(80).uint32(e.sessionID),null!=e.timeOffset&&e.hasOwnProperty("timeOffset")&&t.uint32(89).double(e.timeOffset),null!=e.platformType&&e.hasOwnProperty("platformType")&&t.uint32(96).int32(e.platformType),null!=e.canSkipAssetCache&&e.hasOwnProperty("canSkipAssetCache")&&t.uint32(104).bool(e.canSkipAssetCache),null!=e.causalSeqIDs&&e.causalSeqIDs.length){for(t.uint32(114).fork(),r=0;r<e.causalSeqIDs.length;++r)t.uint32(e.causalSeqIDs[r]);t.ldelim()}return t},e.fromObject=function(e){if(e instanceof b.lr.Event)return e;var t=new b.lr.Event;if(null!=e.time&&(t.time=Number(e.time)),null!=e.type&&(t.type=String(e.type)),null!=e.data&&("string"==typeof e.data?O.base64.decode(e.data,t.data=O.newBuffer(O.base64.length(e.data)),0):e.data.length&&(t.data=e.data)),null!=e.threadID&&(t.threadID=e.threadID>>>0),null!=e.seqID&&(t.seqID=e.seqID>>>0),e.stackTrace){if(!Array.isArray(e.stackTrace))throw TypeError(".lr.Event.stackTrace: array expected");t.stackTrace=[];for(var r=0;r<e.stackTrace.length;++r){if("object"!=typeof e.stackTrace[r])throw TypeError(".lr.Event.stackTrace: object expected");t.stackTrace[r]=b.lr.Event.StackFrame.fromObject(e.stackTrace[r])}}if(e.fileMap){if("object"!=typeof e.fileMap)throw TypeError(".lr.Event.fileMap: object expected");t.fileMap={};var n=Object.keys(e.fileMap);for(r=0;r<n.length;++r)t.fileMap[n[r]]=String(e.fileMap[n[r]])}switch(null!=e.sessionID&&(t.sessionID=e.sessionID>>>0),null!=e.timeOffset&&(t.timeOffset=Number(e.timeOffset)),e.platformType){case"UNKNOWN":case 0:t.platformType=0;break;case"WEB":case 1:t.platformType=1;break;case"ANDROID":case 2:t.platformType=2;break;case"IOS":case 3:t.platformType=3}if(null!=e.canSkipAssetCache&&(t.canSkipAssetCache=Boolean(e.canSkipAssetCache)),e.causalSeqIDs){if(!Array.isArray(e.causalSeqIDs))throw TypeError(".lr.Event.causalSeqIDs: array expected");for(t.causalSeqIDs=[],r=0;r<e.causalSeqIDs.length;++r)t.causalSeqIDs[r]=e.causalSeqIDs[r]>>>0}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.stackTrace=[],n.causalSeqIDs=[]),(t.objects||t.defaults)&&(n.fileMap={}),t.defaults&&(n.time=0,n.type="",n.data=t.bytes===String?"":[],n.threadID=0,n.seqID=0,n.sessionID=0,n.timeOffset=0,n.platformType=t.enums===String?"UNKNOWN":0,n.canSkipAssetCache=!1),null!=e.time&&e.hasOwnProperty("time")&&(n.time=t.json&&!isFinite(e.time)?String(e.time):e.time),null!=e.type&&e.hasOwnProperty("type")&&(n.type=e.type),null!=e.data&&e.hasOwnProperty("data")&&(n.data=t.bytes===String?O.base64.encode(e.data,0,e.data.length):t.bytes===Array?Array.prototype.slice.call(e.data):e.data),null!=e.threadID&&e.hasOwnProperty("threadID")&&(n.threadID=e.threadID),null!=e.seqID&&e.hasOwnProperty("seqID")&&(n.seqID=e.seqID),e.stackTrace&&e.stackTrace.length){n.stackTrace=[];for(var o=0;o<e.stackTrace.length;++o)n.stackTrace[o]=b.lr.Event.StackFrame.toObject(e.stackTrace[o],t)}if(e.fileMap&&(r=Object.keys(e.fileMap)).length)for(n.fileMap={},o=0;o<r.length;++o)n.fileMap[r[o]]=e.fileMap[r[o]];if(null!=e.sessionID&&e.hasOwnProperty("sessionID")&&(n.sessionID=e.sessionID),null!=e.timeOffset&&e.hasOwnProperty("timeOffset")&&(n.timeOffset=t.json&&!isFinite(e.timeOffset)?String(e.timeOffset):e.timeOffset),null!=e.platformType&&e.hasOwnProperty("platformType")&&(n.platformType=t.enums===String?b.lr.Event.PlatformType[e.platformType]:e.platformType),null!=e.canSkipAssetCache&&e.hasOwnProperty("canSkipAssetCache")&&(n.canSkipAssetCache=e.canSkipAssetCache),e.causalSeqIDs&&e.causalSeqIDs.length)for(n.causalSeqIDs=[],o=0;o<e.causalSeqIDs.length;++o)n.causalSeqIDs[o]=e.causalSeqIDs[o];return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.PlatformType=function(){var e={},t=Object.create(e);return t[e[0]="UNKNOWN"]=0,t[e[1]="WEB"]=1,t[e[2]="ANDROID"]=2,t[e[3]="IOS"]=3,t}(),e.StackFrame=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.lineNumber=0,e.prototype.columnNumber=0,e.prototype.fileName="",e.prototype.functionName="",e.prototype.className="",e.prototype.rawStackFrame="",e.encode=function(e,t){return t||(t=g.create()),null!=e.lineNumber&&e.hasOwnProperty("lineNumber")&&t.uint32(8).uint32(e.lineNumber),null!=e.columnNumber&&e.hasOwnProperty("columnNumber")&&t.uint32(16).uint32(e.columnNumber),null!=e.fileName&&e.hasOwnProperty("fileName")&&t.uint32(26).string(e.fileName),null!=e.functionName&&e.hasOwnProperty("functionName")&&t.uint32(34).string(e.functionName),null!=e.className&&e.hasOwnProperty("className")&&t.uint32(42).string(e.className),null!=e.rawStackFrame&&e.hasOwnProperty("rawStackFrame")&&t.uint32(50).string(e.rawStackFrame),t},e.fromObject=function(e){if(e instanceof b.lr.Event.StackFrame)return e;var t=new b.lr.Event.StackFrame;return null!=e.lineNumber&&(t.lineNumber=e.lineNumber>>>0),null!=e.columnNumber&&(t.columnNumber=e.columnNumber>>>0),null!=e.fileName&&(t.fileName=String(e.fileName)),null!=e.functionName&&(t.functionName=String(e.functionName)),null!=e.className&&(t.className=String(e.className)),null!=e.rawStackFrame&&(t.rawStackFrame=String(e.rawStackFrame)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.lineNumber=0,r.columnNumber=0,r.fileName="",r.functionName="",r.className="",r.rawStackFrame=""),null!=e.lineNumber&&e.hasOwnProperty("lineNumber")&&(r.lineNumber=e.lineNumber),null!=e.columnNumber&&e.hasOwnProperty("columnNumber")&&(r.columnNumber=e.columnNumber),null!=e.fileName&&e.hasOwnProperty("fileName")&&(r.fileName=e.fileName),null!=e.functionName&&e.hasOwnProperty("functionName")&&(r.functionName=e.functionName),null!=e.className&&e.hasOwnProperty("className")&&(r.className=e.className),null!=e.rawStackFrame&&e.hasOwnProperty("rawStackFrame")&&(r.rawStackFrame=e.rawStackFrame),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),y.Activate=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.encode=function(e,t){return t||(t=g.create()),t},e.fromObject=function(e){return e instanceof b.lr.Activate?e:new b.lr.Activate},e.toObject=function(){return{}},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.CustomEvent=function(){function e(e){if(this.eventProperties={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.eventName="",e.prototype.eventProperties=O.emptyObject,e.encode=function(e,t){if(t||(t=g.create()),null!=e.eventName&&e.hasOwnProperty("eventName")&&t.uint32(10).string(e.eventName),null!=e.eventProperties&&e.hasOwnProperty("eventProperties"))for(var r=Object.keys(e.eventProperties),n=0;n<r.length;++n)t.uint32(18).fork().uint32(10).string(r[n]),b.lr.CustomEvent.EventPropertyVariant.encode(e.eventProperties[r[n]],t.uint32(18).fork()).ldelim().ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.CustomEvent)return e;var t=new b.lr.CustomEvent;if(null!=e.eventName&&(t.eventName=String(e.eventName)),e.eventProperties){if("object"!=typeof e.eventProperties)throw TypeError(".lr.CustomEvent.eventProperties: object expected");t.eventProperties={};for(var r=Object.keys(e.eventProperties),n=0;n<r.length;++n){if("object"!=typeof e.eventProperties[r[n]])throw TypeError(".lr.CustomEvent.eventProperties: object expected");t.eventProperties[r[n]]=b.lr.CustomEvent.EventPropertyVariant.fromObject(e.eventProperties[r[n]])}}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.eventProperties={}),t.defaults&&(n.eventName=""),null!=e.eventName&&e.hasOwnProperty("eventName")&&(n.eventName=e.eventName),e.eventProperties&&(r=Object.keys(e.eventProperties)).length){n.eventProperties={};for(var o=0;o<r.length;++o)n.eventProperties[r[o]]=b.lr.CustomEvent.EventPropertyVariant.toObject(e.eventProperties[r[o]],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.EventPropertyVariant=function(){function e(e){if(this.stringVal=[],this.doubleVal=[],this.boolVal=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.stringVal=O.emptyArray,e.prototype.doubleVal=O.emptyArray,e.prototype.boolVal=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.stringVal&&e.stringVal.length)for(var r=0;r<e.stringVal.length;++r)t.uint32(10).string(e.stringVal[r]);if(null!=e.doubleVal&&e.doubleVal.length){for(t.uint32(18).fork(),r=0;r<e.doubleVal.length;++r)t.double(e.doubleVal[r]);t.ldelim()}if(null!=e.boolVal&&e.boolVal.length){for(t.uint32(26).fork(),r=0;r<e.boolVal.length;++r)t.bool(e.boolVal[r]);t.ldelim()}return t},e.fromObject=function(e){if(e instanceof b.lr.CustomEvent.EventPropertyVariant)return e;var t=new b.lr.CustomEvent.EventPropertyVariant;if(e.stringVal){if(!Array.isArray(e.stringVal))throw TypeError(".lr.CustomEvent.EventPropertyVariant.stringVal: array expected");t.stringVal=[];for(var r=0;r<e.stringVal.length;++r)t.stringVal[r]=String(e.stringVal[r])}if(e.doubleVal){if(!Array.isArray(e.doubleVal))throw TypeError(".lr.CustomEvent.EventPropertyVariant.doubleVal: array expected");for(t.doubleVal=[],r=0;r<e.doubleVal.length;++r)t.doubleVal[r]=Number(e.doubleVal[r])}if(e.boolVal){if(!Array.isArray(e.boolVal))throw TypeError(".lr.CustomEvent.EventPropertyVariant.boolVal: array expected");for(t.boolVal=[],r=0;r<e.boolVal.length;++r)t.boolVal[r]=Boolean(e.boolVal[r])}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.stringVal=[],r.doubleVal=[],r.boolVal=[]),e.stringVal&&e.stringVal.length){r.stringVal=[];for(var n=0;n<e.stringVal.length;++n)r.stringVal[n]=e.stringVal[n]}if(e.doubleVal&&e.doubleVal.length)for(r.doubleVal=[],n=0;n<e.doubleVal.length;++n)r.doubleVal[n]=t.json&&!isFinite(e.doubleVal[n])?String(e.doubleVal[n]):e.doubleVal[n];if(e.boolVal&&e.boolVal.length)for(r.boolVal=[],n=0;n<e.boolVal.length;++n)r.boolVal[n]=e.boolVal[n];return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),y.Identify=function(){function e(e){if(this.traits={},e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.userID="",e.prototype.name="",e.prototype.email="",e.prototype.isAnonymous=!1,e.prototype.traits=O.emptyObject,e.prototype.isUpdate=!1,e.encode=function(e,t){if(t||(t=g.create()),null!=e.userID&&e.hasOwnProperty("userID")&&t.uint32(10).string(e.userID),null!=e.name&&e.hasOwnProperty("name")&&t.uint32(18).string(e.name),null!=e.email&&e.hasOwnProperty("email")&&t.uint32(26).string(e.email),null!=e.isAnonymous&&e.hasOwnProperty("isAnonymous")&&t.uint32(32).bool(e.isAnonymous),null!=e.traits&&e.hasOwnProperty("traits"))for(var r=Object.keys(e.traits),n=0;n<r.length;++n)t.uint32(42).fork().uint32(10).string(r[n]).uint32(18).string(e.traits[r[n]]).ldelim();return null!=e.isUpdate&&e.hasOwnProperty("isUpdate")&&t.uint32(48).bool(e.isUpdate),t},e.fromObject=function(e){if(e instanceof b.lr.Identify)return e;var t=new b.lr.Identify;if(null!=e.userID&&(t.userID=String(e.userID)),null!=e.name&&(t.name=String(e.name)),null!=e.email&&(t.email=String(e.email)),null!=e.isAnonymous&&(t.isAnonymous=Boolean(e.isAnonymous)),e.traits){if("object"!=typeof e.traits)throw TypeError(".lr.Identify.traits: object expected");t.traits={};for(var r=Object.keys(e.traits),n=0;n<r.length;++n)t.traits[r[n]]=String(e.traits[r[n]])}return null!=e.isUpdate&&(t.isUpdate=Boolean(e.isUpdate)),t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.objects||t.defaults)&&(n.traits={}),t.defaults&&(n.userID="",n.name="",n.email="",n.isAnonymous=!1,n.isUpdate=!1),null!=e.userID&&e.hasOwnProperty("userID")&&(n.userID=e.userID),null!=e.name&&e.hasOwnProperty("name")&&(n.name=e.name),null!=e.email&&e.hasOwnProperty("email")&&(n.email=e.email),null!=e.isAnonymous&&e.hasOwnProperty("isAnonymous")&&(n.isAnonymous=e.isAnonymous),e.traits&&(r=Object.keys(e.traits)).length){n.traits={};for(var o=0;o<r.length;++o)n.traits[r[o]]=e.traits[r[o]]}return null!=e.isUpdate&&e.hasOwnProperty("isUpdate")&&(n.isUpdate=e.isUpdate),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.Activity=function(){function e(e){if(this.transactions=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.transactions=O.emptyArray,e.prototype.pageURL="",e.prototype.count=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.transactions&&e.transactions.length)for(var r=0;r<e.transactions.length;++r)b.lr.Activity.Transaction.encode(e.transactions[r],t.uint32(10).fork()).ldelim();return null!=e.pageURL&&e.hasOwnProperty("pageURL")&&t.uint32(18).string(e.pageURL),null!=e.count&&e.hasOwnProperty("count")&&t.uint32(24).uint32(e.count),t},e.fromObject=function(e){if(e instanceof b.lr.Activity)return e;var t=new b.lr.Activity;if(e.transactions){if(!Array.isArray(e.transactions))throw TypeError(".lr.Activity.transactions: array expected");t.transactions=[];for(var r=0;r<e.transactions.length;++r){if("object"!=typeof e.transactions[r])throw TypeError(".lr.Activity.transactions: object expected");t.transactions[r]=b.lr.Activity.Transaction.fromObject(e.transactions[r])}}return null!=e.pageURL&&(t.pageURL=String(e.pageURL)),null!=e.count&&(t.count=e.count>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.transactions=[]),t.defaults&&(r.pageURL="",r.count=0),e.transactions&&e.transactions.length){r.transactions=[];for(var n=0;n<e.transactions.length;++n)r.transactions[n]=b.lr.Activity.Transaction.toObject(e.transactions[n],t)}return null!=e.pageURL&&e.hasOwnProperty("pageURL")&&(r.pageURL=e.pageURL),null!=e.count&&e.hasOwnProperty("count")&&(r.count=e.count),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Transaction=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.id="",e.prototype.uuid=0,e.prototype.activeTime=0,e.prototype.rawTime=0,e.prototype.didSucceed=!1,e.encode=function(e,t){return t||(t=g.create()),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(10).string(e.id),null!=e.uuid&&e.hasOwnProperty("uuid")&&t.uint32(16).uint32(e.uuid),null!=e.activeTime&&e.hasOwnProperty("activeTime")&&t.uint32(29).float(e.activeTime),null!=e.rawTime&&e.hasOwnProperty("rawTime")&&t.uint32(37).float(e.rawTime),null!=e.didSucceed&&e.hasOwnProperty("didSucceed")&&t.uint32(40).bool(e.didSucceed),t},e.fromObject=function(e){if(e instanceof b.lr.Activity.Transaction)return e;var t=new b.lr.Activity.Transaction;return null!=e.id&&(t.id=String(e.id)),null!=e.uuid&&(t.uuid=e.uuid>>>0),null!=e.activeTime&&(t.activeTime=Number(e.activeTime)),null!=e.rawTime&&(t.rawTime=Number(e.rawTime)),null!=e.didSucceed&&(t.didSucceed=Boolean(e.didSucceed)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.id="",r.uuid=0,r.activeTime=0,r.rawTime=0,r.didSucceed=!1),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),null!=e.uuid&&e.hasOwnProperty("uuid")&&(r.uuid=e.uuid),null!=e.activeTime&&e.hasOwnProperty("activeTime")&&(r.activeTime=t.json&&!isFinite(e.activeTime)?String(e.activeTime):e.activeTime),null!=e.rawTime&&e.hasOwnProperty("rawTime")&&(r.rawTime=t.json&&!isFinite(e.rawTime)?String(e.rawTime):e.rawTime),null!=e.didSucceed&&e.hasOwnProperty("didSucceed")&&(r.didSucceed=e.didSucceed),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),y.ExcludedUser=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.alternateID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.alternateID&&e.hasOwnProperty("alternateID")&&t.uint32(10).string(e.alternateID),t},e.fromObject=function(e){if(e instanceof b.lr.ExcludedUser)return e;var t=new b.lr.ExcludedUser;return null!=e.alternateID&&(t.alternateID=String(e.alternateID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.alternateID=""),null!=e.alternateID&&e.hasOwnProperty("alternateID")&&(r.alternateID=e.alternateID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.Buffer=function(){function e(e){if(this.hrefActivityCounts={},this.scrollableNodes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.eventCount=0,e.prototype.hrefActivityCounts=O.emptyObject,e.prototype.userID="",e.prototype.scrollableNodes=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.eventCount&&e.hasOwnProperty("eventCount")&&t.uint32(8).uint32(e.eventCount),null!=e.hrefActivityCounts&&e.hasOwnProperty("hrefActivityCounts"))for(var r=Object.keys(e.hrefActivityCounts),n=0;n<r.length;++n)t.uint32(18).fork().uint32(10).string(r[n]).uint32(16).uint32(e.hrefActivityCounts[r[n]]).ldelim();if(null!=e.userID&&e.hasOwnProperty("userID")&&t.uint32(26).string(e.userID),null!=e.scrollableNodes&&e.scrollableNodes.length)for(n=0;n<e.scrollableNodes.length;++n)b.lr.Buffer.ScrollableNode.encode(e.scrollableNodes[n],t.uint32(42).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.Buffer)return e;var t=new b.lr.Buffer;if(null!=e.eventCount&&(t.eventCount=e.eventCount>>>0),e.hrefActivityCounts){if("object"!=typeof e.hrefActivityCounts)throw TypeError(".lr.Buffer.hrefActivityCounts: object expected");t.hrefActivityCounts={};for(var r=Object.keys(e.hrefActivityCounts),n=0;n<r.length;++n)t.hrefActivityCounts[r[n]]=e.hrefActivityCounts[r[n]]>>>0}if(null!=e.userID&&(t.userID=String(e.userID)),e.scrollableNodes){if(!Array.isArray(e.scrollableNodes))throw TypeError(".lr.Buffer.scrollableNodes: array expected");for(t.scrollableNodes=[],n=0;n<e.scrollableNodes.length;++n){if("object"!=typeof e.scrollableNodes[n])throw TypeError(".lr.Buffer.scrollableNodes: object expected");t.scrollableNodes[n]=b.lr.Buffer.ScrollableNode.fromObject(e.scrollableNodes[n])}}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.scrollableNodes=[]),(t.objects||t.defaults)&&(n.hrefActivityCounts={}),t.defaults&&(n.eventCount=0,n.userID=""),null!=e.eventCount&&e.hasOwnProperty("eventCount")&&(n.eventCount=e.eventCount),e.hrefActivityCounts&&(r=Object.keys(e.hrefActivityCounts)).length){n.hrefActivityCounts={};for(var o=0;o<r.length;++o)n.hrefActivityCounts[r[o]]=e.hrefActivityCounts[r[o]]}if(null!=e.userID&&e.hasOwnProperty("userID")&&(n.userID=e.userID),e.scrollableNodes&&e.scrollableNodes.length)for(n.scrollableNodes=[],o=0;o<e.scrollableNodes.length;++o)n.scrollableNodes[o]=b.lr.Buffer.ScrollableNode.toObject(e.scrollableNodes[o],t);return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.Selector=function(){function e(e){if(this.classList=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.nodeName="",e.prototype.id="",e.prototype.classList=O.emptyArray,e.prototype.nthChild=0,e.encode=function(e,t){if(t||(t=g.create()),null!=e.nodeName&&e.hasOwnProperty("nodeName")&&t.uint32(10).string(e.nodeName),null!=e.id&&e.hasOwnProperty("id")&&t.uint32(18).string(e.id),null!=e.classList&&e.classList.length)for(var r=0;r<e.classList.length;++r)t.uint32(26).string(e.classList[r]);return null!=e.nthChild&&e.hasOwnProperty("nthChild")&&t.uint32(32).int32(e.nthChild),t},e.fromObject=function(e){if(e instanceof b.lr.Buffer.Selector)return e;var t=new b.lr.Buffer.Selector;if(null!=e.nodeName&&(t.nodeName=String(e.nodeName)),null!=e.id&&(t.id=String(e.id)),e.classList){if(!Array.isArray(e.classList))throw TypeError(".lr.Buffer.Selector.classList: array expected");t.classList=[];for(var r=0;r<e.classList.length;++r)t.classList[r]=String(e.classList[r])}return null!=e.nthChild&&(t.nthChild=0|e.nthChild),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.classList=[]),t.defaults&&(r.nodeName="",r.id="",r.nthChild=0),null!=e.nodeName&&e.hasOwnProperty("nodeName")&&(r.nodeName=e.nodeName),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),e.classList&&e.classList.length){r.classList=[];for(var n=0;n<e.classList.length;++n)r.classList[n]=e.classList[n]}return null!=e.nthChild&&e.hasOwnProperty("nthChild")&&(r.nthChild=e.nthChild),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e.ScrollableNode=function(){function e(e){if(this.nodePath=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.href="",e.prototype.maxScroll=0,e.prototype.nodePath=O.emptyArray,e.encode=function(e,t){if(t||(t=g.create()),null!=e.href&&e.hasOwnProperty("href")&&t.uint32(10).string(e.href),null!=e.maxScroll&&e.hasOwnProperty("maxScroll")&&t.uint32(21).float(e.maxScroll),null!=e.nodePath&&e.nodePath.length)for(var r=0;r<e.nodePath.length;++r)b.lr.Buffer.Selector.encode(e.nodePath[r],t.uint32(26).fork()).ldelim();return t},e.fromObject=function(e){if(e instanceof b.lr.Buffer.ScrollableNode)return e;var t=new b.lr.Buffer.ScrollableNode;if(null!=e.href&&(t.href=String(e.href)),null!=e.maxScroll&&(t.maxScroll=Number(e.maxScroll)),e.nodePath){if(!Array.isArray(e.nodePath))throw TypeError(".lr.Buffer.ScrollableNode.nodePath: array expected");t.nodePath=[];for(var r=0;r<e.nodePath.length;++r){if("object"!=typeof e.nodePath[r])throw TypeError(".lr.Buffer.ScrollableNode.nodePath: object expected");t.nodePath[r]=b.lr.Buffer.Selector.fromObject(e.nodePath[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.nodePath=[]),t.defaults&&(r.href="",r.maxScroll=0),null!=e.href&&e.hasOwnProperty("href")&&(r.href=e.href),null!=e.maxScroll&&e.hasOwnProperty("maxScroll")&&(r.maxScroll=t.json&&!isFinite(e.maxScroll)?String(e.maxScroll):e.maxScroll),e.nodePath&&e.nodePath.length){r.nodePath=[];for(var n=0;n<e.nodePath.length;++n)r.nodePath[n]=b.lr.Buffer.Selector.toObject(e.nodePath[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),e}(),y.DebugLog=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.message="",e.encode=function(e,t){return t||(t=g.create()),null!=e.message&&e.hasOwnProperty("message")&&t.uint32(10).string(e.message),t},e.fromObject=function(e){if(e instanceof b.lr.DebugLog)return e;var t=new b.lr.DebugLog;return null!=e.message&&(t.message=String(e.message)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.message=""),null!=e.message&&e.hasOwnProperty("message")&&(r.message=e.message),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.PreviousSession=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.recordingID="",e.prototype.sessionID=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.recordingID&&e.hasOwnProperty("recordingID")&&t.uint32(10).string(e.recordingID),null!=e.sessionID&&e.hasOwnProperty("sessionID")&&t.uint32(16).uint32(e.sessionID),t},e.fromObject=function(e){if(e instanceof b.lr.PreviousSession)return e;var t=new b.lr.PreviousSession;return null!=e.recordingID&&(t.recordingID=String(e.recordingID)),null!=e.sessionID&&(t.sessionID=e.sessionID>>>0),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.recordingID="",r.sessionID=0),null!=e.recordingID&&e.hasOwnProperty("recordingID")&&(r.recordingID=e.recordingID),null!=e.sessionID&&e.hasOwnProperty("sessionID")&&(r.sessionID=e.sessionID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.ConditionalRecordingConfirmation=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.recordingConditionID="",e.encode=function(e,t){return t||(t=g.create()),null!=e.recordingConditionID&&e.hasOwnProperty("recordingConditionID")&&t.uint32(10).string(e.recordingConditionID),t},e.fromObject=function(e){if(e instanceof b.lr.ConditionalRecordingConfirmation)return e;var t=new b.lr.ConditionalRecordingConfirmation;return null!=e.recordingConditionID&&(t.recordingConditionID=String(e.recordingConditionID)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.recordingConditionID=""),null!=e.recordingConditionID&&e.hasOwnProperty("recordingConditionID")&&(r.recordingConditionID=e.recordingConditionID),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e}(),y.AppFramework=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.type=0,e.encode=function(e,t){return t||(t=g.create()),null!=e.type&&e.hasOwnProperty("type")&&t.uint32(8).int32(e.type),t},e.fromObject=function(e){if(e instanceof b.lr.AppFramework)return e;var t=new b.lr.AppFramework;switch(e.type){case"NATIVE_MOBILE":case 0:t.type=0;break;case"REACT_NATIVE":case 1:t.type=1;break;case"JETPACK_COMPOSE":case 2:t.type=2;break;case"SWIFT_UI":case 3:t.type=3;break;case"SHOPIFY":case 4:t.type=4;break;case"FLUTTER":case 5:t.type=5;break;case"REACT_NATIVE_NEW_ARCH":case 6:t.type=6}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.type=t.enums===String?"NATIVE_MOBILE":0),null!=e.type&&e.hasOwnProperty("type")&&(r.type=t.enums===String?b.lr.AppFramework.FrameworkType[e.type]:e.type),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,m.util.toJSONOptions)},e.FrameworkType=function(){var e={},t=Object.create(e);return t[e[0]="NATIVE_MOBILE"]=0,t[e[1]="REACT_NATIVE"]=1,t[e[2]="JETPACK_COMPOSE"]=2,t[e[3]="SWIFT_UI"]=3,t[e[4]="SHOPIFY"]=4,t[e[5]="FLUTTER"]=5,t[e[6]="REACT_NATIVE_NEW_ARCH"]=6,t}(),e}(),y),e.exports=b},5244:function(e){"use strict";e.exports=function(e,t){var r=new Array(arguments.length-1),n=0,o=2,i=!0;for(;o<arguments.length;)r[n++]=arguments[o++];return new Promise((function(o,a){r[n]=function(e){if(i)if(i=!1,e)a(e);else{for(var t=new Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];o.apply(null,t)}};try{e.apply(t||null,r)}catch(e){i&&(i=!1,a(e))}}))}},2853:function(e,t){"use strict";var r=t;r.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};for(var n=new Array(64),o=new Array(123),i=0;i<64;)o[n[i]=i<26?i+65:i<52?i+71:i<62?i-4:i-59|43]=i++;r.encode=function(e,t,r){for(var o,i=null,a=[],s=0,u=0;t<r;){var l=e[t++];switch(u){case 0:a[s++]=n[l>>2],o=(3&l)<<4,u=1;break;case 1:a[s++]=n[o|l>>4],o=(15&l)<<2,u=2;break;case 2:a[s++]=n[o|l>>6],a[s++]=n[63&l],u=0}s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,a)),s=0)}return u&&(a[s++]=n[o],a[s++]=61,1===u&&(a[s++]=61)),i?(s&&i.push(String.fromCharCode.apply(String,a.slice(0,s))),i.join("")):String.fromCharCode.apply(String,a.slice(0,s))};var a="invalid encoding";r.decode=function(e,t,r){for(var n,i=r,s=0,u=0;u<e.length;){var l=e.charCodeAt(u++);if(61===l&&s>1)break;if(void 0===(l=o[l]))throw Error(a);switch(s){case 0:n=l,s=1;break;case 1:t[r++]=n<<2|(48&l)>>4,n=l,s=2;break;case 2:t[r++]=(15&n)<<4|(60&l)>>2,n=l,s=3;break;case 3:t[r++]=(3&n)<<6|l,s=0}}if(1===s)throw Error(a);return r-i},r.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}},8526:function(e){"use strict";function t(){this._listeners={}}e.exports=t,t.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},t.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],n=0;n<r.length;)r[n].fn===t?r.splice(n,1):++n;return this},t.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<t.length;)t[n].fn.apply(t[n++].ctx,r)}return this}},8641:function(e){"use strict";function t(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),n=128===r[3];function o(e,n,o){t[0]=e,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3]}function i(e,n,o){t[0]=e,n[o]=r[3],n[o+1]=r[2],n[o+2]=r[1],n[o+3]=r[0]}function a(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],t[0]}function s(e,n){return r[3]=e[n],r[2]=e[n+1],r[1]=e[n+2],r[0]=e[n+3],t[0]}e.writeFloatLE=n?o:i,e.writeFloatBE=n?i:o,e.readFloatLE=n?a:s,e.readFloatBE=n?s:a}():function(){function t(e,t,r,n){var o=t<0?1:0;if(o&&(t=-t),0===t)e(1/t>0?0:2147483648,r,n);else if(isNaN(t))e(2143289344,r,n);else if(t>34028234663852886e22)e((o<<31|2139095040)>>>0,r,n);else if(t<11754943508222875e-54)e((o<<31|Math.round(t/1401298464324817e-60))>>>0,r,n);else{var i=Math.floor(Math.log(t)/Math.LN2);e((o<<31|i+127<<23|8388607&Math.round(t*Math.pow(2,-i)*8388608))>>>0,r,n)}}function a(e,t,r){var n=e(t,r),o=2*(n>>31)+1,i=n>>>23&255,a=8388607&n;return 255===i?a?NaN:o*(1/0):0===i?1401298464324817e-60*o*a:o*Math.pow(2,i-150)*(a+8388608)}e.writeFloatLE=t.bind(null,r),e.writeFloatBE=t.bind(null,n),e.readFloatLE=a.bind(null,o),e.readFloatBE=a.bind(null,i)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),n=128===r[7];function o(e,n,o){t[0]=e,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3],n[o+4]=r[4],n[o+5]=r[5],n[o+6]=r[6],n[o+7]=r[7]}function i(e,n,o){t[0]=e,n[o]=r[7],n[o+1]=r[6],n[o+2]=r[5],n[o+3]=r[4],n[o+4]=r[3],n[o+5]=r[2],n[o+6]=r[1],n[o+7]=r[0]}function a(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],r[4]=e[n+4],r[5]=e[n+5],r[6]=e[n+6],r[7]=e[n+7],t[0]}function s(e,n){return r[7]=e[n],r[6]=e[n+1],r[5]=e[n+2],r[4]=e[n+3],r[3]=e[n+4],r[2]=e[n+5],r[1]=e[n+6],r[0]=e[n+7],t[0]}e.writeDoubleLE=n?o:i,e.writeDoubleBE=n?i:o,e.readDoubleLE=n?a:s,e.readDoubleBE=n?s:a}():function(){function t(e,t,r,n,o,i){var a=n<0?1:0;if(a&&(n=-n),0===n)e(0,o,i+t),e(1/n>0?0:2147483648,o,i+r);else if(isNaN(n))e(0,o,i+t),e(2146959360,o,i+r);else if(n>17976931348623157e292)e(0,o,i+t),e((a<<31|2146435072)>>>0,o,i+r);else{var s;if(n<22250738585072014e-324)e((s=n/5e-324)>>>0,o,i+t),e((a<<31|s/4294967296)>>>0,o,i+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),e(4503599627370496*(s=n*Math.pow(2,-u))>>>0,o,i+t),e((a<<31|u+1023<<20|1048576*s&1048575)>>>0,o,i+r)}}}function a(e,t,r,n,o){var i=e(n,o+t),a=e(n,o+r),s=2*(a>>31)+1,u=a>>>20&2047,l=4294967296*(1048575&a)+i;return 2047===u?l?NaN:s*(1/0):0===u?5e-324*s*l:s*Math.pow(2,u-1075)*(l+4503599627370496)}e.writeDoubleLE=t.bind(null,r,0,4),e.writeDoubleBE=t.bind(null,n,4,0),e.readDoubleLE=a.bind(null,o,0,4),e.readDoubleBE=a.bind(null,i,4,0)}(),e}function r(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function n(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function o(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function i(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}e.exports=t(t)},6796:function(e){"use strict";e.exports=function(e,t,r){var n=r||8192,o=n>>>1,i=null,a=n;return function(r){if(r<1||r>o)return e(r);a+r>n&&(i=e(n),a=0);var s=t.call(i,a,a+=r);return 7&a&&(a=1+(7|a)),s}}},1709:function(e,t){"use strict";var r=t;r.length=function(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:55296==(64512&r)&&56320==(64512&e.charCodeAt(n+1))?(++n,t+=4):t+=3;return t},r.read=function(e,t,r){if(r-t<1)return"";for(var n,o=null,i=[],a=0;t<r;)(n=e[t++])<128?i[a++]=n:n>191&&n<224?i[a++]=(31&n)<<6|63&e[t++]:n>239&&n<365?(n=((7&n)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,i[a++]=55296+(n>>10),i[a++]=56320+(1023&n)):i[a++]=(15&n)<<12|(63&e[t++])<<6|63&e[t++],a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),a=0);return o?(a&&o.push(String.fromCharCode.apply(String,i.slice(0,a))),o.join("")):String.fromCharCode.apply(String,i.slice(0,a))},r.write=function(e,t,r){for(var n,o,i=r,a=0;a<e.length;++a)(n=e.charCodeAt(a))<128?t[r++]=n:n<2048?(t[r++]=n>>6|192,t[r++]=63&n|128):55296==(64512&n)&&56320==(64512&(o=e.charCodeAt(a+1)))?(n=65536+((1023&n)<<10)+(1023&o),++a,t[r++]=n>>18|240,t[r++]=n>>12&63|128,t[r++]=n>>6&63|128,t[r++]=63&n|128):(t[r++]=n>>12|224,t[r++]=n>>6&63|128,t[r++]=63&n|128);return r-i}},6558:function(e,t,r){"use strict";e.exports=r(5836)},5836:function(e,t,r){"use strict";var n=t;function o(){n.Reader._configure(n.BufferReader),n.util._configure()}n.build="minimal",n.Writer=r(4858),n.BufferWriter=r(725),n.Reader=r(4089),n.BufferReader=r(9653),n.util=r(8553),n.rpc=r(5643),n.roots=r(4057),n.configure=o,n.Writer._configure(n.BufferWriter),o()},4089:function(e,t,r){"use strict";e.exports=u;var n,o=r(8553),i=o.LongBits,a=o.utf8;function s(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function u(e){this.buf=e,this.pos=0,this.len=e.length}var l,c="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new u(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new u(e);throw Error("illegal buffer")};function f(){var e=new i(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw s(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw s(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function p(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function d(){if(this.pos+8>this.len)throw s(this,8);return new i(p(this.buf,this.pos+=4),p(this.buf,this.pos+=4))}u.create=o.Buffer?function(e){return(u.create=function(e){return o.Buffer.isBuffer(e)?new n(e):c(e)})(e)}:c,u.prototype._slice=o.Array.prototype.subarray||o.Array.prototype.slice,u.prototype.uint32=(l=4294967295,function(){if(l=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return l;if((this.pos+=5)>this.len)throw this.pos=this.len,s(this,10);return l}),u.prototype.int32=function(){return 0|this.uint32()},u.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)},u.prototype.bool=function(){return 0!==this.uint32()},u.prototype.fixed32=function(){if(this.pos+4>this.len)throw s(this,4);return p(this.buf,this.pos+=4)},u.prototype.sfixed32=function(){if(this.pos+4>this.len)throw s(this,4);return 0|p(this.buf,this.pos+=4)},u.prototype.float=function(){if(this.pos+4>this.len)throw s(this,4);var e=o.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},u.prototype.double=function(){if(this.pos+8>this.len)throw s(this,4);var e=o.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},u.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw s(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,r):t===r?new this.buf.constructor(0):this._slice.call(this.buf,t,r)},u.prototype.string=function(){var e=this.bytes();return a.read(e,0,e.length)},u.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw s(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw s(this)}while(128&this.buf[this.pos++]);return this},u.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;;){if(4==(e=7&this.uint32()))break;this.skipType(e)}break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},u._configure=function(e){n=e;var t=o.Long?"toLong":"toNumber";o.merge(u.prototype,{int64:function(){return f.call(this)[t](!1)},uint64:function(){return f.call(this)[t](!0)},sint64:function(){return f.call(this).zzDecode()[t](!1)},fixed64:function(){return d.call(this)[t](!0)},sfixed64:function(){return d.call(this)[t](!1)}})}},9653:function(e,t,r){"use strict";e.exports=i;var n=r(4089);(i.prototype=Object.create(n.prototype)).constructor=i;var o=r(8553);function i(e){n.call(this,e)}o.Buffer&&(i.prototype._slice=o.Buffer.prototype.slice),i.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len))}},4057:function(e){"use strict";e.exports={}},5643:function(e,t,r){"use strict";t.Service=r(5571)},5571:function(e,t,r){"use strict";e.exports=o;var n=r(8553);function o(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");n.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(o.prototype=Object.create(n.EventEmitter.prototype)).constructor=o,o.prototype.rpcCall=function e(t,r,o,i,a){if(!i)throw TypeError("request must be specified");var s=this;if(!a)return n.asPromise(e,s,t,r,o,i);if(s.rpcImpl)try{return s.rpcImpl(t,r[s.requestDelimited?"encodeDelimited":"encode"](i).finish(),(function(e,r){if(e)return s.emit("error",e,t),a(e);if(null!==r){if(!(r instanceof o))try{r=o[s.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return s.emit("error",e,t),a(e)}return s.emit("data",r,t),a(null,r)}s.end(!0)}))}catch(e){return s.emit("error",e,t),void setTimeout((function(){a(e)}),0)}else setTimeout((function(){a(Error("already ended"))}),0)},o.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},1858:function(e,t,r){"use strict";e.exports=o;var n=r(8553);function o(e,t){this.lo=e>>>0,this.hi=t>>>0}var i=o.zero=new o(0,0);i.toNumber=function(){return 0},i.zzEncode=i.zzDecode=function(){return this},i.length=function(){return 1};var a=o.zeroHash="\0\0\0\0\0\0\0\0";o.fromNumber=function(e){if(0===e)return i;var t=e<0;t&&(e=-e);var r=e>>>0,n=(e-r)/4294967296>>>0;return t&&(n=~n>>>0,r=~r>>>0,++r>4294967295&&(r=0,++n>4294967295&&(n=0))),new o(r,n)},o.from=function(e){if("number"==typeof e)return o.fromNumber(e);if(n.isString(e)){if(!n.Long)return o.fromNumber(parseInt(e,10));e=n.Long.fromString(e)}return e.low||e.high?new o(e.low>>>0,e.high>>>0):i},o.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+4294967296*r)}return this.lo+4294967296*this.hi},o.prototype.toLong=function(e){return n.Long?new n.Long(0|this.lo,0|this.hi,Boolean(e)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(e)}};var s=String.prototype.charCodeAt;o.fromHash=function(e){return e===a?i:new o((s.call(e,0)|s.call(e,1)<<8|s.call(e,2)<<16|s.call(e,3)<<24)>>>0,(s.call(e,4)|s.call(e,5)<<8|s.call(e,6)<<16|s.call(e,7)<<24)>>>0)},o.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},o.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},o.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},o.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10}},8553:function(e,t,r){"use strict";var n=t;function o(e,t,r){for(var n=Object.keys(t),o=0;o<n.length;++o)void 0!==e[n[o]]&&r||(e[n[o]]=t[n[o]]);return e}function i(e){function t(e,r){if(!(this instanceof t))return new t(e,r);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),r&&o(this,r)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t}n.asPromise=r(5244),n.base64=r(2853),n.EventEmitter=r(8526),n.float=r(8641),n.inquire=r(2360),n.utf8=r(1709),n.pool=r(6796),n.LongBits=r(1858),n.emptyArray=Object.freeze?Object.freeze([]):[],n.emptyObject=Object.freeze?Object.freeze({}):{},n.isNode=Boolean(r.g.process&&r.g.process.versions&&r.g.process.versions.node),n.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},n.isString=function(e){return"string"==typeof e||e instanceof String},n.isObject=function(e){return e&&"object"==typeof e},n.isset=n.isSet=function(e,t){var r=e[t];return!(null==r||!e.hasOwnProperty(t))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},n.Buffer=function(){try{var e=n.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),n._Buffer_from=null,n._Buffer_allocUnsafe=null,n.newBuffer=function(e){return"number"==typeof e?n.Buffer?n._Buffer_allocUnsafe(e):new n.Array(e):n.Buffer?n._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},n.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,n.Long=r.g.dcodeIO&&r.g.dcodeIO.Long||n.inquire("long"),n.key2Re=/^true|false|0|1$/,n.key32Re=/^-?(?:0|[1-9][0-9]*)$/,n.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,n.longToHash=function(e){return e?n.LongBits.from(e).toHash():n.LongBits.zeroHash},n.longFromHash=function(e,t){var r=n.LongBits.fromHash(e);return n.Long?n.Long.fromBits(r.lo,r.hi,t):r.toNumber(Boolean(t))},n.merge=o,n.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},n.newError=i,n.ProtocolError=i("ProtocolError"),n.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},n.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},n.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},n._configure=function(){var e=n.Buffer;e?(n._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},n._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):n._Buffer_from=n._Buffer_allocUnsafe=null}},4858:function(e,t,r){"use strict";e.exports=f;var n,o=r(8553),i=o.LongBits,a=o.base64,s=o.utf8;function u(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function l(){}function c(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function f(){this.len=0,this.head=new u(l,0,0),this.tail=this.head,this.states=null}function p(e,t,r){t[r]=255&e}function d(e,t){this.len=e,this.next=void 0,this.val=t}function h(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function y(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}f.create=o.Buffer?function(){return(f.create=function(){return new n})()}:function(){return new f},f.alloc=function(e){return new o.Array(e)},o.Array!==Array&&(f.alloc=o.pool(f.alloc,o.Array.prototype.subarray)),f.prototype._push=function(e,t,r){return this.tail=this.tail.next=new u(e,t,r),this.len+=t,this},d.prototype=Object.create(u.prototype),d.prototype.fn=function(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e},f.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new d((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},f.prototype.int32=function(e){return e<0?this._push(h,10,i.fromNumber(e)):this.uint32(e)},f.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},f.prototype.uint64=function(e){var t=i.from(e);return this._push(h,t.length(),t)},f.prototype.int64=f.prototype.uint64,f.prototype.sint64=function(e){var t=i.from(e).zzEncode();return this._push(h,t.length(),t)},f.prototype.bool=function(e){return this._push(p,1,e?1:0)},f.prototype.fixed32=function(e){return this._push(y,4,e>>>0)},f.prototype.sfixed32=f.prototype.fixed32,f.prototype.fixed64=function(e){var t=i.from(e);return this._push(y,4,t.lo)._push(y,4,t.hi)},f.prototype.sfixed64=f.prototype.fixed64,f.prototype.float=function(e){return this._push(o.float.writeFloatLE,4,e)},f.prototype.double=function(e){return this._push(o.float.writeDoubleLE,8,e)};var m=o.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var n=0;n<e.length;++n)t[r+n]=e[n]};f.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(p,1,0);if(o.isString(e)){var r=f.alloc(t=a.length(e));a.decode(e,r,0),e=r}return this.uint32(t)._push(m,t,e)},f.prototype.string=function(e){var t=s.length(e);return t?this.uint32(t)._push(s.write,t,e):this._push(p,1,0)},f.prototype.fork=function(){return this.states=new c(this),this.head=this.tail=new u(l,0,0),this.len=0,this},f.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new u(l,0,0),this.len=0),this},f.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},f.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},f._configure=function(e){n=e}},725:function(e,t,r){"use strict";e.exports=a;var n=r(4858);(a.prototype=Object.create(n.prototype)).constructor=a;var o=r(8553),i=o.Buffer;function a(){n.call(this)}a.alloc=function(e){return(a.alloc=o._Buffer_allocUnsafe)(e)};var s=i&&i.prototype instanceof Uint8Array&&"set"===i.prototype.set.name?function(e,t,r){t.set(e,r)}:function(e,t,r){if(e.copy)e.copy(t,r,0,e.length);else for(var n=0;n<e.length;)t[r++]=e[n++]};function u(e,t,r){e.length<40?o.utf8.write(e,t,r):t.utf8Write(e,r)}a.prototype.bytes=function(e){o.isString(e)&&(e=o._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(s,t,e),this},a.prototype.string=function(e){var t=i.byteLength(e);return this.uint32(t),t&&this._push(u,t,e),this}},3897:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},5372:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},3405:function(e,t,r){var n=r(3897);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},6690:function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},9728:function(e,t,r){var n=r(4062);function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},8416:function(e,t,r){var n=r(4062);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},4836:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},9498:function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},8872:function(e){e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],u=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}},e.exports.__esModule=!0,e.exports.default=e.exports},2218:function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},2281:function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},7061:function(e,t,r){var n=r(8698).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=o=function(){return t},e.exports.__esModule=!0,e.exports.default=e.exports;var t={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",c=s.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),s=new N(n||[]);return a(i,"_invoke",{value:w(e,r,s)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h={};function y(){}function m(){}function g(){}var O={};f(O,u,(function(){return this}));var b=Object.getPrototypeOf,v=b&&b(b(D([])));v&&v!==r&&i.call(v,u)&&(O=v);var E=g.prototype=y.prototype=Object.create(O);function T(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(o,a,s,u){var l=d(e[o],e,a);if("throw"!==l.type){var c=l.arg,f=c.value;return f&&"object"==n(f)&&i.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,s,u)}),(function(e){r("throw",e,s,u)})):t.resolve(f).then((function(e){c.value=e,s(c)}),(function(e){return r("throw",e,s,u)}))}u(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(i,i):i()}})}function w(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return A()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=S(a,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function S(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=d(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,h;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function D(e){if(e){var t=e[u];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(i.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:A}}function A(){return{value:void 0,done:!0}}return m.prototype=g,a(E,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:m,configurable:!0}),m.displayName=f(g,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,f(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},T(I.prototype),f(I.prototype,l,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new I(p(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},T(E),f(E,c,"Generator"),f(E,u,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),u=i.call(o,"finallyLoc");if(s&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),_(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:D(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},7424:function(e,t,r){var n=r(5372),o=r(8872),i=r(6116),a=r(2218);e.exports=function(e,t){return n(e)||o(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},861:function(e,t,r){var n=r(3405),o=r(9498),i=r(6116),a=r(2281);e.exports=function(e){return n(e)||o(e)||i(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},5036:function(e,t,r){var n=r(8698).default;e.exports=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},4062:function(e,t,r){var n=r(8698).default,o=r(5036);e.exports=function(e){var t=o(e,"string");return"symbol"===n(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},8698:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},6116:function(e,t,r){var n=r(3897);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},4687:function(e,t,r){var n=r(7061)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.loaded=!0,i.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},function(){"use strict";var e=r(4836),t=r(4523),n=e(r(7006));r(5230);new t.MessageReceiver({worker:self,onReceiveFullMessage:n.default})}()}();