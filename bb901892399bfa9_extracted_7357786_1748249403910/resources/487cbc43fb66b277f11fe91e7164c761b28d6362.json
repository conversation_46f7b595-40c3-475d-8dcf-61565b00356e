{"filters.status": "Status", "timestampLabel": "Last updated: Today, {{updateTime}}", "tableHeaders.priority.title": "Priority", "sidePanels.filters.created.label": "Created", "modals.duplicateCampaign.infoArea": "{{originalCampaignName}}'s lists will not be added to the new campaign. To run the campaign you must add lists in the Lists tab.", "filters.emptyTitle": "No campaigns found", "modals.startConfirmation.messageLine": "The campaign will start calling records and the reporting information will be available in the campaign details", "modals.duplicateCampaign.title": "Duplicate campaign", "delete": "Delete", "tableHeaders.createdAt.hint": "All times are displayed in the {{timezone}} timezone", "sidePanels.filters.title": "Filters", "modals.createCampaign.teamTooltip": "Users can only see campaigns from their own team", "sidePanels.filters.priority.label": "Priority", "modals.startConfirmation.info": "The dialing mode cannot be changed after the campaign starts.", "sidePanels.filters.status.placeholder": "Select status", "sidePanels.filters.status.label": "Status", "sidePanels.filters.dialingMode.placeholder": "Select dialing mode", "sidePanels.filters.dialingMode.option.preview": "Preview dialing", "sidePanels.filters.dialingMode.option.predictive": "Predictive dialing", "sidePanels.filters.dialingMode.label": "Dialing Mode", "sidePanels.filters.actionRequired.label": "Show only campaigns with warning flags", "tableHeaders.name": "Name", "duplicate": "Duplicate", "toolbar.filters.availableFilters.dialingMode": "Dialing Mode", "toolbar.filters.availableFilters.status": "Status", "toolbar.filters.availableFilters.extra": "Extra", "toolbar.filters.clearButton": "Clear All", "tableHeaders.dialingMode": "Dialing mode", "tableHeaders.status": "Status", "modals.createCampaign.teamPermissionTooltip": "Users can only see the campaigns within their permissions", "search": "Search by campaign name", "searchTooltip": "Type at least {{minLength}} characters", "toolbar.filters.title": "Filters", "headerLabel": "Campaigns", "count": "{{count}} campaign", "modals.duplicateCampaign.warningArea": "Setting up could take some seconds. You can close this modal and access the campaign in the campaigns list, once its setup is complete.", "filters.priority": "Priority", "modals.duplicateCampaign.placeholder": "{{originalCampaignName}} Copy", "modals.startConfirmation.title": "Start campaign?", "tableHeaders.priority.hint": "Priority is set between {{min}} (lowest) and {{max}} (highest)", "tableHeaders.createdAt": "Created at", "tableHeaders.team": "Team", "filters.dialingMode": "Dialing Mode", "filters.extra": "Extra", "filters.created": "Created", "count_plural": "{{count}} campaigns", "modals.createCampaign.title": "Create campaign", "toolbar.title": "Filters", "modals.createCampaign.namePlaceholder": "e.g. Black Friday", "filters.emptyMessage": "We couldn’t find any campaigns with the applied filters", "noTeams": "No teams", "newButtonLabel": "Create campaign", "emptyMessage": "Create a campaign to view it in the campaigns list", "emptyTitle": "No campaigns yet", "tableHeaders.createdAt.label": "Created"}