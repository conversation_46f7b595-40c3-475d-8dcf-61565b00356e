{"name": "@perandrestromhaug/playwright-test-runner-and-debugger", "version": "1.0.33", "type": "module", "bin": {"playwright-test-runner-and-debugger": "dist/server.js"}, "scripts": {"build": "tsc", "start": "tsx src/server.ts", "dev": "fastmcp dev -- src/server.ts", "lint": "prettier --check . && eslint . && tsc --noEmit", "test": "vitest run", "format": "prettier --write . && eslint --fix .", "web": "vite", "typecheck": "tsc --noEmit", "playwright:test": "playwright test"}, "dependencies": {"adm-zip": "^0.5.16", "fastmcp": "^1.27.3", "jiti": "^2.4.2", "yargs": "^17.7.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@playwright/test": "^1.52.0", "@tsconfig/node22": "^22.0.1", "@types/adm-zip": "^0.5.7", "@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@types/yargs": "^17.0.33", "@vitejs/plugin-react": "^4.4.1", "eslint-config-prettier": "^10.1.3", "eslint-plugin-perfectionist": "^4.12.3", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "semantic-release": "^24.2.3", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vitest": "^3.1.3"}}