{"name": "playwright-trace-analyzer", "version": "1.0.0", "type": "module", "bin": {"playwright-trace-analyzer": "dist/server.js"}, "scripts": {"build": "tsc", "start": "tsx src/server.ts", "dev": "fastmcp dev -- src/server.ts", "lint": "prettier --check . && eslint . && tsc --noEmit", "test": "vitest run", "format": "prettier --write . && eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"adm-zip": "^0.5.16", "fastmcp": "^1.27.3", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@tsconfig/node22": "^22.0.1", "@types/adm-zip": "^0.5.7", "@types/node": "^22.15.17", "eslint-config-prettier": "^10.1.3", "eslint-plugin-perfectionist": "^4.12.3", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vitest": "^3.1.3"}}