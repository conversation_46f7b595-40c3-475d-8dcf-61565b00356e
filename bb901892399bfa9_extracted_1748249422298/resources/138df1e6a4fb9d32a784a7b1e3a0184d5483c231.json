{"extension_points.ui_blocks.interaction_info.behaviors.reschedule.value": "reschedule", "extension_points.ui_blocks.interaction_info.form.reschedule_call.hint": "Reschedule the call to a new date and time", "extension_points.ui_blocks.panel.targets_list.error_states.flows.title": "Could not load the flows list", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.title": "No favorites found", "extension_points.ui_blocks.common.MONTHS.4": "April", "extension_points.ui_blocks.panel.pagination.previous": "Previous", "extension_points.ui_blocks.panel.targets_list.transfer_target.queues.search_placeholder": "Search for queues...", "extension_points.notifications.unrecoverable_errors.prepare_agent_call.title": "Couldn't start the internal call", "extension_points.ui_blocks.dialog.nailed_up_mismatch.confirm_text": "Accept and close", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.personas": "Search by name or number...", "extension_points.notifications.received.consultation.on_agent_to_agent.title": "Cannot start a transfer or a consultation while an internal call is in progress", "extension_points.notifications.drop_voicemail.success": "Voicemail message dropped", "extension_points.ui_blocks.interaction_info.behaviors.finalize.text": "Finalize", "extension_points.notifications.actions.consultation.failure_no_agents.title": "No colleagues available in the queue", "extension_points.ui_blocks.common.WeekDays.TUE": "<PERSON><PERSON>", "extension_points.ui_blocks.common.MONTHS.11": "November", "extension_points.notifications.click_to_sms_error.message": "Try again. If the problem persists, contact support.", "extension_points.ui_blocks.interaction_actions.no_actions.end_call.external_phone": "End the call on your External Phone", "extension_points.ui_blocks.panel.tabs.right_control": "<PERSON>roll tabs forward", "extension_points.notifications.actions.conference.leave_conference_failure.message:": "Please try again", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.favorites.refine_search": "{{count}} more favorites. Use the search to refine further.", "extension_points.notifications.actions.skip_reschedule_faild": "Couldn't reschedule the call", "extension_points.ui_blocks.dialog.leave_conference.choose_transfer_agent.second_subtitle": "Call hosting will be transferred to:", "extension_points.ui_blocks.interaction_info.form.reschedule_call.label": "Reschedule call", "extension_points.ui_blocks.interaction_info.states.outbound_dial_voice_call": "Outbound call", "extension_points.notifications.drop_voicemail.failure": "Couldn't drop voicemail message", "extension_points.ui_blocks.interaction_actions.buttons.skip.confirmText": "Skip {{option}}", "extension_points.ui_blocks.outbound_actions.button_labels.start_agent_voice_conversation": "Call a colleague", "extension_points.ui_blocks.interaction_actions.no_actions.answer_call.sip": "Answer the call on your SIP Device", "extension_points.ui_blocks.dialog.choice.transfer.text": "Transfer the current active call to {{ number }}", "extension_points.ui_blocks.dialog.nailed_up_mismatch.first_detail": "Your status was changed to Offline and your voice handling is now in Conversations.", "extension_points.ui_blocks.common.MONTHS.3": "March", "extension_points.notifications.actions.consultation.merge_call.message": "Please try again", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.title": "No colleagues found", "extension_points.ui_blocks.common.WeekDays.SUN": "Sun", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.agents": "Search for colleagues...", "extension_points.ui_blocks.interaction_actions.no_actions.answer_call.sip_device": "Answer the call on your SIP Device", "extension_points.notifications.actions.wrap_up.disposition_timeout_auto_submitted.message": "The wrap-up was automatically dismissed and changes have been saved", "extension_points.ui_blocks.dialog.leave_conference.first_subtitle": "The call will continue with the other participants.", "extension_points.ui_blocks.dialog.leave_conference.cancel_text": "Back to call", "extension_points.ui_blocks.components.targets_list.transfer_target.time.title": "Time", "extension_points.notifications.click_to_email_error.message": "Try again. If the problem persists, contact support.", "wrap_up": "Wrap-up", "extension_points.notifications.click_to_sms_error.title": "An error occurred when triggering click to sms", "extension_points.ui_blocks.common.time.input.error": "Can't be earlier than the current time", "extension_points.ui_blocks.common.WeekDays.SAT": "Sat", "extension_points.notifications.actions.skip_reschedule_success": "Call was rescheduled successfully", "extension_points.ui_blocks.interaction_info_consultation.buttons.merge_calls.text": "Add to consultation", "extension_points.ui_blocks.common.WeekDays.WED": "Wed", "extension_points.ui_blocks.panel.targets_list.transfer_target.transfer_voicemail": "Transfer to voicemail", "extension_points.notifications.ongoing_interactions_error.title": "Failed to start a new conversation", "extension_points.ui_blocks.dialog.choice.consultation.title": "Consultation", "extension_points.ui_blocks.panel.targets_list.tabs.labels.queues": "Queues", "extension_points.notifications.click_to_email_error.title": "An error occurred when triggering click to email", "extension_points.notifications.actions.skip_requeue_faild": "Couldn't requeue the call", "extension_points.ui_blocks.interaction_info.states.schedule_callback_voice_call": "Scheduled callback", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.onACall": "On a call", "extension_points.ui_blocks.interaction_info.behaviors.finalize.value": "finalize", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.unknown_status": "Unknown status", "extension_points.ui_blocks.interaction_actions.buttons.leave_conference.text": "Leave", "extension_points.ui_blocks.interaction_info.behaviors.reschedule.text": "Reschedule", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_call_popup.td_phone_label": "Talkdesk Phone", "extension_points.ui_blocks.interaction_info.form.behavior.label": "Behavior", "extension_points.notifications.actions.call_swap.accept_and_swap.not_in_progress_failure.message": "The ongoing call was dropped", "extension_points.ui_blocks.dialog.nailed_up_mismatch.cancel_text": "Go to Settings", "extension_points.notifications.actions.skip_requeue_success": "Call was requeued successfully", "extension_points.notifications.actions.skip_finalize_faild": "Couldn't finalize the call", "extension_points.ui_blocks.panel.targets_list.empty_states.no_personas.message": "There are no colleagues configured on your account", "extension_points.notifications.auto_away.message": "Your status has been set to Away due to missed or rejected calls", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.ring_groups": "Search for queues...", "extension_points.ui_blocks.panel.targets_list.transfer_target.buttons.refresh": "Refresh", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.away": "Away", "extension_points.ui_blocks.common.MONTHS.5": "May", "extension_points.ui_blocks.dialog.choice.transfer.title": "Blind transfer", "extension_points.ui_blocks.common.MONTHS.2": "February", "extension_points.ui_blocks.interaction_actions.no_actions.answer_call.external_phone": "Answer the call on your External Phone", "extension_points.notifications.permission.no_microphone_access.message": "Please check system settings.", "extension_points.ui_blocks.dialog.nailed_up_mismatch.title": "Disconnected from external devices", "extension_points.ui_blocks.panel.pagination.next": "Next", "extension_points.ui_blocks.interaction_info.form.reschedule_call.contactTimezone": "Contact time zone", "extension_points.ui_blocks.interaction_actions.buttons.skip.cancelText": "Cancel", "extension_points.notifications.unrecoverable_errors.schedule_callback.message": "Please refresh this page.If this problem persists, please contact your administrator", "extension_points.ui_blocks.dialog.choice.title": "Choose an option", "extension_points.notifications.actions.call_swap.accept_and_swap.not_in_source_interaction_failure": "You cannot accept a call while in a Conference or Consultation.", "extension_points.ui_blocks.interaction_actions.no_actions.transfer.external_phone": "To complete the transfer, please end the call on your External Phone", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.title": "No results found", "extension_points.notifications.received.consultation.on_consultation.title": "Cannot start a transfer or a consultation while a consultation is in progress", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem.onCall": "On a call", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.available": "Available", "extension_points.notifications.unrecoverable_errors.conversation_ivr.title": "Couldn't load the Call screen", "extension_points.ui_blocks.outbound_actions.external_device_toast.title": "Disconnected from External Device", "extension_points.ui_blocks.panel.targets_list.tabs.labels.agents": "Colleagues", "extension_points.ui_blocks.common.MONTHS.10": "October", "extension_points.ui_blocks.common.WeekDays.FRI": "<PERSON><PERSON>", "extension_points.ui_blocks.dialog.cancel_text": "Cancel", "extension_points.notifications.received.consultation.on_hold.message": "Please unhold the current call and try again", "extension_points.ui_blocks.interaction_info.form.reschedule_call.modal.title": "Reschedule callback", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.title": "Could not load the favorites list", "extension_points.notifications.emergency.no_address.title": "Emergency information is missing", "extension_points.notifications.conference.owner_changed.update_guest_state_error.title": "Unable to retrieve participants' status", "extension_points.ui_blocks.interaction_actions.no_actions.end_call.sip": "End the call on your SIP Device", "extension_points.ui_blocks.interaction_info.behaviors.reschedule.desc": "Reschedules the call to a new date and time", "extension_points.notifications.actions.call_swap.swap.generic_failure.title": "An unexpected error occurred", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_call_popup.msteams_label": "Microsoft Teams", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.flows.message": "We couldn't find any flow that match your search", "extension_points.ui_blocks.interaction_info.form.contact_have_callback_tip": "Contact already has a callback scheduled for this same date and time. Please select another one.", "extension_points.ui_blocks.interaction_info.form.schedule_callback_timeout": "Please select a date and time after {{time}}", "extension_points.ui_blocks.interaction_actions.buttons.skip.text": "<PERSON><PERSON>", "extension_points.ui_blocks.panel.targets_list.tabs.labels.favorites": "Favorites", "extension_points.notifications.actions.call_answered_by_other_agent": "Another colleague has already answered this call", "extension_points.ui_blocks.dialog.title": "Consultation", "extension_points.ui_blocks.interaction_actions.no_action.end_call.external_phone": "End the call on your External Phone", "extension_points.notifications.emergency.disabled.title": "Emergency Services are disabled", "extension_points.notifications.agent_status_synced.message": "Colleague status synced with omnichannel", "extension_points.ui_blocks.interaction_actions.no_actions.transfer.sip": "To complete the transfer, please end the call on your SIP Device", "extension_points.notifications.agent_status_overridden.message": "Your status has been set to {{status}} by {{name}}", "voice-sdk.notifications.siloed_account": "Your account is suspended. You will be unable to place or receive calls until the account is recharged.", "extension_points.notifications.actions.outbound_caller_id_error.title": "Couldn’t place the call", "extension_points.notifications.drop_voicemail.active_error.message": "You can still make and receive calls. Please reload or restart the app to try again. If this problem persists, contact your administrator.", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.title": "No queues found", "extension_points.ui_blocks.common.MONTHS.6": "June", "extension_points.notifications.actions.quality_feedback.failure.message": "An error occurred and call quality feedback was not submitted. If the error persists, contact your Admin.", "extension_points.ui_blocks.interaction_info.chip.auto_submit": "Auto-submit in {{time}}", "extension_points.ui_blocks.dialog.consultation": "You are about to consult <strong>{{ number }}</strong> and the current call will be put on hold", "extension_points.ui_blocks.dialog.leave_conference.confirm_text": "Leave call", "extension_points.ui_blocks.dialog.nailed_up_mismatch.second_detail": "Your admin has made changes to the external devices you can connect. To connect to another external device, go to Conversation Settings.", "extension_points.notifications.actions.connect_call.title": "Couldn't connect the call", "extension_points.notifications.actions.call_swap.accept_and_swap.ongoing_transfer_conference_failure": "Cannot accept a call while in a Conference or Consultation.", "extension_points.notifications.received.consultation.on_consultation.message": "Please end the current consultation and try again", "extension_points.notifications.actions.call_swap.swap.not_in_source_interaction_failure": "Cannot switch calls while in a Conference or Consultation.", "extension_points.notifications.permission.no_microphone_access.title": "Unable to access the microphone.", "extension_points.ui_blocks.panel.targets_list.tabs.labels.external_number": "Dial a number", "extension_points.ui_blocks.interaction_info.form.diff_timezone_tip": "This is {{time}} in your timezone", "extension_points.notifications.received.consultation.on_conference.message": "Please end the current conference call and try again", "extension_points.notifications.actions.agent_call.external_devices_reject_internal_calls": "Internal calls are disabled when external devices (SIP or External Phone) are configured.", "extension_points.notifications.actions.call_swap.swap.not_in_progress_failure.message": "The ongoing call was dropped", "extension_points.ui_blocks.common.MONTHS.1": "January", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.offline": "Offline", "extension_points.commands.unknown_agent": "Unknown Colleague", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.flows": "Search by name", "extension_points.ui_blocks.interaction_info.behaviors.requeue.desc": "Requeues the call to be connected later", "extension_points.notifications.actions.call_swap.accept_and_swap.generic_failure.message": "If this problem persists, please contact your administrator", "extension_points.ui_blocks.panel.targets_list.error_states.agents.title": "Could not load the colleagues list", "extension_points.ui_blocks.panel.targets_list.error_states.queues.title": "Could not load the queues list", "extension_points.ui_blocks.panel.targets_list.tabs.labels.flows": "Flows", "extension_points.ui_blocks.interaction_info.form.reschedule_call.contactPreferredTimeWindow.endTime": "End time", "extension_points.ui_blocks.panel.targets_list.error_states.personas.message": "Something went wrong. Please refresh the page or try again later.", "extension_points.notifications.actions.wrap_up.disposition_timeout.message": "The wrap-up was automatically dismissed and changes haven’t been saved", "extension_points.ui_blocks.interaction_info.states.conversation_ivr_voice_call": "On a Call IVR", "extension_points.notifications.received.consultation.on_conference.title": "Cannot start a transfer or a consultation while a conference is in progress", "extension_points.ui_blocks.interaction_info.form.validTime": "The time frame should be from 12:00 AM to 11:59 PM.", "extension_points.ui_blocks.dialog.choice.consultation.text": "Consult the number {{ number }} while the current active call is put on hold", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.agents.message": "We couldn't find any colleagues that match your search", "extension_points.ui_blocks.common.WeekDays.MON": "Mon", "extension_points.ui_blocks.interaction_actions.buttons.reschedule.disableTooltipText": "The rescheduling limit has been reached", "extension_points.ui_blocks.interaction_actions.no_action.end_call.sip_phone": "End the call on your SIP Device", "extension_points.notifications.actions.skip_finalize_success": "Call was finalized successfully", "extension_points.ui_blocks.interaction_info.form.behavior.placeholder": "Select behavior", "extension_points.ui_blocks.interaction_info.chip.auto_dismiss": "Auto-dismiss in {{time}}", "extension_points.notifications.drop_voicemail.active_error.title": "The Voicemail Drop feature is currently unavailable.", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.favorites.message": "We couldn't find any favorites that match your search", "extension_points.ui_blocks.interaction_info.form.reschedule_call.contactPreferredTimeWindow": "Contact preferred time window", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.message": "There are no colleagues configured on your account", "extension_points.ui_blocks.interaction_info.form.agents_have_callback_tip": "You already have a schedule for this date and time.", "extension_points.ui_blocks.interaction_actions.buttons.connect.text": "Connect to call", "extension_points.notifications.unrecoverable_errors.conversation_ivr.message": "Please wait for the call to be picked up. If this problem persists, please contact your administrator", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.favorites": "Search favorites...", "extension_points.ui_blocks.interaction_info.behaviors.finalize.hint": "Finalizing will remove the call from the queue and the record will no longer be dialed in this campaign", "extension_points.notifications.conference.owner_changed.owner.title": "Conference hosting changed to you", "extension_points.ui_blocks.common.MONTHS.7": "July", "extension_points.ui_blocks.interaction_info.behaviors.requeue.value": "requeue", "extension_points.ui_blocks.interaction_info.behaviors.requeue.text": "<PERSON><PERSON><PERSON>", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.refine_search": "{{count}} more colleagues. Use the search to refine further.", "extension_points.notifications.actions.call_swap.swap.generic_failure.message": "If this problem persists, please contact your administrator", "extension_points.notifications.actions.quality_feedback.failure.title": "The information submitted during Wrap-Up may not have been saved.", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.title": "Could not load the queues list", "extension_points.ui_blocks.interaction_info.behaviors.requeue.hint": "Requeuing will send the call back to the queue to be connected later", "extension_points.notifications.cti_not_connected.message": "Click-to-Call and Contact Pop capabilities won’t work. Please make sure Talkdesk CTI Connector is correctly configured for your integration. For more information, refer to our Knowledge Base.", "extension_points.notifications.unrecoverable_errors.schedule_callback.title": "Couldn't load the schedule-callback screen", "extension_points.ui_blocks.interaction_info.form.reschedule_call.contactPreferredTimeWindow.startTime": "Start time", "extension_points.ui_blocks.interaction_info.behaviors.finalize.desc": "Removes the call from the queue", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.ring_groups.message": "We couldn't find any queues that match your search", "extension_points.ui_blocks.outbound_actions.button_labels.start_voice_conversation_sip": "SIP Device", "extension_points.notifications.actions.call_swap.join.generic_failure.message": "If this problem persists, please contact your administrator", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.message": "There was a problem loading the queues list, please try again.", "extension_points.ui_blocks.panel.targets_list.tabs.labels.ring_groups": "Queues", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.flows.refine_search": "{{count}} more flows. Use the search to refine further.", "extension_points.ui_blocks.dialog.choice.button.confirm": "Confirm", "extension_points.ui_blocks.interaction_actions.buttons.drop_voicemail.text": "Voicemail drop", "extension_points.notifications.invalid_number.error": "Invalid number", "extension_points.notifications.actions.call_swap.accept.incoming_dedicated_while_dialing.message": "Cannot accept this call while dialing another contact", "extension_points.notifications.cti_not_connected.title": "Your default integration is not connected.", "extension_points.ui_blocks.interaction_info.form.same_timezone_tip": "You are in the same timezone", "extension_points.notifications.actions.call_swap.accept_and_swap.not_in_progress_failure.title": "Couldn't establish the call", "extension_points.commands.topics.uc_call": "Internal call", "extension_points.ui_blocks.panel.targets_list.error_states.personas.title": "We couldn't load colleagues list", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.personas.message": "We couldn't find any colleagues that match your search", "extension_points.notifications.emergency.disabled.message": "To call an emergency number, Emergency Services must be enabled. Please contact your administrator.", "extension_points.ui_blocks.interaction_info_consultation.active_states.disconnected_participant_conversation": "Talking", "extension_points.ui_blocks.interaction_actions.buttons.skip.title": "<PERSON><PERSON>", "extension_points.notifications.actions.conference.leave_conference_failure.message": "Please try again", "extension_points.ui_blocks.interaction_info.form.isOutThePrefferedTime": "Please select a time within the contact preferred time window", "extension_points.ui_blocks.interaction_info.form.isOutTheCallingHours": "Please select a date time within the campaign calling hours", "extension_points.notifications.inbound_call.agent_to_agent": "Internal call", "extension_points.notifications.actions.call_swap.accept.incoming_dedicated_while_dialing.title": "Couldn't accept the call", "extension_points.ui_blocks.panel.tabs.left_control": "<PERSON><PERSON> tabs back", "extension_points.ui_blocks.panel.targets_list.transfer_target.selection.contactCenter": "Contact center", "extension_points.ui_blocks.panel.targets_list.error_states.agents.message": "There was a problem loading the colleagues list, please try again.", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.message": "There was a problem loading the favorites list, please try again.", "extension_points.ui_blocks.panel.targets_list.error_states.queues.message": "There was a problem loading the queues list, please try again.", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.message": "There are no favorite numbers configured on your account", "extension_points.ui_blocks.common.WeekDays.THU": "<PERSON>hu", "extension_points.notifications.click_to_call.number_with_extension_not_supported.message": "It is not possible to initiate a click-to-call to a number with extensions or post-dial codes. The call will proceed to the main number.", "extension_points.notifications.actions.call_swap.accept_and_swap.not_in_source_interaction_failure.message": "Cannot accept consultations or conferences after accepting a direct call.", "extension_points.ui_blocks.outbound_actions.button_labels.start_voice_conversation_external_phone": "External Device", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_call_popup.group_name": "Call to", "extension_points.notifications.received.consultation.on_hold.title": "Cannot start a consultation while on hold", "extension_points.ui_blocks.panel.targets_list.transfer_target.personaItem_status.busy": "Busy", "extension_points.commands.topics.agent_call": "Internal call", "extension_points.ui_blocks.dialog.leave_conference.transfer_host_title": "Leave and transfer hosting", "extension_points.notifications.actions.outbound_caller_id_error.message": "Please select an outbound caller ID", "extension_points.ui_blocks.dialog.confirm_text": "Consult", "extension_points.notifications.invalid_number": "This number might be invalid. The call may fail.", "extension_points.ui_blocks.panel.targets_list.empty_states.no_personas.title": "No colleagues found", "extension_points.notifications.actions.wrap_up.disposition_timeout.title": "Wrap-up time expired", "extension_points.ui_blocks.interaction_info.behaviors.finalize.warning": "The max number of skips has been reached. The only enabled skip behavior is Finalize.", "extension_points.ui_blocks.panel.titles.agent_call": "Call a colleague", "extension_points.ui_blocks.components.targets_list.transfer_target.date.title": "Date", "extension_points.ui_blocks.interaction_info.form.time": "Time", "extension_points.ui_blocks.interaction_info.form.failed_to_load_timezone": "Failed to load timezone", "extension_points.notifications.actions.call_swap.accept_and_swap.not_in_source_interaction_failure.title": "Couldn't accept the call", "extension_points.ui_blocks.panel.targets_list.empty_states.no_flows.message": "There are no flows configured for transferred calls on your account", "extension_points.notifications.inbound_call": "Incoming call", "extension_points.ui_blocks.common.MONTHS.9": "September", "extension_points.ui_blocks.interaction_info.form.date": "Date", "extension_points.ui_blocks.common.MONTHS.8": "August", "extension_points.ui_blocks.dialog.leave_conference.transfer_title": "Leave call", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.message": "There are no queues configured on your account", "extension_points.ui_blocks.interaction_actions.no_actions.answer_call.external_device": "Answer the call on your External Phone", "extension_points.notifications.actions.target.empty_ring_group.title": "No colleagues assigned to this queue", "extension_points.notifications.received.consultation.on_agent_to_agent.message": "Please end the current call and try again", "extension_points.notifications.ongoing_interactions_error.message": "Maximum on-going interactions reached", "extension_points.notifications.conference.owner_changed.update_guest_state_error.message": "Participants information may be outdated.", "extension_points.notifications.emergency.no_address.first_button_label": "Update settings", "extension_points.ui_blocks.panel.targets_list.empty_states.no_flows.title": "No flows found", "extension_points.notifications.actions.call_swap.swap.not_in_progress_failure.title": "Couldn't establish the call", "extension_points.ui_blocks.interaction_actions.buttons.switch_back_to_call.text": "Switch back to this call", "extension_points.ui_blocks.interaction_info.form.users_reached_max_callback_tip": "Your account has reached max. scheduled callbacks limitation in an hour, please select another time", "extension_points.notifications.actions.call_swap.join.generic_failure.title": "An unexpected error occurred", "extension_points.notifications.conference.owner_changed.participant.title": "Conference hosting changed to {{new_owner}}", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.ring_groups.refine_search": "{{count}} more queues. Use the search to refine further.", "extension_points.ui_blocks.dialog.choice.consultation.unavailable": "Cannot start a consultation while the call is on hold", "extension_points.notifications.actions.conference.leave_conference_failure.title:": "Couldn't leave the call", "extension_points.ui_blocks.outbound_actions.external_device_toast.message": "Your status was changed to Offline and your voice handling is now Conversations. Your admin has made changes to the external devices you can connect. If you wish to connect to another external device, go to Conversation Settings.", "extension_points.notifications.actions.conference.leave_conference_failure.title": "Couldn't leave the call", "extension_points.notifications.actions.call_swap.click_to_consult.all_calls_on_hold.message": "Please unhold one of the calls", "extension_points.notifications.actions.consultation.merge_call.title": "Couldn't add guest", "extension_points.ui_blocks.interaction_actions.buttons.make_call.text": "Make call", "extension_points.ui_blocks.interaction_info.form.reschedule_call.Text": "Reschedule", "extension_points.notifications.actions.call_swap.accept_and_swap.generic_failure.title": "An unexpected error occurred", "extension_points.notifications.actions.agent_call.unavailable": "Couldn't start the call. Your colleague is unavailable", "extension_points.ui_blocks.common.MONTHS.12": "December", "extension_points.notifications.actions.call_swap.click_to_consult.all_calls_on_hold.title": "Cannot start a consultation while all calls are on hold", "extension_points.notifications.emergency.no_address.message": "Please update your Emergency settings", "extension_points.notifications.actions.consultation.failure_no_agents.message": "Couldn’t start the consultation call as there are no colleagues available in the selected queue. Please try again or select another destination.", "extension_points.ui_blocks.dialog.leave_conference.transfer_external.second_subtitle": "The other participants will continue on call.", "extension_points.ui_blocks.interaction_info.form.reschedule_call.contactPreferredTimeWindow.hoverTip": "Contact will always be called within the preferred time window", "extension_points.ui_blocks.panel.targets_list.error_states.flows.message": "There was a problem loading the flows list, please try again.", "extension_points.ui_blocks.dialog.leave_conference.transfer_agent.second_subtitle": "<strong>{{variable}}</strong> will take control of the call.", "extension_points.ui_blocks.interaction_info.states.incoming_voice_agent_call": "Internal call", "extension_points.ui_blocks.panel.targets_list.transfer_target.selection.moreDepartments": "More departments"}