{"menus": [{"id": "conversation-app-tabs-v2", "evaluation": {"children": [{"id": "522e6616-8909-4a3a-84bd-9faa2cf84eef", "title": "Snapshot", "type": "droplet", "slug": "snapshot-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Snapshot", "pt-PT": "Visão geral", "pt-BR": "Visão geral", "it-IT": "Visione generale", "fr-FR": "Vision générale", "fr-CA": "Vue globale", "es-ES": "Resumen", "de-DE": "Übersicht"}, "owner": "@Talkdesk/conversation-team", "states": {"dialing": {"channels": ["voice"], "direction": ["outbound"], "configurations": {"contact_read_only": true, "auto_open_links": false}, "bindings": {"contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}}}, "invite": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": true, "auto_open_links": true}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "is_transfer": {"key": "is_transfer", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "waiting_time": {"key": "waiting_time", "type": "external"}}}, "conversation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": false, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_email": {"key": "contact_person_email", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "contact_switch": {"key": "contact_switch", "type": "action"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "is_transfer": {"key": "is_transfer", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "waiting_time": {"key": "waiting_time", "type": "external"}}}, "conversation_consultation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": true, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "contact_switch": {"key": "contact_switch", "type": "action"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "is_transfer": {"key": "is_transfer", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "waiting_time": {"key": "waiting_time", "type": "external"}}}, "digital_conversation": {"channels": ["live-chat", "sms", "email", "digital-connect", "digital-connect-fax", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": false, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_email": {"key": "contact_person_email", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "visitor_name": {"key": "visitor_name", "type": "local"}, "visitor_number": {"key": "visitor_number", "type": "local"}, "visitor_email": {"key": "visitor_email", "type": "local"}, "contact_switch": {"key": "contact_switch", "type": "action"}, "change_contact_name": {"key": "change_contact_name", "type": "action"}, "create_contact": {"key": "create_contact", "type": "action"}, "associate_contact": {"key": "associate_contact", "type": "action"}, "merge_contact": {"key": "merge_contact", "type": "action"}, "contacts_panel_show_switch": {"key": "contacts_panel_show_switch", "type": "action"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "initial_screen_person_contact": {"key": "initial_screen_person_contact", "type": "external"}, "initial_screen_person_number": {"key": "initial_screen_person_number", "type": "external"}, "initial_screen_person_email": {"key": "initial_screen_person_email", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "contact_person_fax_number": {"key": "contact_person_fax_number", "type": "external"}}}, "digital_preview_conversation": {"channels": ["live-chat", "sms", "email", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": true, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_email": {"key": "contact_person_email", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "visitor_name": {"key": "visitor_name", "type": "local"}, "visitor_number": {"key": "visitor_number", "type": "local"}, "visitor_email": {"key": "visitor_email", "type": "local"}, "contact_switch": {"key": "contact_switch", "type": "action"}, "change_contact_name": {"key": "change_contact_name", "type": "action"}, "create_contact": {"key": "create_contact", "type": "action"}, "associate_contact": {"key": "associate_contact", "type": "action"}, "merge_contact": {"key": "merge_contact", "type": "action"}, "contacts_panel_show_switch": {"key": "contacts_panel_show_switch", "type": "action"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "initial_screen_person_contact": {"key": "initial_screen_person_contact", "type": "external"}, "initial_screen_person_number": {"key": "initial_screen_person_number", "type": "external"}, "initial_screen_person_email": {"key": "initial_screen_person_email", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}}}, "digital_offer_conversation": {"channels": ["live-chat", "email", "sms", "digital-connect", "digital-connect-whatsapp", "fbm", "whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {"contact_read_only": false, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_email": {"key": "contact_person_email", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "previous_user_href": {"key": "previous_user_href", "type": "external"}, "visitor_name": {"key": "visitor_name", "type": "local"}, "visitor_number": {"key": "visitor_number", "type": "local"}, "visitor_email": {"key": "visitor_email", "type": "local"}, "contact_switch": {"key": "contact_switch", "type": "action"}, "change_contact_name": {"key": "change_contact_name", "type": "action"}, "create_contact": {"key": "create_contact", "type": "action"}, "associate_contact": {"key": "associate_contact", "type": "action"}, "merge_contact": {"key": "merge_contact", "type": "action"}, "contacts_panel_show_switch": {"key": "contacts_panel_show_switch", "type": "action"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "initial_screen_person_contact": {"key": "initial_screen_person_contact", "type": "external"}, "initial_screen_person_number": {"key": "initial_screen_person_number", "type": "external"}, "initial_screen_person_email": {"key": "initial_screen_person_email", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "contact_person_fax_number": {"key": "contact_person_fax_number", "type": "external"}}}, "schedule_callback_dialing": {"channels": ["voice"], "direction": ["outbound"], "configurations": {"contact_read_only": true, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}}}, "preview_dialing": {"channels": ["voice"], "direction": ["outbound"], "configurations": {"contact_read_only": true, "auto_open_links": false}, "bindings": {"ring_groups": {"key": "ring_groups", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}}}}}}, {"id": "9e1850d6-564c-465a-a078-470e450bf48d", "title": "Notes", "type": "droplet", "slug": "notes-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Notes", "pt-PT": "Notas", "pt-BR": "Notas", "it-IT": "Note", "fr-FR": "<PERSON><PERSON><PERSON>", "fr-CA": "<PERSON><PERSON><PERSON>", "es-ES": "Notas", "de-DE": "Notizen", "nl-NL": "Notities"}, "owner": "@Talkdesk/conversation-team", "states": {"conversation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"notes": {"key": "notes", "type": "local"}, "dispositions_href": {"key": "dispositions_href", "type": "external"}, "disposition_set_id": {"key": "disposition_set_id", "type": "local"}, "disposition_id": {"key": "disposition_id", "type": "local"}, "disposition_name": {"key": "disposition_name", "type": "local"}, "disposition_set_name": {"key": "disposition_set_name", "type": "local"}, "nested_disposition_id": {"key": "nested_disposition_id", "type": "local"}, "nested_disposition_name": {"key": "nested_disposition_name", "type": "local"}, "channel_type": {"key": "channel_type", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}}}, "digital_conversation": {"channels": ["live-chat", "sms", "email", "digital-connect", "digital-connect-fax", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"notes": {"key": "notes", "type": "local"}, "note_id": {"key": "note_id", "type": "local"}, "dispositions_href": {"key": "dispositions_href", "type": "external"}, "disposition_set_id": {"key": "disposition_set_id", "type": "local"}, "disposition_id": {"key": "disposition_id", "type": "local"}, "disposition_name": {"key": "disposition_name", "type": "local"}, "disposition_set_name": {"key": "disposition_set_name", "type": "local"}, "nested_disposition_id": {"key": "nested_disposition_id", "type": "local"}, "nested_disposition_name": {"key": "nested_disposition_name", "type": "local"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "notes_information": {"key": "notes_information", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}}}}}}, {"id": "e3e0b351-0571-43d3-b83a-c8b9093d67ce", "title": "Dispositions", "type": "droplet", "slug": "dispositions-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Wrap-up", "pt-PT": "Resumo", "pt-BR": "Resumo", "it-IT": "Riepilogo", "fr-FR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fr-CA": "Résumé", "es-ES": "Resumen", "de-DE": "Zusammenfassung"}, "owner": "@Talkdesk/conversation-team", "states": {"wrap_up": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {"required": ["dispositions"]}, "bindings": {"rating": {"key": "rating", "type": "local"}, "reasons": {"key": "reasons", "type": "local"}, "notes": {"key": "notes", "type": "local"}, "dispositions_href": {"key": "dispositions_href", "type": "external"}, "disposition_set_id": {"key": "disposition_set_id", "type": "local"}, "disposition_id": {"key": "disposition_id", "type": "local"}, "disposition_name": {"key": "disposition_name", "type": "local"}, "disposition_set_name": {"key": "disposition_set_name", "type": "local"}, "nested_disposition_id": {"key": "nested_disposition_id", "type": "local"}, "nested_disposition_name": {"key": "nested_disposition_name", "type": "local"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "contact_person_id": {"key": "contact_person_id", "type": "local"}, "channel_type": {"key": "channel_type", "type": "external"}, "ring_groups": {"key": "ring_groups", "type": "external"}, "is_transfer": {"key": "is_transfer", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "voice_interaction_start_timestamp": {"key": "voice_interaction_start_timestamp", "type": "external"}, "interaction_external_id": {"key": "interaction_external_id", "type": "local"}, "default_cti_integration": {"key": "default_cti_integration", "type": "external"}, "call_relating": {"key": "call_relating", "type": "external"}, "mandatory_call_relating": {"key": "mandatory_call_relating", "type": "external"}, "external_ids": {"key": "external_ids", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "waiting_time": {"key": "waiting_time", "type": "external"}, "relate_to_active_channels": {"key": "relate_to_active_channels", "type": "external"}}}, "digital_wrap_up": {"channels": ["live-chat", "sms", "email", "digital-connect", "digital-connect-fax", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {"required": ["dispositions"]}, "bindings": {"notes": {"key": "notes", "type": "local"}, "note_id": {"key": "note_id", "type": "local"}, "dispositions_href": {"key": "dispositions_href", "type": "external"}, "disposition_set_id": {"key": "disposition_set_id", "type": "local"}, "disposition_id": {"key": "disposition_id", "type": "local"}, "disposition_name": {"key": "disposition_name", "type": "local"}, "disposition_set_name": {"key": "disposition_set_name", "type": "local"}, "nested_disposition_id": {"key": "nested_disposition_id", "type": "local"}, "nested_disposition_name": {"key": "nested_disposition_name", "type": "local"}, "visitor_name": {"key": "visitor_name", "type": "local"}, "visitor_number": {"key": "visitor_number", "type": "local"}, "visitor_email": {"key": "visitor_email", "type": "local"}, "contacts_panel_show_switch": {"key": "contacts_panel_show_switch", "type": "action"}, "change_contact_name": {"key": "change_contact_name", "type": "action"}, "create_contact": {"key": "create_contact", "type": "action"}, "associate_contact": {"key": "associate_contact", "type": "action"}, "merge_contact": {"key": "merge_contact", "type": "action"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_email": {"key": "contact_person_email", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "initial_screen_person_contact": {"key": "initial_screen_person_contact", "type": "external"}, "initial_screen_person_number": {"key": "initial_screen_person_number", "type": "external"}, "initial_screen_person_email": {"key": "initial_screen_person_email", "type": "external"}, "channel_type": {"key": "channel_type", "type": "external"}, "contact_person_id": {"key": "contact_person_id", "type": "local"}, "notes_information": {"key": "notes_information", "type": "external"}, "interaction_id": {"key": "interaction_id", "type": "external"}, "voice_interaction_start_timestamp": {"key": "voice_interaction_start_timestamp", "type": "external"}, "interaction_external_id": {"key": "interaction_external_id", "type": "local"}, "default_cti_integration": {"key": "default_cti_integration", "type": "external"}, "call_relating": {"key": "call_relating", "type": "external"}, "mandatory_call_relating": {"key": "mandatory_call_relating", "type": "external"}, "external_ids": {"key": "external_ids", "type": "external"}, "relate_to_active_channels": {"key": "relate_to_active_channels", "type": "external"}, "contact_person_fax_number": {"key": "contact_person_fax_number", "type": "external"}}}}}}, {"id": "f42bf3f8-a828-4181-9c3e-ea46473f1045", "title": "Contact Activities Conversation Tab", "type": "droplet", "slug": "contact-activities-conversation-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Activity", "pt-PT": "Atividade", "pt-BR": "Atividade", "it-IT": "Attività", "fr-FR": "Activité", "fr-CA": "Activité", "es-ES": "Actividad", "de-DE": "Aktivitäten"}, "owner": "@Talkdesk/apps-productivity-eagle", "states": {"conversation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"contact_person_id": {"key": "contact_person_id", "type": "external"}}}, "digital_conversation": {"channels": ["live-chat", "sms", "email", "digital-connect", "digital-connect-fax", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"contact_person_id": {"key": "contact_person_id", "type": "external"}}}, "wrap_up": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"contact_person_id": {"key": "contact_person_id", "type": "external"}, "local_contact_person_id": {"key": "contact_person_id", "type": "local"}}}, "digital_wrap_up": {"channels": ["live-chat", "sms", "email", "digital-connect", "digital-connect-fax", "fbm", "whatsapp", "digital-connect-whatsapp", "apple-messages-for-business"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"contact_person_id": {"key": "contact_person_id", "type": "external"}, "local_contact_person_id": {"key": "contact_person_id", "type": "local"}}}, "preview_dialing": {"channels": ["voice"], "direction": ["outbound"], "configurations": {}, "bindings": {"contact_person_id": {"key": "contact_person_id", "type": "external"}}}}}}, {"id": "1d999639-e5fb-4add-8858-c38ca7daee72", "title": "Identity", "type": "droplet", "slug": "identity-conversation-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Identity"}, "owner": "@Talkdesk/ai-guardian", "states": {"invite": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {"ringing": true}, "bindings": {"contact_person_number": {"key": "contact_person_number", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}}}, "conversation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"contact_person_number": {"key": "contact_person_number", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}}}}}}, {"id": "3ab68ba3-4558-4502-ac77-a695a1a9b94f", "title": "Callback Scheduler", "type": "droplet", "slug": "manager-contact-card-tab", "path": "/", "metadata": {"type": "standalone", "tab_name": {"en-US": "Callback Scheduler"}, "owner": "@Talkdesk/continuouseng-deeting", "states": {"conversation": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"interaction_id": {"key": "interaction_id", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "is_proactive_outbound": {"key": "is_proactive_outbound", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "talkdesk_number": {"key": "talkdesk_number", "type": "external"}, "contact_person_id": {"key": "contact_person_id", "type": "external"}}}, "wrap_up": {"channels": ["voice"], "direction": ["inbound", "outbound"], "configurations": {}, "bindings": {"interaction_id": {"key": "interaction_id", "type": "external"}, "interaction_context_href": {"key": "interaction_context_href", "type": "external"}, "contact_person_number": {"key": "contact_person_number", "type": "external"}, "contact_person_href": {"key": "contact_person_href", "type": "external"}, "is_proactive_outbound": {"key": "is_proactive_outbound", "type": "external"}, "call_type": {"key": "call_type", "type": "external"}, "talkdesk_number": {"key": "talkdesk_number", "type": "external"}, "contact_person_id": {"key": "contact_person_id", "type": "external"}}}}}}]}}], "_embedded": {"apps": [{"id": "c3d08a0a92eb489fbc6c3d7c7e152d22", "name": "Callback Scheduler", "scopes": ["account-uc-status:read", "apps:graphql", "byot-voice-call:read", "call-entry:read", "callbar", "callbar-analytics:write", "callbar-settings:read", "campaigns:read", "device-routing-nailup:write", "device-routing:read", "device-routing:write", "dispositions:read", "do-not-call-lists:manage", "emergency-device-locations:read", "graph-users:read", "integrations-zendesk-tickets:read", "interaction-quality-feedback:write", "interaction-quality-settings:read", "interaction-quality-settings:write", "interactions:disconnect", "interactions:hold", "interactions:transfer", "megazord:read", "numbers:read", "openid", "po-callbacks:read", "po-callbacks:write", "po-interactions:read", "policies:evaluate", "presence-user:read", "record-lists:manage", "ring-groups:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "sentiment-settings:read", "ur:read", "ur:write", "user-session:end", "users:read", "schedule-callbacks:read", "schedule-callbacks:write", "context:read"], "capabilities": ["@atlas/product-analytics", "authorization", "toast", "messaging", {"name": "protocol", "protocols": []}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "continuouseng-deeting"}}, {"name": "rtm", "events": {"account": ["agent_status_changed", "outbound_call_answered"], "user": ["call_leg_answered"]}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/manager-contact-tab/0.23.1/index.html", "slug": "manager-contact-card-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg"}, "version": "1.3.5", "version_id": "e564005682db41739cf7d5d4c27cdc9f"}, {"id": "02be8e2160a94c7fb900d1d64cd1e15e", "name": "Dispositions Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/dispositions-tab/index.html", "slug": "dispositions-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.32", "version_id": "f2b9f915aca14026aede37df86da5ec4"}, {"id": "5a7e1ab6b0de4cb0bf68f4d972d68479", "name": "Notes Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/notes-tab/index.html", "slug": "notes-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.31", "version_id": "0d11a6e7aa9842a2af43f8315f977e7b"}, {"id": "d9509283197f487f9242d902dd4d4f03", "name": "Snapshot Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/snapshot-tab/index.html", "slug": "snapshot-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.31", "version_id": "acda71c3d61842619f35fcd8880a04a9"}, {"id": "963846c212364661a38c007c13c55465", "name": "Identity Tab", "scopes": ["openid", "users:read", "account:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "policies:evaluate", "guardian:read", "guardian:write", "guardian-users:read", "guardian-upsell:write", "voicebiometrics:read", "identity-phone-validation:read", "graph-users:read", "apps:read", "licenses:read", "context:read", "identity:read", "voicebiometrics-enroll:write", "voicebiometrics-consent:write", "voicebiometrics-consent:read", "contacts:read", "identity-upsell:write", "identity-notifications:write", "identity-subscriptions:read"], "capabilities": [{"name": "i18n", "languages": ["pt-PT", "en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, "authorization", "navigation", "form", "toast", "popup", "download", "messaging"], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/identity-conversation-tab/1.13.0/index.html", "slug": "identity-conversation-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/identity.svg"}, "version": "0.0.9", "version_id": "ccd2574ea26e4f4fae1c72eb914e3bef"}, {"id": "05dcac0ef84d4fbfa18a38f8a12a626a", "name": "Contact Activities Conversation Tab", "scopes": ["account:read", "contacts-activities:read", "graph-users:read", "numbers:read", "openid", "policies:evaluate", "recordings:read", "contact-details:read", "digital-connect:read", "provider-abstraction-service:read", "email-touchpoints:read", "contact-interactions:write", "activity-notes:write", "interaction-contacts:read", "omnichannel-inbox:write", "customer-data-store:read", "ai-generative-function:invoke", "interaction-contacts:write", "cases:read", "apps:graphql"], "capabilities": ["authorization", "messaging", "navigation", "clipboard", "popup", "toast", "portal", "download", {"name": "protocol", "protocols": ["voicemails:show-details"]}, "@atlas/context", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/contact-activities-app/2.4.6/contact-activities-portal-app/index.html?config=conversation-tab", "slug": "contact-activities-conversation-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "0.0.8", "version_id": "2f2ae7cab6e34ef88735c6181d48e615"}], "isFallback": false}}