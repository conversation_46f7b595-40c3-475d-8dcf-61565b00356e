{"error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","line":"{","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"type\": \"trace\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 9 (line 1 column 10)","line":"\"entries\": [","type":"malformed"}
{"error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","line":"{","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSO<PERSON> at position 6 (line 1 column 7)","line":"\"type\": \"action\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after <PERSON>SO<PERSON> at position 8 (line 1 column 9)","line":"\"action\": \"goto\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 5 (line 1 column 6)","line":"\"url\": \"https://example.com\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","line":"\"timestamp\": 1748248216724","type":"malformed"}
{"error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","line":"},","type":"malformed"}
{"error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","line":"{","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"type\": \"action\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","line":"\"action\": \"click\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 10 (line 1 column 11)","line":"\"selector\": \"button#submit\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","line":"\"timestamp\": 1748248217724","type":"malformed"}
{"error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","line":"},","type":"malformed"}
{"error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","line":"{","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"type\": \"console\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 13 (line 1 column 14)","line":"\"messageType\": \"log\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"text\": \"Button clicked successfully\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","line":"\"timestamp\": 1748248218224","type":"malformed"}
{"error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","line":"},","type":"malformed"}
{"error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","line":"{","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"type\": \"console\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 13 (line 1 column 14)","line":"\"messageType\": \"error\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","line":"\"text\": \"Network request failed\",","type":"malformed"}
{"error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","line":"\"timestamp\": 1748248218724","type":"malformed"}
{"error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","line":"}","type":"malformed"}
{"error":"SyntaxError: Unexpected token ']', \"]\" is not valid JSON","line":"]","type":"malformed"}
{"error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","line":"}","type":"malformed"}
