{"type": "network", "entries": [{"type": "request", "url": "https://example.com", "method": "GET", "status": 200, "headers": [{"name": "Content-Type", "value": "text/html"}, {"name": "Cache-Control", "value": "no-cache"}], "timestamp": 1748248216724}, {"type": "request", "url": "https://example.com/api/data", "method": "POST", "status": 500, "headers": [{"name": "Content-Type", "value": "application/json"}], "timestamp": 1748248217724}, {"type": "request", "url": "https://analytics.google.com/track", "method": "GET", "status": 200, "headers": [{"name": "Content-Type", "value": "image/gif"}], "timestamp": 1748248218724}]}