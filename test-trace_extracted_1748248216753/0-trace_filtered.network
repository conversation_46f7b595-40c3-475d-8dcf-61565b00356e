{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"type\": \"network\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after <PERSON>SO<PERSON> at position 9 (line 1 column 10)","_originalLine":"\"entries\": [","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"type\": \"request\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 5 (line 1 column 6)","_originalLine":"\"url\": \"https://example.com\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"method\": \"GET\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"status\": 200,","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 9 (line 1 column 10)","_originalLine":"\"headers\": [","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"name\": \"Content-Type\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 7 (line 1 column 8)","_originalLine":"\"value\": \"text/html\"","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","_originalLine":"},","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"name\": \"Cache-Control\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 7 (line 1 column 8)","_originalLine":"\"value\": \"no-cache\"","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","_originalLine":"}","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token ']', \"],\" is not valid JSON","_originalLine":"],","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","_originalLine":"\"timestamp\": 1748248216724","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","_originalLine":"},","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"type\": \"request\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 5 (line 1 column 6)","_originalLine":"\"url\": \"https://example.com/api/data\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"method\": \"POST\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"status\": 500,","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 9 (line 1 column 10)","_originalLine":"\"headers\": [","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"name\": \"Content-Type\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 7 (line 1 column 8)","_originalLine":"\"value\": \"application/json\"","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","_originalLine":"}","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token ']', \"],\" is not valid JSON","_originalLine":"],","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","_originalLine":"\"timestamp\": 1748248217724","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"},\" is not valid JSON","_originalLine":"},","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"type\": \"request\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 5 (line 1 column 6)","_originalLine":"\"url\": \"https://analytics.google.com/track\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"method\": \"GET\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 8 (line 1 column 9)","_originalLine":"\"status\": 200,","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 9 (line 1 column 10)","_originalLine":"\"headers\": [","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)","_originalLine":"{","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 6 (line 1 column 7)","_originalLine":"\"name\": \"Content-Type\",","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 7 (line 1 column 8)","_originalLine":"\"value\": \"image/gif\"","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","_originalLine":"}","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token ']', \"],\" is not valid JSON","_originalLine":"],","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected non-whitespace character after JSON at position 11 (line 1 column 12)","_originalLine":"\"timestamp\": 1748248218724","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","_originalLine":"}","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token ']', \"]\" is not valid JSON","_originalLine":"]","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}
{"_error":"SyntaxError: Unexpected token '}', \"}\" is not valid JSON","_originalLine":"}","snapshot":{"request":{"headers":[],"method":"UNKNOWN","url":"MALFORMED"},"response":{"content":{"mimeType":"","size":0},"headers":[],"status":0}},"type":"malformed"}