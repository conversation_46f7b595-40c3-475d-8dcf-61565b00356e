{"modals.deleteRecordConfirmation.title": "Delete record?", "tableHeaders.lastName": "Last name", "hints.deleteAction": "Delete record", "update.keyField": "Field", "update.title": "Record fields", "sidePanels.filters.timezone.search.loadMore": "Load more...", "count_plural": "{{count}} records", "filters.priorities": "Priority", "filters.priorities.plural": "priorities", "filters.timezones.plural": "time zones", "searchPlaceHolderText": "Search for first/last name or number", "modals.deleteRecordConfirmation.message": "You won't be able to retrieve the record back\t", "details.description": "Description", "sidePanels.filters.timezone.search.networkError.loadMoreErrorMessage": "An error occurred, please try again", "details.emptyMessage": "Guarantee that the searched detail is valid", "hints.highestPriority": "Hot leads have the highest priority", "update.value": "Value", "tableHeaders.phoneNumber": "Phone number", "sidePanels.filters.timezone.search.noResults": "No timezones found", "details.keyField": "Field", "tableHeaders.priority": "Priority", "details.value": "Value", "tableHeaders.priority.hint": "Record’s dialing priority(1 being the lowest)", "hotLead.not.description": "Set a dialing priority between 1 and 10 (Hot leads are only assigned through API)", "details.count": "{{count}} extra field", "filters.updatedTime": "Updated at", "filters.timezones": "Time zone", "sidePanels.filters.timezone.search.minLimit": "Type at least 3 characters to start searching", "filters.priorities.single": "priority", "sidePanels.filters.timezone.search.networkError.button": "Try again", "sidePanels.filters.timezone.search.networkError.message": "Couldn't load data", "searchTooltipText": "Type at least {{minLength}} characters to start searching", "details.subtitle": "Overview on {{firstName}} {{lastName}} with the number {{phoneNumber}} extra fields", "hotLead.is.description": "Dialer only allows editing records with a priority set between 1 and 10", "tableHeaders.createdAt.hint": "All times are displayed in the {timezone} timezone\t", "emptyMessage": "Guarantee that the record list is valid", "emptyTitle": "No available records", "tableHeaders.firstName": "First name", "hints.detailsAction": "See record extra fields", "tableHeaders.timezone": "Time zone", "hints.viewAction": "View record", "hotLead": "Hot lead", "details.title": "Extra fields", "sidePanels.filters.timezone.search.loading": "Loading", "sidePanels.filters.lastUpdated.label": "Last Updated", "sidePanels.filters.timezone.label": "Time zone", "hints.updateAction": "Edit record", "tableHeaders.updatedAt.hint": "All times are displayed in the {{timezone}} time zone", "tableHeaders.updatedAt.label": "Last updated", "details.count_plural": "{{count}} extra fields", "tableHeaders.createdAt.label": "Created at", "sidePanels.filters.timezone.search.noTimezones": "There are no timezones to use as filter", "emptyMessageByFilter": "You may want to try using different keywords, checking for typos, or adjusting applied filters", "noRecordsMessage": "There are no records left in this list, but you can still add new items through the API", "details.searchPlaceholder": "Find a record detail...", "details.emptyTitle": "No available details", "update.subtitle": "Overview on {{firstName}} {{lastName}} with the number {{phoneNumber}}", "sidePanels.filters.title": "Filters", "filters.timezones.single": "time zone", "noRecords": "No records", "emptyTitleByFilter": "Nothing turned up", "count": "{{count}} record"}