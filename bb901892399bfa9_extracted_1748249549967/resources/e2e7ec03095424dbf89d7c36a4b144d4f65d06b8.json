{"menus": [{"id": "workspace-primary", "evaluation": {"children": [{"id": "5c541a34-e95a-4c49-b93f-bdb973d37ff9", "title": "Home", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/home.svg", "slug": "home", "path": "/"}, {"id": "84e18268-55b6-4d44-851d-8432900b58f5", "title": "Conversations", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/conversation.svg", "slug": "conversation", "path": "/"}, {"id": "4e120cd0-a883-4fa8-9855-cb433a357cb7", "title": "Voicemails", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/voicemails.svg", "slug": "voicemails", "path": "/"}, {"id": "45b27085-0826-410f-bc27-db65c88ac5f7", "title": "Activities", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/activity.svg", "slug": "activity", "path": "/"}, {"id": "cdd3bcb9-0b17-4dff-b158-ba44fa4d0f3b", "title": "Contacts", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg", "slug": "contacts", "path": "/"}, {"id": "0fe8943f-29cc-41fe-9ba7-0d2f5f79f73f", "title": "<PERSON><PERSON><PERSON>", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg", "slug": "outbound-dialer", "children": [{"id": "3be76b2f-d829-4296-94f0-eb50d57648a6", "title": "Campaigns", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg", "slug": "outbound-dialer", "path": "/campaigns"}, {"id": "6e792c4d-5bab-4ea9-9849-a176530be933", "title": "Lists", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg", "slug": "outbound-dialer", "path": "/lists"}, {"id": "f68c6cdb-9daa-4bce-b91b-69fefe003954", "title": "Configurations", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg", "slug": "outbound-dialer", "path": "/configurations"}]}, {"id": "b9ff5837-2a98-4695-a880-584a602cebcb", "title": "Knowledge Management", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guide.svg", "slug": "guide", "path": "/"}, {"id": "e9888288-4e96-4970-99f0-48ff4f9dbd3e", "title": "Studio", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "children": [{"id": "b5feb98c-8996-496a-999d-9072063f86f0", "title": "Flows", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "classic-admin", "feature": "STUDIO_show_classic_admin_app_menu", "path": "/admin/studio"}, {"id": "970942af-f2dd-44e9-9924-e8470edd8356", "title": "Prompts", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "audio-prompts", "feature": "WORKFLOW_AUDIO_PROMPTS_ENABLE", "path": "/"}]}, {"id": "8d723398-164f-4e84-8ce0-2c55bd25bd28", "title": "Guardian", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "children": [{"id": "05f7f80b-0f85-4cf8-9749-8ebc36e695df", "title": "Overview", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "path": "/home"}, {"id": "2ca8bde9-7ef6-47dd-8c02-838a0bd1dfa8", "title": "Cases", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "path": "/cases"}, {"id": "c6ee4bec-7a71-4f75-b103-6bd6741dfed5", "title": "Logs", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "path": "/logs/sessions"}, {"id": "59d86a65-37a0-4594-86cf-f936c998a2bb", "title": "Users", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "path": "/users"}, {"id": "86363a8c-c387-4a2d-9e45-9016e198f95d", "title": "Reporting", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg", "slug": "guardian", "path": "/reports"}]}, {"id": "04f23154-5765-4b4e-8133-f47cd31cf294", "title": "Identity", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/identity.svg", "slug": "identity", "path": "/"}, {"id": "a337022b-f8d4-4bab-b26c-94217dd0265a", "title": "Builder", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/developer-app.svg", "slug": "developer", "children": [{"id": "7d2e7e5e-08c0-4e56-9135-750a24a31d51", "title": "OAuth Clients", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/oauth-clients.svg", "slug": "oauth-clients", "path": "/"}, {"id": "6e67c133-cf11-4e38-983a-5ccef9fad454", "title": "Integrations", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "integrations", "feature": "INTEGRATIONS_app_access", "path": "/"}, {"id": "1ad1d8fe-871b-47ea-bbb6-b71bfe393382", "title": "Automation Designer", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg", "slug": "automation-designer", "path": "/"}]}, {"id": "f196cb8a-57db-45f5-a210-d432fb1f0c10", "title": "Workspace Designer", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer.svg", "slug": "workspace-designer", "children": [{"id": "2ab6f758-846c-46f7-bcfc-2f632ea33b38", "title": "Cards", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_cards.svg", "slug": "card-builder", "path": "/"}, {"id": "441394cd-d539-4026-baca-a0c0442721fd", "title": "Panels", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_dashboard.svg", "slug": "workspace-designer", "path": "/"}, {"id": "e40d15fe-18e0-420f-8849-6d4b55394202", "title": "<PERSON><PERSON>", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_dashboard.svg", "slug": "workspace-designer", "path": "/canvas"}]}, {"id": "51cf9dcb-29a4-41ca-b19c-cb699818be5e", "title": "Interaction Analytics", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/interaction-analytics.svg", "slug": "speech-analytics", "children": [{"id": "00cfb88d-363e-45fc-8686-137a1b536653", "title": "Dashboards", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/interaction-analytics.svg", "slug": "speech-analytics", "path": "/dashboards"}, {"id": "63a622c9-afc5-442b-91d2-702639a6d5c3", "title": "Journey Map", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/interaction-analytics.svg", "slug": "speech-analytics", "feature": "AI_SPEECH_ANALYTICS_CUSTOMER_JOURNEY", "path": "/journey-map"}, {"id": "6211cd83-eb21-4dd0-916e-54fdb99ad797", "title": "Search", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/interaction-analytics.svg", "slug": "speech-analytics", "path": "/search"}]}, {"id": "695e32c3-db76-4823-85f8-41603fd2339f", "title": "AI Trainer", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/ai-trainer.svg", "slug": "aitrainer", "path": "/"}, {"id": "ea6fdbaa-da8e-4621-94db-dcc946df9c95", "title": "Live", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/live.svg", "slug": "live", "path": "/"}, {"id": "8fbc9347-d25b-4c1a-be0f-8dcd578f3aac", "title": "Explore", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/explore.svg", "slug": "explore", "path": "/"}, {"id": "f0d7bcbc-2a20-4aad-9271-d910e445f893", "title": "Academy", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/talkdesk-academy.svg", "slug": "workspace-talkdesk-academy", "path": "/"}, {"id": "57c5be3c-ae0a-459b-a780-9351017b93cf", "title": "Unified Recording", "type": "droplet", "slug": "ur-app", "path": "/", "hidden": true}, {"id": "6b97e202-b6d2-4a11-8d60-8873783f0015", "title": "AppConnect", "type": "droplet", "slug": "appconnect", "path": "/", "hidden": true}, {"id": "0f17cb01-4836-42de-8e07-c343e45b6610", "title": "Classic Admin Bulk", "type": "droplet", "slug": "classic-admin-bulk", "feature": "ATLAS_classic_admin_access", "path": "/admin/users/actions", "hidden": true}, {"id": "ad547e65-e508-4381-9fe1-072c59390678", "title": "Voice ACW Settings Bulk Action", "type": "droplet", "slug": "voice-acw-settings-bulk-action", "feature": "CONVERSATION_VOICE_show_bulk_actions_for_wrap_up", "path": "/", "hidden": true}, {"id": "********-39f0-49bc-b393-b01305c7c73e", "title": "Admin", "type": "submenu", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "children": [{"id": "286d7368-b4f6-4356-9275-f6d96c935cc3", "title": "Account", "type": "submenu", "children": [{"id": "250a4c98-7caf-41b9-855e-9aa450714924", "title": "Customization", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "workspace-settings", "path": "/account"}, {"id": "fd091e7b-156e-4a5b-801c-962f98136bbc", "title": "Preferences", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "classic-admin", "feature": "ATLAS_classic_admin_access", "path": "/admin/preferences"}, {"id": "c6100c76-dfab-4152-9224-a6780a943fd6", "title": "Holiday Hours", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "classic-admin", "feature": "ATLAS_classic_admin_access", "path": "/admin/holidayhours"}, {"id": "7a2c397f-0fc1-4495-9b43-654f8398bdbc", "title": "Security settings", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "login-settings", "feature": "EDGE_enable_session_inactivity_timeout", "path": "/"}, {"id": "48dcf666-d3b8-4428-aba3-9508c32266df", "title": "Emergency Calls", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "classic-admin", "feature": "ATLAS_classic_admin_access", "path": "/admin/emergency-calls"}]}, {"id": "d47568b8-529e-4847-862d-5f28cd3c8135", "title": "People", "type": "submenu", "children": [{"id": "714386d3-5333-4f7d-bf8a-8617855d553b", "title": "Users", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "users-settings", "path": "/"}, {"id": "bd5721ab-ae2e-4b3a-9358-dfc3dff43b18", "title": "Teams", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "teams", "path": "/"}, {"id": "53950c23-2054-4bf0-9155-6766<PERSON><PERSON>a5d", "title": "Roles and Permissions", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "roles-permissions", "path": "/"}]}, {"id": "6a8daab8-682f-4c94-aab2-6a0595448c2b", "title": "Service Settings", "type": "submenu", "children": [{"id": "f5fb6ede-45af-47a2-b684-c97da1711f03", "title": "Numbers", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "classic-admin", "feature": "UC_NUMBERS_main_access", "path": "/admin/numbers"}, {"id": "a317a802-d582-4f74-b014-b649bd0a4f63", "title": "Channels", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "channels", "path": "/"}, {"id": "e19acef4-a245-4afe-b180-b87d824e7a5c", "title": "Dispositions", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "classic-admin", "feature": "ATLAS_classic_admin_access", "path": "/admin/dispositions"}, {"id": "722be226-c940-4bae-8125-820c3d3ee85e", "title": "Storage and Retention", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "recordings", "path": "/storage-settings"}, {"id": "21011f94-15c8-4852-96b7-796f749e300a", "title": "AI Launchpad", "type": "droplet", "icon": "https://qa-cdn-talkdesk.talkdeskdev.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "ai-launchpad", "path": "/"}]}, {"id": "89c3eee2-1f39-4bc1-b62f-3388c553bd9b", "title": "Routing", "type": "submenu", "children": [{"id": "c10c3486-4a9e-483f-b6a1-10e8347a78c8", "title": "Queues", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "queues-ui", "path": "/admin/queues"}, {"id": "d2fec0c9-1469-40a0-9af0-6297c3c16460", "title": "Attributes", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg", "slug": "classic-admin", "path": "/admin/attributes"}, {"id": "c25425de-e2ef-4f8f-a810-28a69ce02831", "title": "Continuity Settings", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "continuity-settings", "path": "/"}]}, {"id": "c532ba84-16d1-4c8f-8a00-aece925cb767", "title": "Billing", "type": "submenu", "children": [{"id": "ecb96caa-568a-4499-94cc-906a270e1393", "title": "Overview", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "billing", "path": "/overview"}, {"id": "fec76379-ecf7-496d-b0a8-ce3462ddacdc", "title": "Subscription", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "provisioning", "feature": "PROV_billing_licenses_ui_workspace_enabled", "path": "/subscription"}, {"id": "ef92eac0-bb4c-4ff4-954c-6bce3e79a7f8", "title": "Usage", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "billing", "path": "/usage"}, {"id": "72503287-93b2-421a-93ca-9ca9d018dc79", "title": "Invoices", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "billing", "path": "/invoices"}]}]}, {"id": "f1fce942-f0fa-4df8-a73e-a53781a696d9", "title": "Roles Permissions", "type": "droplet", "slug": "roles-permissions-bulk", "path": "/roles/users/actions", "hidden": true}, {"id": "11973fe8-3611-46d4-8eae-ab0235321250", "title": "Conversation Runtime Settings Bulk", "type": "droplet", "slug": "conversation-runtime-settings-bulk", "path": "/actions", "hidden": true}]}}, {"id": "workspace-secondary", "evaluation": {"children": [{"id": "eae5d6d8-0bdc-4170-9392-f89917c800e9", "title": "Copilot", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg", "slug": "agent-assist-secondary", "path": "/"}, {"id": "63c4a12e-87db-460f-afed-d045d8943679", "title": "Smart Help", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/app_helper.svg", "slug": "contextual-help", "path": "/"}]}}, {"id": "workspace-headless", "evaluation": {"children": [{"id": "557a5147-80a2-48a5-9014-e83c824e8da8", "title": "Live", "type": "droplet", "slug": "live", "path": "/"}, {"id": "034ff186-3097-4ce2-83ac-2dd67a405069", "title": "Live Monitoring", "type": "droplet", "slug": "live-monitoring", "path": "/"}, {"id": "af1f9eb3-7296-47bf-bedf-cba897c8cd5a", "title": "Conversation", "type": "droplet", "slug": "conversation", "path": "/"}, {"id": "c35957be-dfda-4bea-bc72-93fa2933de46", "title": "Studio Editor", "type": "droplet", "slug": "studio-editor", "path": "/"}, {"id": "2b9bff77-0c57-4723-9209-432e80dfe3d4", "title": "Recordings", "type": "droplet", "slug": "recordings", "path": "/"}]}}, {"id": "workspace-user-profile", "evaluation": {"children": [{"id": "eb4f4296-b4a7-4297-9d9c-0fa0e766b997", "title": "Workspace Settings", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "workspace-settings", "path": "/user"}, {"id": "a158d475-cfb4-45cf-9ce7-f7699ccc645b", "title": "Conversations Settings", "type": "droplet", "icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg", "slug": "conversation-settings", "path": "/"}, {"id": "e012c8ff-418c-49a5-9c04-6c7743b34adf", "title": "Security settings", "type": "external", "href": "//po-automationca.talkdeskidca.com/users/settings"}, {"id": "749b6663-495a-466e-ab3b-60cf5e4cff74", "title": "Support", "type": "external", "href": "//support.talkdesk.com"}, {"id": "e6c28181-910f-4320-b943-f80b6c4fd8bd", "title": "Training", "type": "external", "href": "//academy.talkdesk.com"}, {"id": "307d2917-9a3f-4ceb-af5e-d72ff8dae577", "title": "Terms of Service", "type": "external", "href": "//www.talkdesk.com/terms-of-service"}, {"id": "b4eb58c6-a929-4b2e-8957-700d42d2b190", "title": "Privacy Policy", "type": "external", "href": "//www.talkdesk.com/privacy-policy"}, {"id": "************************************", "title": "Talkdesk Classic", "type": "external", "href": "//po-automationca.mytalkdeskca.com?classic"}, {"id": "62bd054e-9896-48b2-b28f-74c5be524742", "title": "Desktop versions", "type": "external", "href": "//po-automationca.mytalkdeskca.com/atlas/download", "metadata": {"id": "download"}}, {"id": "25b003b0-9994-40f4-89b1-b4d61bdaa505", "title": "About Talkdesk", "type": "submenu", "metadata": {"id": "about"}, "children": [{"id": "70e7dde4-8ba1-4b23-8b75-08fb2c6011ba", "title": "Other versions", "type": "external", "href": "//po-automationca.mytalkdeskca.com/atlas/download"}]}]}}, {"id": "workspace-portals", "evaluation": {"children": [{"id": "5790c641-8d83-4f2e-9356-68a3e7b3f21d", "title": "voiceACWSettings", "type": "droplet", "slug": "voice-acw-settings", "path": "/"}, {"id": "987a8179-fd17-4193-8375-4be471b4603c", "title": "Uc Directory", "type": "droplet", "slug": "uc-directory", "path": "/"}, {"id": "02e18ff0-81f7-4131-8200-097ac51a1d33", "title": "Transcription Card", "type": "droplet", "slug": "transcription-card", "path": "/"}, {"id": "727f9b48-1e2c-45df-9e34-33ea5c62f38d", "title": "Snapshot Tab", "type": "droplet", "slug": "snapshot-tab", "path": "/"}, {"id": "9900e777-6cd3-4a8a-8440-0a016c465b66", "title": "Notes Tab", "type": "droplet", "slug": "notes-tab", "path": "/"}, {"id": "2100af03-2f46-4f01-b3e0-3c7151d4349d", "title": "Manager Contact Card Tab", "type": "droplet", "slug": "manager-contact-card-tab", "path": "/"}, {"id": "f55e2070-5eea-417e-aaa3-04b2278b5012", "title": "Identity Conversation Tab", "type": "droplet", "slug": "identity-conversation-tab", "path": "/"}, {"id": "6dcbe9f0-bf2a-4ed3-ba39-bc6607e43132", "title": "Dispositions Tab", "type": "droplet", "slug": "dispositions-tab", "path": "/"}, {"id": "64e6c0ed-a077-4900-b959-a7cdcab45558", "title": "Conversation Voice Channel", "type": "droplet", "slug": "conversation-voice-channel", "path": "/"}, {"id": "775b65ea-69c5-4b53-bd7c-e1ee243dc3ef", "title": "Conversation Whatsapp Channel", "type": "droplet", "slug": "conversation-whatsapp-channel", "path": "/"}, {"id": "62f934d4-76f2-47e7-8b28-b16e1dd92586", "title": "Conversation Sms Channel", "type": "droplet", "slug": "conversation-sms-channel", "path": "/"}, {"id": "31a7ae4f-d8fa-4f3e-bc2b-0933a9169f14", "title": "Conversation Runtime Settings Tab", "type": "droplet", "slug": "conversation-runtime-settings-tab", "path": "/"}, {"id": "062bb130-bea1-4fda-8934-5002f8b1605d", "title": "Conversation Live Chat Channel", "type": "droplet", "slug": "conversation-live-chat-channel", "path": "/"}, {"id": "9aaa9fe8-648d-44f6-a082-33bfbdb4015c", "title": "Conversation Fbm Channel", "type": "droplet", "slug": "conversation-fbm-channel", "path": "/"}, {"id": "cd8338d2-4e01-419d-bdb3-1481b1ef37ec", "title": "Conversation Email Channel", "type": "droplet", "slug": "conversation-email-channel", "path": "/"}, {"id": "664dea09-4767-4805-aec3-819e39d8b46e", "title": "Conversation Digital Connect Whatsapp Channel", "type": "droplet", "slug": "conversation-digital-connect-whatsapp-channel", "path": "/"}, {"id": "82c3c5da-1372-4ef9-8f31-28147a8bf2d4", "title": "Conversation Digital Connect Fax Channel", "type": "droplet", "slug": "conversation-digital-connect-fax-channel", "path": "/"}, {"id": "72ea3287-9aac-439f-b4ee-325a350591ad", "title": "Conversation Digital Connect Channel", "type": "droplet", "slug": "conversation-digital-connect-channel", "path": "/"}, {"id": "35abe786-020f-4139-9cef-b90a711718ca", "title": "Conversation Demo Channel", "type": "droplet", "slug": "conversation-demo-channel", "path": "/"}, {"id": "8cd8179c-5731-4082-99dc-371af726de4e", "title": "Conversation Apple Messages For Business Channel", "type": "droplet", "slug": "conversation-apple-messages-for-business-channel", "path": "/"}, {"id": "8c4315a2-cc6e-4bc4-b9c8-aa52101be4dc", "title": "Contact Creation Portal", "type": "droplet", "slug": "contact-creation-portal", "path": "/"}, {"id": "408aaee1-4a77-4213-b15b-c61549de7a38", "title": "Contact Activities Conversation Tab", "type": "droplet", "slug": "contact-activities-conversation-tab", "path": "/"}, {"id": "59cc8e63-c6e3-40a2-88b1-de5e71237d19", "title": "Contact Activities Portal", "type": "droplet", "slug": "contact-activities-portal", "path": "/"}, {"id": "83fcc2fc-019f-407f-ad56-832bf7fda08f", "title": "Classic Admin Portal", "type": "droplet", "slug": "classic-admin-portal", "path": "/"}, {"id": "02122222-f2d2-4b6e-b919-7373af63d627", "title": "Case Card Tab", "type": "droplet", "slug": "case-card-tab", "path": "/"}, {"id": "9246cc42-9e3d-40e5-9255-5e3690e9360f", "title": "Cards Static Component", "type": "droplet", "slug": "cards-static-component", "path": "/"}, {"id": "b069ac12-10a2-4dbe-8b13-22d03b885606", "title": "Canvas Runtime", "type": "droplet", "slug": "canvas-runtime", "path": "/"}, {"id": "f062e6ab-21a4-4ce4-946a-8369bd62523c", "title": "Conversation Digital Channels", "type": "droplet", "slug": "conversation-digital-channels", "path": "/"}]}}], "_embedded": {"apps": [{"id": "d585635329dd472c98c1f84b1098d4a4", "name": "AI Trainer", "scopes": ["account:read", "graph-users:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "policies:evaluate", "openid", "recordings:read", "proxy:read", "users:read", "user-roles:read", "aitrainer:read", "aitrainer:write", "aitrainer-entities:read", "aitrainer-entities:write", "aitrainer-intents:read", "aitrainer-intents:write", "aitrainer-reports:read", "aitrainer-reports:write", "ai-tiers:read", "acls:read", "ai-settings:read", "conversations:read", "automation-designer:read", "virtual-agents-flows:read", "applied-science-functions:invoke", "aitrainer-generative-ai:read", "aitrainer-model-exports:read", "aitrainer-model-imports:write", "aitrainer-model-imports:read", "aitrainer-generative-ai-fine-tune:read", "aitrainer-generative-ai-fine-tune:write", "ai-generative-function:invoke", "nlp-aa-pipeline:summarize"], "capabilities": ["authorization", "navigation", "toast", "form", "popup", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, "audio", "download", {"name": "protocol", "protocols": ["ai-trainer:open-model"]}, "@atlas/context", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/aistudio-ui/6.41.4/index.html", "slug": "aitrainer", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/ai-trainer.svg"}, "version": "0.1.3", "version_id": "888ec1d1f1ed41219ebef80e8b0e3223"}, {"id": "d492d967e7ba4be8b7bcc151476868e5", "name": "Explore", "scopes": ["account-profile:read", "apps:graphql", "explore-dashboards:read", "explore-dashboards:write", "explore-datasets:read", "explore-reports:read", "explore-reports:write", "openid", "policies:evaluate", "rtm-user:auth", "rtm:subscribe", "rtm-settings:read", "user-notifications:read", "user-notifications:write", "workspace-settings:read", "workspace-settings:write", "graph-users:read"], "capabilities": ["authorization", "download", "navigation", "popup", "modal", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "d-dp"}}, {"name": "protocol", "protocols": ["explore:show"]}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/explore-ui/2.23.0/atlas-entrypoint.html", "slug": "explore", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/explore.svg"}, "version": "0.1.17", "version_id": "148f9d6e1246498ebb4263e8e2aa746b"}, {"id": "7c0928c768c34789864d610ac6d59cac", "name": "Guardian", "scopes": ["openid", "users:read", "account:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "policies:evaluate", "guardian:read", "guardian:write", "guardian-upsell:write", "guardian-users:read", "guardian-call-quality:read", "guardian-cases:read", "graph-users:read", "apps:read", "licenses:read", "identity:read", "guardian-subscriptions:write", "guardian-subscriptions:read"], "capabilities": [{"name": "i18n", "languages": ["pt-PT", "en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": ["guardian-notifications", "guardian-logs:sessions", "guardian:show"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, "authorization", "navigation", "form", "toast", "popup", "download", "clipboard", "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/guardian-ui/latest/index.html", "slug": "guardian", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guardian.svg"}, "version": "0.0.1", "version_id": "7c0928c768c34789864d610ac6d59cac"}, {"id": "62b79f1cae70435bad229d330339b2dd", "name": "Live", "scopes": ["policies:evaluate", "reporting-dashboards:read", "reporting-dashboards:write", "reporting-metrics:read", "reporting-user-statuses:read", "ring-groups:read", "salesforce", "styx-subscriptions:write", "teams:read", "user-ring-groups:read", "users:read", "graph-users:read", "customer-data-store:read", "omnichannel-inbox:read", "omnichannel-inbox:write", "callbar", "contact-details:read", "email-touchpoints:read", "assets:read", "assets:download", "account-custom-status:read", "ccaas-user-status:read", "occupancy-api:read", "campaigns:read", "record-lists:manage", "queues:read", "flow-triggers:read", "user-session-public:end", "context:read"], "capabilities": ["authorization", "clipboard", "fullscreen", "popup", "download", "portal", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "d-dp"}}, {"name": "protocol", "protocols": ["live:show"]}, {"name": "rtm", "events": {"account": ["agent_status_changed"]}}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/reporting-live-dashboards-ui/1.61.0/atlas-entrypoint.html", "slug": "live", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/live.svg"}, "version": "0.0.21", "version_id": "5d36934cc3e441a7b61c10bdea1ad03b"}, {"id": "9a562fbf2ff540408af0ac8b2cfa23a6", "name": "Knowledge Management", "scopes": ["openid", "users:read", "roles:read", "user-roles:read", "user-ring-groups:read", "account:read", "graph-users:read", "guide-analytics:read", "guide-analytics:write", "guide-content:import", "guide-content:search", "guide-content:read", "guide-content:write", "guide-content-draft-version:read", "guide-content-draft-version:write", "guide-content-draft:read", "guide-content-draft:write", "guide-content-metrics:query", "guide-nlqa:read", "guide-nlqa:write", "guide-incidents:read", "guide-incidents:write", "guide-permissions:read", "guide-permissions:write", "guide-labels:read", "upload-jobs:read", "upload-jobs:write", "ai-knowledge:read", "aitrainer:read", "aitrainer-intents:read", "aitrainer-entities:read", "ai-settings:read", "nlp-knowledge:read", "nlp-knowledge:write", "ring-groups:read", "templates:read", "templates:write", "templates:install", "teams:read", "ai-platform-feedback:send"], "capabilities": ["@atlas/product-analytics", "authorization", "clipboard", "download", "navigation", "toast", "form", "popup", "portal", {"name": "protocol", "protocols": ["navigate-to-knowledge-management"]}, {"name": "i18n", "languages": ["en-US", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/guide-ui/5.0.0/index.html", "slug": "guide", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/guide.svg"}, "version": "0.0.11", "version_id": "8cfadb9440b245ea8402c7c06530f650"}, {"id": "f8eb86c8379f49249c8e041395005486", "name": "Home", "scopes": ["openid", "policies:evaluate", "teams:read", "live-queries:read", "live-subscriptions:write", "account-custom-status:read", "guardian-sessions:read", "graph-users:read", "qm:read", "account:read", "cfm:read", "data-queries:read", "salesforce", "ring-groups:read", "apps:graphql", "interaction-analytics:read", "ai-tiers:read", "ai-leads:write", "account-billing:read", "express-onboarding:read", "express-onboarding:write", "numbers-ng:read", "numbers-ng:write", "numbers-ng-details:read", "roles:read", "user-ring-groups:write", "administration-users:write", "user-disposition-settings:write", "provider-abstraction-service:read", "provider-abstraction-service:write", "billing-details:read", "account-status:read", "express-contracts-actions:write", "account-organization:read", "express-settings:read", "support-dashboard-emergency-settings:write", "express-zendesk-ticket-forms:read", "ai-settings-pipelines:write", "account-profile:read", "identity-subscriptions:write", "identity:write", "email-touchpoints:write", "nlp-knowledge:write", "email-touchpoints:read"], "capabilities": ["authorization", "clipboard", "navigation", "toast", "popup", "@atlas/features", "portal", "@atlas/product-analytics", "@atlas/messages", {"name": "i18n", "languages": ["en-US", "fr-CA", "pt-PT", "es-ES", "fr-FR", "it-IT", "de-DE", "pt-BR"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": []}, "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "growth"}}, "fullscreen", "download"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/home-app/latest/index.html", "slug": "home", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/home.svg"}, "version": "0.0.0", "version_id": "f8eb86c8379f49249c8e041395005486"}, {"id": "052233959eea4b98925da16f2544fa72", "name": "Channels", "scopes": ["openid", "account:read", "flows:read", "provider-abstraction-service:write", "provider-abstraction-service:read", "email-touchpoints:read", "email-touchpoints:write", "ring-groups:read", "queues:read", "numbers:read", "flow-definitions:read", "users:read", "policies:evaluate", "guide-content:write", "guide-content:read", "guide-content-draft:write", "guide-content-draft:read", "ai-knowledge:read", "graph-users:read", "digital-connect:read", "digital-connect:write", "integrations:read", "actions:read", "channel-capacity:read", "channel-capacity:write", "notifications:read", "omnichannel-inbox:read", "assets:delete", "assets:write", "assets:read", "assets:download", "efax-bridge:access", "channel-configs:read", "channel-configs:write", "reopen-settings:read", "reopen-settings:write", "roles:read", "priority-configuration:read", "priority-configuration:write", "channels-placeholders:read", "channels-signatures:write", "channels-signatures:read", "account-phones:read", "phone-details:read", "conversation-timeout-rule:read", "conversation-timeout-rule:write", "flow-triggers:read", "flow-triggers:write", "email-configurations:read", "email-configurations:write", "attachments:request", "attachments:write", "attachments:download", "cfm:read", "voice-platform-settings:read", "voice-platform-settings:write", "channels-sla-policies:write", "channels-sla-policies:read", "infobip-whatsapp-template:read", "infobip-whatsapp-template:write", "numbers-ng-details:read", "whatsapp-business-platform:read", "tts-languages:read", "tts-languages:write", "conversation-runtime-user-settings:read", "conversation-runtime-user-settings:write", "multiple-interaction-settings:read", "multiple-interaction-settings:write"], "capabilities": ["authorization", "form", "popup", "toast", "navigation", "clipboard", "@atlas/product-analytics", "@atlas/menus", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": ["system:td+oauth2", "re-authentication-notification-protocol"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "dce-kunpeng"}}, {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/channels-app/0.8.0/index.html", "slug": "channels", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.0", "version_id": "052233959eea4b98925da16f2544fa72"}, {"id": "bcefed530a804a149d5023b417c5bbe8", "name": "<PERSON><PERSON><PERSON>", "scopes": ["campaigns:read", "campaigns:write", "record-lists:read", "record-lists:write", "numbers:read", "phone-details:read", "user-ring-groups:read", "users:read", "do-not-call-lists:read", "do-not-call-lists:write", "campaign-do-not-call-lists:read", "campaign-do-not-call-lists:write", "phone-info:read", "graph-users:read", "ring-groups:read", "record-lists:manage", "do-not-call-lists:manage", "rtm-settings:read", "teams:read", "flows:read", "policies:evaluate", "resources-limitation:read", "salesforce-noetica:read", "salesforce-record-lists-sync:read", "salesforce-record-lists-sync:write"], "capabilities": ["@atlas/product-analytics", "authorization", "navigation", "toast", "popup", "form", "download", {"name": "protocol", "protocols": ["download-validation-list", "download-current-list", "download-current-dnc-list", "download-record-list-success"]}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "continuouseng-deeting"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/campaign-manager-ui/latest/index.html", "slug": "outbound-dialer", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg"}, "version": "0.0.10", "version_id": "eccecfe3afed4a50aeb68a73d3b9ede8"}, {"id": "e1d61b6c333646bfb189bd108fa57e7f", "name": "Interaction Analytics", "scopes": ["speech-analytics:read", "speech-analytics:write", "interaction-analytics:read", "interaction-analytics:write", "ring-groups:read", "users:read", "recordings:read", "graph-users:read", "policies:evaluate", "aitrainer:read", "aitrainer-intents:read", "aitrainer-reports:write", "ai-leads:read", "ai-leads:write", "conversations:read", "ai-tiers:read", "ai-settings:read", "ai-settings-pipelines:write", "virtual-agents-flows:read", "apps:read", "ai-platform-feedback:send"], "capabilities": ["authorization", "download", "navigation", "toast", "portal", "popup", "@atlas/product-analytics", "@atlas/context", {"name": "i18n", "languages": ["en-US", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": ["interaction-analytics:view-dashboard"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, {"name": "rtm", "events": {"user": ["user_notification_created"]}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/ai-speech-analytics/2.0.2/app/index.html", "slug": "speech-analytics", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/interaction-analytics.svg"}, "version": "0.0.16", "version_id": "f4d70640dc274066a4756da4086822e4"}, {"id": "841f90dba0b84828a7e57e897b6637ec", "name": "Voicemails", "scopes": ["openid", "contacts:read", "graph-users:read", "users:read", "numbers:read", "ring-groups:read", "recordings:read", "voicemails:read", "voicemails:write", "policies:evaluate", "contact-details:read", "account:read", "callbar", "recordings:delete", "queues:read"], "capabilities": ["authorization", "form", "navigation", "portal", "toast", {"name": "protocol", "protocols": ["voicemails:show", "voicemails:show-details"]}, "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}, "@atlas/features", "@atlas/logger", "download"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/voicemails-app/3.2.2/index.html", "slug": "voicemails", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/voicemails.svg"}, "version": "1.0.15", "version_id": "0836c0fd05924760b123a46ae03bad0e"}, {"id": "972282cb0a384371b466a67ad38bd94f", "name": "Activities", "scopes": ["openid", "users:read", "graph-users:read", "numbers:read", "policies:evaluate", "ring-groups:read", "recordings:read", "contacts:read", "styx-subscriptions:write", "contact-details:read", "activity-subscriptions:write", "account:read", "interaction-contacts:read", "apps:graphql", "qm:read", "customer-data-store:read", "interactions:read", "email-touchpoints:read", "contacts-activities:read", "provider-abstraction-service:read", "omnichannel-inbox:write", "digital-connect:read", "recordings:delete", "queues:read", "cases:read"], "capabilities": ["authorization", "form", "navigation", "clipboard", "portal", "popup", "toast", {"name": "protocol", "protocols": ["activity:show"]}, "download", "@atlas/logger", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/activity-app/3.4.3/index.html", "slug": "activity", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/activity.svg"}, "version": "0.0.18", "version_id": "d1d408334f194e7497ee89b52fb15404"}, {"id": "fb3738c057554aa2b187052cefe9054b", "name": "Contacts", "scopes": ["openid", "contact-details:write", "contact-details:read", "account:read", "contacts-activities:read", "numbers:read", "graph-users:read", "policies:evaluate", "recordings:read", "directory-personas:read", "account-favorites:read", "account-custom-fields:read", "presence-hub:read", "callbar", "apps:graphql", "cti:messaging", "directory-providers:read", "workspace-contacts:read", "activity-notes:write", "contact-gdpr:write", "interaction-contacts:read"], "capabilities": ["@atlas/context", "authorization", "navigation", "popup", "toast", "form", "portal", "clipboard", "download", {"name": "i18n", "languages": ["en-US", "fr-CA", "pt-PT"], "fallbackResourcesTemplateUrl": "assets/locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": ["contacts:show", "contacts:show-details", {"name": "Search contacts", "key": "contacts:search", "description": "Searches for a contact, and shows the results", "icon": "person_search", "params": {"keyword": {"name": "Contact", "type": "string", "description": "The search term", "placeholder": "<PERSON><PERSON>"}}}]}, "@atlas/product-analytics", "@atlas/logger", "@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/contacts-app/4.1.6/contacts-app/index.html", "slug": "contacts", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "0.2.21", "version_id": "a2649a7bd2034ebaa893fd7d3a45e819"}, {"id": "8c23d8b7406d4f28accc423e4921a0f1", "name": "Conversations", "scopes": ["interactions:submit-wrap-up", "interactions:switch-contact", "interactions:control-recordings", "interactions-stage:read", "device-routing:read", "device-routing:write", "device-routing-nailup:write", "dispositions:read", "integrations:read", "interaction-quality-feedback:write", "interaction-quality-settings:read", "interaction-quality-settings:write", "openid", "ring-groups:read", "rtm-publish:write", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "sentiment-settings:read", "presence-device-session:write", "presence-account:read", "interactions:create", "interactions:transfer", "account-custom-status:read", "account:read", "contacts:read", "numbers:read", "megazord:read", "pas-provider-client-credentials:generate", "contact-details:read", "graph-users:read", "user-heartbeat:write", "interactions:hold", "interactions:disconnect", "callbar", "callbar-analytics:write", "callbar-settings:read", "omnichannel-inbox:read", "omnichannel-inbox:write", "outbound-numbers:read", "call-entry:read", "users:read", "provider-abstraction-service:read", "provider-abstraction-service:write", "emails:write", "customer-data-store:read", "customer-data-store:write", "email-threads:read", "email-threads:write", "email-touchpoints:read", "emergency-device-locations:read", "emails-job:read", "policies:evaluate", "assets:read", "assets:write", "assets:download", "occupancy-api:write", "context:read", "context:write", "cases:read", "guide-content:read", "guide-content-draft:read", "ai-knowledge:read", "ccaas-user-status:read", "schedule-callbacks:read", "occupancy-api:read", "interactions:swap", "digital-connect:read", "digital-connect:write", "account-uc-status:read", "po-interactions:read", "attachments:delete", "attachments:download", "attachments:read", "attachments:request", "attachments:write", "channel-configs:read", "cti:messaging", "integrations-zendesk-tickets:read", "directory-personas:read", "directory-providers:read", "presence-hub:read", "channels-signatures:read", "credentials-twilio:read", "phone-details:read", "dce-tracking:read", "dce-tracking:write", "digital-messenger:read", "digital-messenger:write", "interaction-metrics:read", "workspace-contacts:read", "queues:read", "ai-generative-function:invoke", "infobip-meeting-room:write", "flow-triggers:read", "infobip-whatsapp-template:read", "ai-settings-pipelines:read", "ai-settings-configs:read", "numbers-ng-details:read", "priority-configuration:read", "conversation-runtime-user-settings:read", "users-wrap-up-settings:read"], "capabilities": ["authorization", "audio", "form", "portal", "popup", "download", "clipboard", "video", "fullscreen", "@atlas/context", "@atlas/product-analytics", "@atlas/features", "@atlas/audio-device-management", "@atlas/notification", "@atlas/call-quality", "@atlas/vdi", "@atlas/menus", {"name": "@atlas/logger", "provider": "dynatrace"}, {"name": "background", "enableRequestToForeground": true}, {"name": "protocol", "protocols": ["trigger-voice-agent-conversation", {"name": "Call", "key": "trigger-voice-conversation", "description": "Make a phone call to a number using the conversation app", "icon": "call", "params": {"target": {"name": "Phone number", "type": "phone-number", "description": "The phone number to call", "placeholder": "****** 555 555"}}}, "trigger-email-conversation", "system:tel", "system:td+tel", "select-active-conversation", "preview-conversation", "trigger-sms-conversation", "conversations:show"]}, {"name": "lifecycle", "nonPerformant": true}, "toast", "badge", "@atlas/agent-status", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "../assets/locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-core", "metrics": [{"name": "conversation.app_startup_observability"}, {"name": "conversation.channel_initialization", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.channel_init_count", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.channel_init_error", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.sdk_initialization", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.sdk_init_count", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.sdk_init_error", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.command_processed", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.command_processed_count", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.command_processed_error", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "conversation.render_processed", "dimension1": ["demo", "voice", "email", "sms", "live-chat", "fbm", "whatsapp", "digital-connect", "digital-connect-fax", "digital-connect-whatsapp", "apple-messages-for-business"]}, {"name": "azores.missed-acw-event"}, {"name": "azores.auto-answer-failed", "dimension1": ["TRIGGER_MSG_INBOUND_CALL_ENDED", "TRIGGER_CALL_FINISH", "TRANSITION_TO_READY"]}, {"name": "azores.voice-answer-failed", "dimension1": ["AUTO_ANSWER", "MANUAL_ANSWER"]}, {"name": "azores.voice-outbound-call-initiated"}, {"name": "azores.voice-outbound-call-attempt"}, {"name": "azores.voice-provider-warning", "dimension1": ["constant-audio-input-level", "high-jitter", "high-packet-loss", "high-packets-lost-fraction", "high-rtt", "ice-connectivity-lost", "low-bytes-received", "low-bytes-sent", "low-mos"]}]}}, {"name": "rtm", "events": {"account": ["agent_updated", "account_updated", "agent_status_changed", "call_initiated", "call_answered", "call_finished", "outbound_call_answered", "interaction_extension_procedure_started", "call_agents_dialed", "agent_call_initiated", "agent_call_canceled", "agent_call_answered", "agent_call_finished", "call_agent_callback_answered", "call_agents_dial_ended", "call_transfer_answered", "call_transfer_failed", "call_transfer_merged", "call_transfer_switched", "call_warm_transfer_finished", "call_transfer_interrupted", "call_merged", "agent_deleted", "telephone_created", "telephone_updated", "telephone_deleted", "external_favorites_updated", "call_external_answered", "call_external_finished", "call_external_missed", "call_external_initiated", "waiting_call_enqueued", "waiting_call_dequeued", "contact_exported", "call_switching_failed", "call_switching_succeeded", "action_dispatched", "outgoing_call_via_phone_finished", "twilio_account_switched", "update_account_device_routing", "account_survival_mode_update"], "user": ["user_swap_interaction", "digital_interaction_assigned", "digital_interaction_offered", "digital_interaction_finished_batch", "digital_interaction_unassigned", "digital_interaction_acw_started", "digital_interaction_finished", "digital_interaction_resumed", "missed_calls_auto_away", "user_status_change_forced", "agent_logout_forced", "call_leg_ringing", "call_park_start_success", "call_park_add_participant_success", "call_park_add_participant_failure", "call_park_switch_participant_success", "call_park_remove_participant_success", "call_park_end_success", "call_park_abort", "call_park_participant_leave", "call_recording_started", "call_recording_paused", "update_user_device_routing", "relate_to_options_created", "after_call_work_started", "after_call_work_skipped", "after_call_work_finished", "conversations_session_override", "digital_message_added", "user_update_contact", "call_transfer_requested", "conference_owner_changed", "digital_contact_typing", "copilot_copy_summary_to_notes", "copilot_disposition_selection"]}}, "@atlas/session-recorder", "@atlas/modules", "navigation"], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/index.html", "slug": "conversation", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/conversation.svg"}, "version": "0.0.2", "version_id": "8c23d8b7406d4f28accc423e4921a0f1"}, {"id": "9ad69eff0cd341ba8c4392a8af735063", "name": "Workspace Designer", "scopes": ["actions:read", "cards:read", "cards:write", "connection-action-executions:write", "openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "workspace-settings:read", "workspace-settings:write", "automation-designer:read", "automation-designer:write", "contacts-activities:read", "numbers:read", "recordings:read", "contact-details:read", "graph-users:read", "teams:read", "contacts:read"], "capabilities": ["authorization", "clipboard", "navigation", "toast", "popup", "portal", "form", "video", "audio", "fullscreen", "download", "@atlas/context", "@atlas/features", "@atlas/session-recorder", "@atlas/product-analytics", "@atlas/logger", "@atlas/menus", {"name": "protocol", "protocols": ["voicemails:show-details"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "builder-integrations-platform"}}, {"name": "rtm", "events": {"account": ["call_answered", "call_finished", "call_initiated", "call_external_answered", "call_external_finished", "call_external_initiated", "agent_status_changed", "outbound_call_answered"], "user": ["digital_interaction_assigned"]}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/workspace-designer/app/0.1.14/index.html", "slug": "workspace-designer", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_dashboard.svg"}, "version": "0.0.0", "version_id": "9ad69eff0cd341ba8c4392a8af735063"}, {"id": "f296eac270a347799ff87d7ae31cad1b", "name": "Identity", "scopes": ["openid", "users:read", "account:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "policies:evaluate", "identity-upsell:write", "guardian-users:read", "guardian-call:read", "apps:read", "licenses:read", "identity:read", "identity:write", "voicebiometrics:read", "graph-users:read", "identity-subscriptions:read", "identity-subscriptions:write", "identity-activity:read"], "capabilities": [{"name": "i18n", "languages": ["en-US", "fr-CA", "fr-FR", "es-ES", "pt-PT", "it-IT", "de-DE", "pt-BR"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, "authorization", "navigation", "form", "toast", "popup", "@atlas/product-analytics", "fullscreen", "download"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/identity-ui/1.22.0/index.html", "slug": "identity", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/identity.svg"}, "version": "0.0.15", "version_id": "c57edc4b31e542aa85e5187abc263ac1"}, {"id": "abcdd4c34d72491e9b9939eabeef6d97", "name": "OAuth Clients", "scopes": ["openid", "scopes:read", "developer-clients:read", "developer-clients:write", "client-certificates:read", "client-certificates:write"], "capabilities": ["clipboard", "authorization", "download", "lifecycle", "navigation", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace", "snippetUrl": "https://js-cdn.dynatrace.com/jstag/188d956ec0f/bf18185ugq/95f0379d3db5cb21_complete.js"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/developer-portal-frontend/latest/index.html", "slug": "oauth-clients", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/oauth-clients.svg"}, "version": "0.0.10", "version_id": "646277d5392f4991a385ebc53cd11c24"}, {"id": "5aa9b1f9718347f181c5b642a5d663ba", "name": "AI Launchpad", "scopes": ["openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "workspace-settings:read", "workspace-settings:write", "ai-tiers:read", "ai-settings-capabilities:read", "ring-groups:read", "ai-settings-pipelines:read", "ai-settings-pipelines:write", "ai-settings:read", "ai-settings:write", "graph-users:read", "virtual-agents-flows:read", "templates:read", "templates:install", "templates:write", "teams:read"], "capabilities": ["authorization", "navigation", "toast", "popup", {"name": "i18n", "languages": ["en-US", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, "form", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, {"name": "protocol", "protocols": ["navigate-to-launchpad", "ai-launchpad:listConnectors"]}, {"name": "rtm", "events": {"user": ["user_notification_created"]}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/ai-launchpad-fe/3.23.0/index.html", "slug": "ai-launchpad", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/app_ai_launchpad.svg"}, "version": "0.0.19", "version_id": "f256e87f6da647a1ade501385f8d34c4"}, {"id": "36309b60c7df448fae14809ed47959e0", "name": "Audio Prompts", "scopes": ["openid", "contacts:read", "contact-details:read", "account:read", "users:read", "graph-users:read", "notifications:read", "assets:read", "assets:write", "assets:download", "assets:delete", "flows:read", "flow-resources:read", "prompts:delete", "prompts:read", "prompts:write", "prompts:download", "policies:evaluate", "queues:read"], "capabilities": ["authorization", "navigation", "popup", "toast", "clipboard", "download", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "orchestration-platform", "metrics": [{"name": "prompt-page-load-time"}]}}, "@atlas/features"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/audio-management-ui/0.7.7/index.html", "slug": "audio-prompts", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg"}, "version": "0.1.0", "version_id": "36309b60c7df448fae14809ed47959e0"}, {"id": "5d03ceba11ab4c8580a18858203413b6", "name": "Academy", "scopes": ["apps:graphql", "openid", "policies:evaluate", "rtm-settings:read", "rtm-user:auth", "user-notifications:read", "user-notifications:write", "account-profile:read", "workspace-settings:read", "workspace-settings:write"], "capabilities": ["authorization", "popup", "embedded", "form"], "render": "canvas", "src": "https://talkdesk-academy-2.workramp.io/trainings", "slug": "workspace-talkdesk-academy", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/talkdesk-academy.svg"}, "version": "0.0.0", "version_id": "5d03ceba11ab4c8580a18858203413b6"}, {"id": "b3b20bf4e726431fb8cb8729d92a6d9c", "name": "Recordings app", "scopes": ["openid", "recordings-share:read", "account-storages:read", "account-storages:write", "numbers:read", "storage-retentions:read", "storage-retentions:write", "graph-users:read", "policies:evaluate", "ai-settings-capabilities:read"], "capabilities": ["authorization", "navigation", "form", "toast", "clipboard", "popup", "@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "wfo-echo"}}, {"name": "@atlas/logger", "provider": "dynatrace"}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/recordings-app/1.19.4/index.html", "slug": "recordings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.0", "version_id": "b3b20bf4e726431fb8cb8729d92a6d9c"}, {"id": "6d3f5bafa2fb4ceaaf5a441d197eca64", "name": "Continuity Settings", "scopes": ["openid", "continuity-settings:read", "continuity-settings:write", "continuity-settings-activation:read", "continuity-settings-activation:write", "policies:evaluate", "account-profile:read"], "capabilities": ["authorization", "toast", "popup", "download", "@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ccp-voiceplatform-va-pangu"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/continuity-settings/latest/index.html", "slug": "continuity-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.0", "version_id": "6d3f5bafa2fb4ceaaf5a441d197eca64"}, {"id": "391f4aafca4942e98ddb9b42ec339a5c", "name": "Billing", "scopes": ["openid", "policies:evaluate", "graph-users:read", "billing-contacts:read", "billing-contacts:write", "account-usage-summary:read", "account-buckets:read", "account-usage-products:read", "account-billing:read", "account-billing:manage", "account-profile:read", "billing-auto-recharge:write", "billing-auto-recharge:read", "billing-details:read", "billing-payment:manage", "invoices:read", "contracts:read", "contracts:write", "licenses:read", "express-contracts-actions:write"], "capabilities": ["authorization", "navigation", "toast", "popup", {"name": "protocol", "protocols": []}, "download", "form", "@atlas/features", "@atlas/product-analytics", "@atlas/session-recorder", {"name": "@atlas/logger", "provider": "dynatrace"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "bt"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/billing-dashboard-ui/1.47.7/index.html", "slug": "billing", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.6.3", "version_id": "f0a67b60231246aaacac33d8f952ad23"}, {"id": "9ec6c6b2237b4f2da7be1ed4b1781a56", "name": "Queues", "scopes": ["openid", "queues:write", "queues:read", "graph-users:read", "roles:read", "policies:evaluate", "prompts:download", "prompts:read"], "capabilities": ["authorization", "navigation", "toast", "popup", "@atlas/features", "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/queues-ui/1.4.0/index.html", "slug": "queues-ui", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg"}, "version": "0.0.13", "version_id": "ebcfd50b54d743a6b04813b02d69d12c"}, {"id": "8fdec57596dc424e89da2a63be172403", "name": "Roles and Permissions", "scopes": ["roles:read", "roles:write", "administration-permissions:read", "administration-permissions:write", "administration-roles:read", "administration-products:read", "policies:evaluate"], "capabilities": ["authorization", "toast", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "iam-authorization-team"}}, "@atlas/product-analytics", {"name": "protocol", "protocols": []}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/roles-ui/1.41.5/atlas/atlas-entrypoint.html", "slug": "roles-permissions", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.14", "version_id": "f6666a48dbd6446f8df68125f81d487e"}, {"id": "78141bfa3f9f4196a468d18cd9d79082", "name": "Provisioning", "scopes": ["openid", "policies:evaluate", "licenses:read", "license-orders:write", "license-orders:read", "policies:evaluate", "seats:read", "seats:write", "roles:read", "account-profile:read"], "capabilities": ["authorization", "navigation", "toast", "popup", {"name": "protocol", "protocols": ["provisioning:show-subscription"]}, {"name": "@atlas/logger", "provider": "dynatrace"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "bt"}}, "form", "@atlas/features", "@atlas/product-analytics", "@atlas/session-recorder"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/billing-licenses-ui/1.48.1/index.html", "slug": "provisioning", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.2.0", "version_id": "ece0f038e4cf430fa4d77239706b8f2a"}, {"id": "c57ee231612f457dbcfd9fd77c3a4897", "name": "Roles Permissions Bulk", "scopes": ["roles:read", "roles:write", "administration-permissions:read", "administration-permissions:write", "administration-roles:read", "administration-products:read", "policies:evaluate", "role-users:write"], "capabilities": ["authorization", "toast", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "iam-authorization-team"}}, "@atlas/product-analytics", {"name": "protocol", "protocols": ["users-role-bulk-action:show"]}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/roles-ui/1.41.5/atlas/atlas-entrypoint.html", "slug": "roles-permissions-bulk", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.5", "version_id": "3c6bc67ac9354414abb70d66f10bd016"}, {"id": "3a21295d1e5c48258b737bfa4ef7eb5b", "name": "<PERSON><PERSON>", "scopes": ["authentication-settings:read", "authentication-settings:write", "password-policies:read", "password-policies:write"], "capabilities": ["authorization", "navigation", "toast", "popup", {"name": "i18n", "languages": ["en-US", "pt-PT"], "resourcesTemplateUrl": "https://prd-cdn-talkdesk.talkdesk.com/login-settings-app/latest/i18n/resources/{{lng}}/{{ns}}.json", "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, "@atlas/features"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/login-settings-app/3.0.0/index.html", "slug": "login-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.3", "version_id": "c8a634d9c7e843dba37bc5c0a81d5f1c"}, {"id": "bf674055bf5e4bc4977c38815f53c6a2", "name": "Conversation Runtime Settings Bulk", "scopes": ["openid", "users:read", "conversation-runtime-user-settings:read", "conversation-runtime-user-settings:write"], "capabilities": ["authorization", "toast", "messaging", "navigation", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": ["users-conversation-settings-bulk-action:show"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-core"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-runtime-settings/0.5.0/index.html", "slug": "conversation-runtime-settings-bulk", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.2", "version_id": "0ccc4090fc0b4fe1875b033e718b5cb9"}, {"id": "2dfabcccbcf04c4fb68c43753f0b67f6", "name": "Integrations", "scopes": ["openid", "account:read", "users:read", "actions:read", "actions:write", "automations:read", "automations:write", "connection-action-executions:write", "integrations:read", "integrations:write", "integrations:sync", "account-profile:read", "policies:evaluate", "agent-assist-automations:read", "agent-assist-automations:write", "agent-assist-recommendations:read", "agent-assist-recommendations:write", "actions-executions:read", "actions-executions-details:read", "graph-users:read", "queues:read", "ring-groups:read", "rtm-topics:read", "rtm-publish:write", "rtm-user:auth", "rtm:subscribe", "rtm-settings:read", "callbar", "cti:messaging", "classic-admin:write"], "capabilities": ["messaging", "authorization", "toast", "download", "@atlas/context", "@atlas/features", "@atlas/session-recorder", "@atlas/product-analytics", "@atlas/menus", "navigation", "portal", "popup", "form", {"name": "protocol", "protocols": []}, "@atlas/logger", "clipboard"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/integrations-app/1.8.1/index.html", "slug": "integrations", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.1", "version_id": "e4940b81ad404553968603091ae2f8e7"}, {"id": "3b0f00d0a112419cba673372cd9d76cf", "name": "Teams", "scopes": ["account:read", "teams:read", "teams:write", "policies:evaluate", "graph-users:read", "ring-groups:read", "team-ring-groups:read", "team-ring-groups:write", "user-ring-groups:write"], "capabilities": ["authorization", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-admin"}}, "@atlas/features", "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/teams-ui/latest/index.html", "slug": "teams", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.2", "version_id": "f6b8fa103f0d4726ae2364933cd20880"}, {"id": "59425d9aaffa4667abacce22375db9be", "name": "Voice ACW Settings Bulk Action", "scopes": ["openid", "users-wrap-up-settings:write"], "capabilities": ["authorization", "@atlas/features", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-team"}}, {"name": "protocol", "protocols": ["voice-acw-settings-bulk-action:show"]}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-settings-app/conversation-settings-app/voice-acw-settings-bulk-action/0.0.3/voice-acw-settings-bulk-action/index.html", "slug": "voice-acw-settings-bulk-action", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.5", "version_id": "517ebd2cfbd840fa99b8532b06b86f53"}, {"id": "a5e6a7d6191c4fc9931f604a16b33bb1", "name": "Card Builder", "scopes": ["actions:read", "cards:read", "cards:write", "openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "automation-designer:read", "automation-designer:write", "workspace-settings:read", "workspace-settings:write"], "capabilities": ["authorization", "navigation", "popup", "toast", "@atlas/features", "@atlas/product-analytics", {"name": "protocol", "protocols": ["card-builder:redirect"]}, {"name": "@atlas/monitoring"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/card-builder/latest/index.html", "slug": "card-builder", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_cards.svg"}, "version": "0.1.3", "version_id": "2202a80f5969412b93c29de185d65afa"}, {"id": "64dd549f92b14acf96393f0c3720bfee", "name": "Automation Designer", "scopes": ["openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "workspace-settings:read", "workspace-settings:write", "automation-designer:read", "automation-designer:write", "automation-designer:notifications", "users:read", "actions:read", "integrations:read", "cards:read", "functions:read", "virtual-agents-flows:read", "virtual-agent-monitor:read", "virtual-agent-conversation-engine:inter-message", "virtual-agent-conversation-engine:inter-context", "virtual-agents-flows-config:read", "virtual-agents-flows-config:write", "cfm:read", "cfm:write", "ai-generative-function:invoke", "queues:read"], "capabilities": ["authorization", "navigation", "toast", "download", "popup", "clipboard", {"name": "i18n", "languages": ["en-US", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, "@atlas/features", "@atlas/product-analytics", "form", {"name": "protocol", "protocols": ["automation-designer:edit"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/automation-builder-ui/2.66.0/app/index.html", "slug": "automation-designer", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.18", "version_id": "0709f86562e144d3929ee714a369c3e6"}, {"id": "dc4a190c39fc49c7ae5be9b2acf40659", "name": "Classic Admin (INTERNAL USE ONLY)", "scopes": ["openid", "account-phones:read", "account-profile:read", "account:read", "apps:graphql", "billing-details:read", "contacts:read", "graph-users:read", "integrations:read", "main-bff", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-heartbeat:write", "roles:read", "voicemails:read", "account-billing:read", "account-phones:read", "acls:read", "apps:graphql", "authentication-settings:read", "authentication-settings:write", "billing-details:read", "contacts:read", "device-routing:read", "device-routing:write", "dispositions:read", "dispositions:write", "ring-groups:read", "interaction-quality-settings:read", "interaction-quality-settings:write", "password-policies:read", "password-policies:write", "policies:evaluate", "recordings:read", "roles:read", "teams:read", "teams:write", "user-disposition-settings:write", "user-ring-groups:write", "users-privacy-settings:write", "users:read", "user-ring-groups:read", "user-roles:read", "voicemails:read", "voicemails:write", "flow-definitions:read", "flow-definitions:write", "flow-resources:read", "functions:read", "functions:write", "functions:execute", "interaction-triggers:read", "interaction-triggers:write", "outage-mitigations-activation:read", "outage-mitigations-activation:write", "outage-mitigations:read", "outage-mitigations:write", "routing-components:read", "numbers:read", "mobile-settings:read", "mobile-settings:write", "message-templates-audio-file:write", "message-templates:read", "message-templates:write", "account-pricing-resources:read", "billable-numbers:read", "team-ring-groups:read", "team-ring-groups:write", "enterprise-voice-numbers:read", "integrations:execute", "licenses:read", "license-orders:write", "license-orders:read", "classic-admin:read", "classic-admin:write", "seats:read", "seats:write", "attributes:read", "attributes:write", "actions:read", "actions:write", "automations:read", "automations:write", "connection-action-executions:write", "agent-assist-automations:read", "agent-assist-automations:write", "agent-assist-recommendations:read", "agent-assist-recommendations:write", "actions-executions:read", "actions-executions-details:read", "queues:read", "emergency-notification-groups:read", "emergency-endpoints:read", "emergency-locations:read", "emergency-settings:read", "emergency-settings:write", "emergency-locations:write", "emergency-notification-groups:write", "emergency-endpoints:write", "agent-settings-passthrough:write", "users:write"], "capabilities": ["authorization", "popup", "audio", "navigation", "form", "download", "@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-atlas"}}, {"name": "@atlas/product-analytics"}, {"name": "@atlas/session-recorder"}, {"name": "@atlas/features"}, {"name": "lifecycle", "nonPerformant": true}, "messaging", {"name": "protocol", "protocols": ["numbers:details", "agents:list"]}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/main-ui/1.250.4/assets/resources/static-pages/index.html", "slug": "classic-admin", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.12", "version_id": "fa6a232abfda4034a54683ffbd280cac"}, {"id": "55502b7271414d58a071922e37aba089", "name": "Unified Recording", "scopes": ["openid", "ur:read", "ur:write"], "capabilities": ["@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "wfo-echo"}}, {"name": "@atlas/logger", "provider": "dynatrace"}, "@atlas/desktop-activity", "@atlas/context", "@atlas/session-recorder", "authorization", {"name": "background", "retryOnFailure": true}, {"name": "rtm", "events": {"account": ["agent_status_changed"], "user": ["screen_recording_started", "screen_recording_stopped"]}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/ur-app/1.19.0/index.html", "slug": "ur-app", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/screenrecording.svg"}, "version": "0.2.5", "version_id": "1a87da88b0f74a729434cf800ff73849"}, {"id": "c955b320344c470cbd04e0b855bb8ffc", "name": "Workspace Settings", "scopes": ["openid", "workspace-settings:read", "workspace-settings:write"], "capabilities": ["authorization", "navigation", "toast", "form", {"name": "i18n", "languages": ["en-US", "es-ES", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-atlas"}}, "@atlas/features", "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/workspace-settings-app/0.10.0/index.html", "slug": "workspace-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.11", "version_id": "ab089ae5edf0415d9b6aece1abc26190"}, {"id": "be2b6acd80a04c5389f072be2ac8eea4", "name": "AppConnect Marketplace", "scopes": [], "capabilities": ["form", "popup", "navigation", {"name": "protocol", "protocols": []}], "render": "canvas", "src": "https://appconnect.talkdesk.com/marketplace-iframe", "slug": "appconnect", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/developer-app.svg"}, "version": "0.0.2", "version_id": "ea58099439194386a27fd7f016d7edc1"}, {"id": "0e81e821a58a469380962d4c86c079a8", "name": "Users Settings", "scopes": ["openid", "roles:read", "users:read", "acls:read", "administration-users:write", "graph-users:read", "users:write", "policies:evaluate", "bulk-imports:read", "bulk-imports:write", "teams:read", "teams:write", "integrations:read", "authentication-invitation:write", "agent-settings-passthrough:write", "queues:read"], "capabilities": ["authorization", "toast", "navigation", "form", {"name": "i18n", "languages": ["pt-PT", "en-US"], "resourcesTemplateUrl": "https://prd-cdn-talkdesk.talkdesk.com/i18n/live/users-settings/{{lng}}/latest/{{ns}}.json", "fallbackResourcesTemplateUrl": "i18n/resources/{{lng}}/{{ns}}.json"}, "portal", "download", "messaging", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-admin"}}, "@atlas/menus", "popup", {"name": "protocol", "protocols": [{"name": "Create user", "key": "users-settings:create-user", "description": "Creates a new user", "icon": "person_add", "params": {"first_name": {"name": "First name", "type": "string", "description": "The first name of the user", "placeholder": "<PERSON>"}, "last_name": {"name": "Last name", "type": "string", "description": "The last name of the user", "placeholder": "<PERSON><PERSON>"}, "email": {"name": "Email", "type": "email", "description": "The email of the user", "placeholder": "<EMAIL>"}}}, "users-settings:show", "navigate-to-user-details", "navigate-to-user-create", "navigate-to-bulk-import", "users-bulk-action:result"]}, "@atlas/features", "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/users-app/v1.25.2/index.html", "slug": "users-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/people.svg"}, "version": "0.0.17", "version_id": "d49b879367f34e458ef71e1969609f7e"}, {"id": "3399dfe579f8406994f35fe4f2e0b34f", "name": "Classic Admin Bulk", "scopes": ["openid", "account-phones:read", "account-profile:read", "account:read", "apps:graphql", "billing-details:read", "contacts:read", "graph-users:read", "integrations:read", "main-bff", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-heartbeat:write", "roles:read", "voicemails:read", "account-billing:read", "account-phones:read", "acls:read", "apps:graphql", "authentication-settings:read", "authentication-settings:write", "billing-details:read", "contacts:read", "device-routing:read", "device-routing:write", "dispositions:read", "dispositions:write", "ring-groups:read", "interaction-quality-settings:read", "interaction-quality-settings:write", "password-policies:read", "password-policies:write", "policies:evaluate", "recordings:read", "roles:read", "teams:read", "teams:write", "user-disposition-settings:write", "user-ring-groups:write", "users-privacy-settings:write", "users:read", "user-ring-groups:read", "user-roles:read", "voicemails:read", "voicemails:write", "flow-definitions:read", "flow-definitions:write", "flow-resources:read", "functions:read", "functions:write", "functions:execute", "interaction-triggers:read", "interaction-triggers:write", "outage-mitigations-activation:read", "outage-mitigations-activation:write", "outage-mitigations:read", "outage-mitigations:write", "routing-components:read", "numbers:read", "mobile-settings:read", "mobile-settings:write", "message-templates-audio-file:write", "message-templates:read", "message-templates:write", "account-pricing-resources:read", "billable-numbers:read", "team-ring-groups:read", "enterprise-voice-numbers:read", "integrations:execute", "licenses:read", "license-orders:write", "license-orders:read", "classic-admin:read", "classic-admin:write", "seats:read", "seats:write", "attributes:read", "attributes:write", "actions:read", "actions:write", "automations:read", "automations:write", "connection-action-executions:write", "agent-assist-automations:read", "agent-assist-automations:write", "agent-assist-recommendations:read", "agent-assist-recommendations:write", "actions-executions:read", "actions-executions-details:read", "queues:read", "emergency-notification-groups:read", "emergency-endpoints:read", "emergency-locations:read", "emergency-settings:read", "emergency-locations:write", "emergency-notification-groups:write", "emergency-endpoints:write", "agent-settings-passthrough:write", "users:write"], "capabilities": ["authorization", "popup", "audio", "navigation", "form", "download", {"name": "lifecycle", "nonPerformant": true}, "messaging", {"name": "protocol", "protocols": ["users-bulk-action:show", "numbers-bulk-action:show"]}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-atlas"}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/main-ui/1.250.4/assets/resources/static-pages/index.html", "slug": "classic-admin-bulk", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.8", "version_id": "ab18eec8f0b147a6b3bfda872c7a4bbd"}, {"id": "44d1b37f6805467bb3318b3cde5d18e2", "name": "Copilot", "scopes": ["openid", "users:read", "contacts:read", "account:read", "numbers:read", "agent-assist-automations:read", "agent-assist-automations:write", "agent-assist-recommendations:read", "agent-assist-recommendations:write", "ai-knowledge:read", "upload-jobs:read", "graph-users:read", "policies:evaluate", "ai-aa-storage:bookmark-read", "ai-aa-storage:bookmark-write", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "automation-designer:read", "virtual-agents-flows:read", "virtual-agents-flows-content:read", "virtual-agent-conversation-engine:inter-message", "guide-analytics:read", "aitrainer:read", "ai-settings:read", "ai-tiers:read", "ai-leads:write", "ai-leads:read", "context:read", "virtual-agent-monitor:read", "ai-aa-storage:read", "virtual-agent-conversation-engine:inter-context", "ai-settings-pipelines:read", "dispositions:read", "applied-science-functions:invoke", "ai-platform-feedback:send", "conversations:read", "omnichannel-inbox:write", "ai-acw-wrap-up:invoke", "ai-generative-function:invoke", "nlp-aa-pipeline:summarize", "rtm-publish:write"], "capabilities": ["authorization", "navigation", "popup", "portal", "toast", "download", "form", "@atlas/context", "@atlas/product-analytics", "@atlas/features", "@atlas/session-recorder", {"name": "background", "enableRequestToForeground": true}, {"name": "protocol", "protocols": ["copilot-copy-notes-secondary"]}, {"name": "i18n", "languages": ["en-US", "pt-PT", "es-ES", "fr-FR"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "rtm", "events": {"account": ["call_answered", "outbound_call_answered", "call_finished", "call_transfer_answered", "call_warm_transfer_finished", "ai_interaction_recommendation"], "user": ["after_call_work_started", "after_call_work_skipped", "digital_interaction_acw_started", "digital_interaction_resumed", "ai_interaction_acw_wrap_up_available", "ai_interaction_message", "ai_interaction_recommendation", "ai_interaction_summarization_available", "ai_interaction_disposition_detection_available", "ai_interaction_call_event", "ai_interaction_summarization_started", "ai_interaction_summarization_failed"]}}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "secondary-area", "src": "https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/index.html", "slug": "agent-assist-secondary", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg"}, "version": "0.0.1", "version_id": "70e41f28408a11eca3e5931bc78a1f81"}, {"id": "655210e9c2d2412daf5e02dce6cfba94", "name": "Smart Help", "scopes": ["openid", "users:read", "contacts:read", "account:read", "numbers:read", "ai-knowledge:read", "graph-users:read", "policies:evaluate", "context:read", "templates:read", "templates:write", "templates:install", "aitrainer:read", "flow-definitions:read", "aitrainer-model-imports:write", "virtual-agents-flows:read", "ai-settings:write", "automation-designer:write", "ai-tiers:read"], "capabilities": ["audio", "authorization", "navigation", "popup", "portal", "toast", "download", "form", "@atlas/context", "@atlas/product-analytics", "fullscreen", {"name": "protocol", "protocols": []}, {"name": "i18n", "languages": ["en-US", "pt-PT", "es-ES", "fr-FR"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring"}], "render": "secondary-area", "src": "https://prd-cdn-talkdesk.talkdesk.com/ai-contextual-help-ui/latest/index.html", "slug": "contextual-help", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/app_helper.svg"}, "version": "0.0.10", "version_id": "e52b607c46584d329740cac993626e38"}, {"id": "a8d3263a80524c67906674d33296bdb4", "name": "Studio Editor", "scopes": ["account-profile:read", "flow-definitions:read", "flow-definitions:write", "functions:read", "numbers:read", "routing-components:read", "routing-components:translate", "ring-groups:read", "users:read", "assets:write", "assets:read", "assets:download", "account:read", "openid", "graph-users:read", "actions:read", "virtual-agents-flows:read", "attributes:read", "voicebiometrics:read", "sms-integrations:read", "directory-personas:read", "prompts:read", "prompts:write", "prompts:download", "policies:evaluate", "automation-designer:read", "cfm:read", "tts-languages:read"], "capabilities": ["authorization", "navigation", "clipboard", "audio", "download", "popup", "form", "portal", "toast", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "orchestration-platform", "metrics": [{"name": "flow-editor-load-time"}]}}, "@atlas/product-analytics"], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/cxm-ui/1.112.0/cxm-ui-flow-editor-atlas.html", "slug": "studio-editor", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/routing.svg"}, "version": "0.0.15", "version_id": "f65e4ed827904cf3bc1cc663f7799364"}, {"id": "1b2d0a3e387447dc9146ee360a454d8b", "name": "Live Monitoring", "scopes": ["openid", "ur:write", "call-entry:read", "graph-users:read", "contacts:read", "main-bff", "interactions:barge", "interactions:mute", "interactions:whisper", "contact-details:read", "presence-user:read", "callbar", "credentials-twilio:read"], "capabilities": ["audio", "authorization", "navigation", "toast", "@atlas/features", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "wfo-echo"}}, {"name": "@atlas/logger", "provider": "dynatrace"}, "@atlas/session-recorder", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "rtm", "events": {"account": ["agent_status_changed", "call_finished", "call_answered", "call_merged", "outbound_call_answered", "call_transfer_answered", "call_warm_transfer_finished"]}}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/live-monitoring/0.11.8/index.html", "slug": "live-monitoring", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.10", "version_id": "3ddeb71e51f4461896608696f9740949"}, {"id": "4feef246ae2c4753bf98d1e2350f054b", "name": "conversation-settings", "scopes": ["openid", "callbar", "users-default-country-code:write", "device-routing:read", "device-routing-nailup:write"], "capabilities": ["authorization", "audio", "download", "popup", "toast", "@atlas/features", "@atlas/audio-device-management", "@atlas/session-recorder", {"name": "i18n", "languages": ["en-US"], "resourcesTemplateUrl": "https://prd-cdn-talkdesk.talkdesk.com/i18n/live/conversation-settings/{{lng}}/latest/app.json", "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "rtm", "events": {"account": ["update_account_device_routing"], "user": ["update_user_device_routing"]}}, "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-team"}}, {"name": "protocol", "protocols": ["open-conversation-settings"]}], "render": "canvas", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-settings-app/conversation-settings-app/app/0.6.2/index.html", "slug": "conversation-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.17", "version_id": "c3db0da6b1d64212b383800d6006a180"}, {"id": "c3d08a0a92eb489fbc6c3d7c7e152d22", "name": "Callback Scheduler", "scopes": ["account-uc-status:read", "apps:graphql", "byot-voice-call:read", "call-entry:read", "callbar", "callbar-analytics:write", "callbar-settings:read", "campaigns:read", "device-routing-nailup:write", "device-routing:read", "device-routing:write", "dispositions:read", "do-not-call-lists:manage", "emergency-device-locations:read", "graph-users:read", "integrations-zendesk-tickets:read", "interaction-quality-feedback:write", "interaction-quality-settings:read", "interaction-quality-settings:write", "interactions:disconnect", "interactions:hold", "interactions:transfer", "megazord:read", "numbers:read", "openid", "po-callbacks:read", "po-callbacks:write", "po-interactions:read", "policies:evaluate", "presence-user:read", "record-lists:manage", "ring-groups:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "sentiment-settings:read", "ur:read", "ur:write", "user-session:end", "users:read", "schedule-callbacks:read", "schedule-callbacks:write", "context:read"], "capabilities": ["@atlas/product-analytics", "authorization", "toast", "messaging", {"name": "protocol", "protocols": []}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "continuouseng-deeting"}}, {"name": "rtm", "events": {"account": ["agent_status_changed", "outbound_call_answered"], "user": ["call_leg_answered"]}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/manager-contact-tab/0.23.1/index.html", "slug": "manager-contact-card-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/proactive-outbound.svg"}, "version": "1.3.5", "version_id": "e564005682db41739cf7d5d4c27cdc9f"}, {"id": "f8bf11fb81454c258035cecaa0d65484", "name": "Voice ACW Settings", "scopes": ["openid", "user-disposition-settings:read", "user-disposition-settings:write", "users-wrap-up-settings:read", "users-wrap-up-settings:write", "users:read", "policies:evaluate"], "capabilities": ["authorization", "toast", "@atlas/features", "@atlas/menus", "messaging", "navigation", {"name": "i18n", "languages": ["en-US"], "resourcesTemplateUrl": "https://prd-cdn-talkdesk.talkdesk.com/i18n/live/voice-acw-settings/{{lng}}/latest/app.json", "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-team"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-settings-app/conversation-settings-app/voice-acw-settings/1.2.8/voice-acw-settings/index.html", "slug": "voice-acw-settings", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.2", "version_id": "f8bf11fb81454c258035cecaa0d65484"}, {"id": "a53ceae5476347548d3506e8393b5b7b", "name": "Conversation Runtime Settings Tab", "scopes": ["openid", "policies:evaluate", "users:read", "graph-users:read", "conversation-runtime-user-settings:read", "conversation-runtime-user-settings:write"], "capabilities": ["authorization", "toast", "messaging", "navigation", {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "conversation-core"}}, {"name": "protocol", "protocols": []}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-runtime-settings/0.5.0/index.html", "slug": "conversation-runtime-settings-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.5", "version_id": "5ff70c86c5cf43128329dd4f71040cac"}, {"id": "98183694aea148b48da4fe12f07d3038", "name": "Contact Creation Portal", "scopes": ["openid", "contact-details:write", "account-custom-fields:read", "workspace-contacts:read"], "capabilities": ["authorization", "form", "messaging", "toast", "@atlas/features"], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/contacts-app/4.1.5/creation-app/index.html", "slug": "contact-creation-portal", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "1.1.4", "version_id": "761ca4fdf9224aecad44d79ac7c40952"}, {"id": "02be8e2160a94c7fb900d1d64cd1e15e", "name": "Dispositions Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/dispositions-tab/index.html", "slug": "dispositions-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.32", "version_id": "f2b9f915aca14026aede37df86da5ec4"}, {"id": "5a7e1ab6b0de4cb0bf68f4d972d68479", "name": "Notes Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/notes-tab/index.html", "slug": "notes-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.31", "version_id": "0d11a6e7aa9842a2af43f8315f977e7b"}, {"id": "d9509283197f487f9242d902dd4d4f03", "name": "Snapshot Tab", "scopes": [], "capabilities": [], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/conversation-app/v3.35.0/snapshot-tab/index.html", "slug": "snapshot-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.31", "version_id": "acda71c3d61842619f35fcd8880a04a9"}, {"id": "b62aad08444349a685dcd44a03b31df9", "name": "Transcription Card", "scopes": ["apps:read", "speech-analytics:read", "interaction-analytics:read", "recordings:read", "policies:evaluate", "conversations:read", "ai-tiers:read", "graph-users:read"], "capabilities": ["authorization", "toast", "messaging", "download", "popup", "@atlas/product-analytics", "@atlas/context", {"name": "i18n", "languages": ["en-US", "pt-PT"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "protocol", "protocols": []}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/ai-speech-analytics/2.0.2/transcription-card/index.html", "slug": "transcription-card", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.8", "version_id": "38509546accc4bc290b8a65f97077f9b"}, {"id": "cc63dda846a7419d8455d3ddbefef663", "name": "Unified Communications - Directory", "scopes": ["openid", "directory-personas:read", "presence-hub:read", "policies:evaluate", "directory-providers:read"], "capabilities": ["authorization", "navigation", "popup", "toast", "messaging", {"name": "protocol", "protocols": []}, {"name": "i18n", "languages": ["en-US", "es-ES", "fr-CA", "pt-PT"], "fallbackResourcesTemplateUrl": "assets/locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "uc-directory"}}, "@atlas/product-analytics", {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/uc-directory-frontend/3.1.9/index.html", "slug": "uc-directory", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "0.0.10", "version_id": "7c18397672314e5ab4ba8ca1aa4a58c0"}, {"id": "963846c212364661a38c007c13c55465", "name": "Identity Tab", "scopes": ["openid", "users:read", "account:read", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "policies:evaluate", "guardian:read", "guardian:write", "guardian-users:read", "guardian-upsell:write", "voicebiometrics:read", "identity-phone-validation:read", "graph-users:read", "apps:read", "licenses:read", "context:read", "identity:read", "voicebiometrics-enroll:write", "voicebiometrics-consent:write", "voicebiometrics-consent:read", "contacts:read", "identity-upsell:write", "identity-notifications:write", "identity-subscriptions:read"], "capabilities": [{"name": "i18n", "languages": ["pt-PT", "en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "ai-engineering"}}, "authorization", "navigation", "form", "toast", "popup", "download", "messaging"], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/identity-conversation-tab/1.13.0/index.html", "slug": "identity-conversation-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/identity.svg"}, "version": "0.0.9", "version_id": "ccd2574ea26e4f4fae1c72eb914e3bef"}, {"id": "1ccc51a92e5a4700a67bc89d24cea8b2", "name": "Case Card Tab", "scopes": ["openid", "acls:read", "notifications:read", "graph-users:read", "cases:write", "cases:read", "case-notification:write", "case-forms:read", "case-fields:read", "callbar", "case-tenant-apps:read", "customer-data-store:read", "case-tags:read", "contacts:read", "case-views:read", "email-threads:read", "case-web-forms:read", "omnichannel-inbox:write", "voicemails:read", "case-bi-report-trace:write", "policies:evaluate"], "capabilities": ["authorization", "toast", "messaging", "popup", "clipboard", "@atlas/product-analytics", {"name": "protocol", "protocols": ["case-card-tab-protocol"]}, {"name": "i18n", "languages": ["en-US"], "fallbackResourcesTemplateUrl": "locales/en-US/{{ns}}.json"}, {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "omni-kungfu"}}, {"name": "@atlas/logger", "provider": "dynatrace"}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/case-card-tab/1.0.55/index.html", "slug": "case-card-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/case-management-ui/latest/case-management.svg"}, "version": "1.0.54", "version_id": "ca8db544a2c94eacbe265a3543ebb76b"}, {"id": "1c9e989d214f41c6a6efb68e453be984", "name": "Contact Activities Portal", "scopes": ["account:read", "contacts-activities:read", "graph-users:read", "numbers:read", "openid", "policies:evaluate", "recordings:read", "contact-details:read", "digital-connect:read", "provider-abstraction-service:read", "email-touchpoints:read", "contact-interactions:write", "activity-notes:write", "interaction-contacts:read", "omnichannel-inbox:write", "customer-data-store:read", "ai-generative-function:invoke", "interaction-contacts:write", "cases:read", "apps:graphql"], "capabilities": ["@atlas/context", "@atlas/features", "@atlas/logger", "authorization", "messaging", "navigation", "clipboard", "popup", "toast", "portal", "download", {"name": "protocol", "protocols": ["voicemails:show-details"]}, "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/contact-activities-app/2.4.6/contact-activities-portal-app/index.html", "slug": "contact-activities-portal", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "1.0.16", "version_id": "200a5691bc1a4426a6a3082081546103"}, {"id": "ed5df2a40aa94505906cebea11fa172c", "name": "Cards Static Component", "scopes": ["openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "workspace-settings:read", "workspace-settings:write"], "capabilities": ["authorization", "navigation", "portal", "messaging", "popup"], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/cards-static-component/latest/index.html", "slug": "cards-static-component", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_dashboard.svg"}, "version": "0.0.1", "version_id": "60abf592fa4d46b78fb245039ad15203"}, {"id": "c1b986dcaf1043d7a9245bf6d94b537c", "name": "Canvas Runtime", "scopes": ["actions:read", "connection-action-executions:write", "cards:read", "context:read", "openid", "account:read", "apps:graphql", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-notifications:read", "user-notifications:write", "policies:evaluate", "account-profile:read", "workspace-settings:read", "workspace-settings:write", "automation-designer:read", "automation-designer:write"], "capabilities": ["authorization", "clipboard", "navigation", "portal", "messaging", "toast", "form", "video", "audio", "fullscreen", "download", "@atlas/context", "@atlas/features", "@atlas/session-recorder", "@atlas/product-analytics", "popup", "@atlas/logger", "@atlas/menus", {"name": "@atlas/monitoring"}, {"name": "rtm", "events": {"account": ["call_answered", "call_finished", "call_initiated", "call_external_answered", "call_external_finished", "call_external_initiated", "agent_status_changed", "outbound_call_answered"], "user": ["digital_interaction_assigned"]}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/workspace-designer/runtime/0.2.6/index.html", "slug": "canvas-runtime", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/workspace_designer_dashboard.svg"}, "version": "0.1.12", "version_id": "9bc05e0edef241888cd93afd45ba88c6"}, {"id": "b1e313c76eac4d6ea74a49bd5498a8ab", "name": "Conversation Demo Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "demo", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/demo-channel/2.5.9/demo-channel/remoteEntry.js", "slug": "conversation-demo-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.20", "version_id": "765ed17a41fd4e9db7adf7c3e75c30f2"}, {"id": "b3d98614c84f4821ac13f0eabf1b986b", "name": "Conversation Digital Connect Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "digital_connect", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/digital-connect-channel/0.99.0/digital-connect-channel/remoteEntry.js", "slug": "conversation-digital-connect-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.40", "version_id": "b2d2ec0ead5d44e1902387559ae27b56"}, {"id": "68a122c1ec22403b8e32ee5b183b7ad2", "name": "Conversation Email Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "email", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/email-channel/0.99.0/email-channel/remoteEntry.js", "slug": "conversation-email-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.37", "version_id": "42a4b87d35924282adf7ab7f8f55b418"}, {"id": "7f2b46ba6de84e99b724af3f78b089c7", "name": "Conversation Digital Connect Fax Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "digital_connect_fax", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/digital-connect-fax-channel/0.99.0/digital-connect-fax-channel/remoteEntry.js", "slug": "conversation-digital-connect-fax-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.36", "version_id": "908e24ceec6a4ed2811e13b8aa77d448"}, {"id": "cc7ecd171aad4ddb9cba24f00d6eb957", "name": "Conversation Live Chat Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "live_chat", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/live-chat-channel/0.99.0/live-chat-channel/remoteEntry.js", "slug": "conversation-live-chat-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.37", "version_id": "ca4b4f6a21034d97a791601f1f4bbb7f"}, {"id": "3589b2051f1a4d82b03cebe2e1f46dcf", "name": "Conversation Fbm Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "fbm", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/fbm-channel/0.99.0/fbm-channel/remoteEntry.js", "slug": "conversation-fbm-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.37", "version_id": "615ce4f5016b4f4bbe6301b16a7dfc09"}, {"id": "55c92c725a074954beaca31bf7779f7e", "name": "Conversation SMS Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "sms", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/sms-channel/0.99.0/sms-channel/remoteEntry.js", "slug": "conversation-sms-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.27", "version_id": "3bec869b2e75439f89e2321523689b1e"}, {"id": "e58ae7958970487e83fca4ffd45c321b", "name": "Conversation Whatsapp Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "whatsapp", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/whatsapp-channel/0.99.0/whatsapp-channel/remoteEntry.js", "slug": "conversation-whatsapp-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.37", "version_id": "dafa5af1a1184dd688275c6ead490b09"}, {"id": "c47618876e0a46a6a22e467e0b0a4ce9", "name": "Conversation Voice Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "voice", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/voice-channel/1.48.0/voice-channel/remoteEntry.js", "slug": "conversation-voice-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.45", "version_id": "5aa64702520545f8bbab2a4e03222a76"}, {"id": "05dcac0ef84d4fbfa18a38f8a12a626a", "name": "Contact Activities Conversation Tab", "scopes": ["account:read", "contacts-activities:read", "graph-users:read", "numbers:read", "openid", "policies:evaluate", "recordings:read", "contact-details:read", "digital-connect:read", "provider-abstraction-service:read", "email-touchpoints:read", "contact-interactions:write", "activity-notes:write", "interaction-contacts:read", "omnichannel-inbox:write", "customer-data-store:read", "ai-generative-function:invoke", "interaction-contacts:write", "cases:read", "apps:graphql"], "capabilities": ["authorization", "messaging", "navigation", "clipboard", "popup", "toast", "portal", "download", {"name": "protocol", "protocols": ["voicemails:show-details"]}, "@atlas/context", "@atlas/features", "@atlas/product-analytics", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "apps-productivity-eagle"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/contact-activities-app/2.4.6/contact-activities-portal-app/index.html?config=conversation-tab", "slug": "contact-activities-conversation-tab", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/contacts.svg"}, "version": "0.0.8", "version_id": "2f2ae7cab6e34ef88735c6181d48e615"}, {"id": "008192304b8c48ed98993a5448d79aa5", "name": "Conversation Digital Connect WhatsAPP Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "digital_connect_whatsapp", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/digital-connect-whatsapp-channel/0.99.0/digital-connect-whatsapp-channel/remoteEntry.js", "slug": "conversation-digital-connect-whatsapp-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.10", "version_id": "f4f9383a316848108cd5195526b19905"}, {"id": "863ea1275ed0423f8233ae069ceeb7e9", "name": "Conversation Apple Messages for Business Channel", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "apple_messages_for_business", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/apple-messages-for-business-channel/0.99.0/apple-messages-for-business-channel/remoteEntry.js", "slug": "conversation-apple-messages-for-business-channel", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.10", "version_id": "16b36055efd7438cbeb7066963da7ad5"}, {"id": "183c223a750e44f8bf863de8c171c643", "name": "Conversation Digital Channels", "scopes": [], "capabilities": [{"name": "@atlas/modules", "remoteName": "digital_channels", "moduleName": "./Channel"}], "render": "canvas", "src": "https://conversationapp.svc.talkdeskapp.com/conversation-app/digital-channels/0.101.0/digital-channels/remoteEntry.js", "slug": "conversation-digital-channels", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/dummy-app.svg"}, "version": "0.0.2", "version_id": "08786d537fe74a1d875a8e177c0440fb"}, {"id": "924d4dc1b2e74c019ca4671ebf86cbe1", "name": "Classic Admin (Portal)", "scopes": ["openid", "account-phones:read", "account-profile:read", "account:read", "apps:graphql", "billing-details:read", "contacts:read", "graph-users:read", "integrations:read", "main-bff", "rtm-settings:read", "rtm-user:auth", "rtm:subscribe", "user-heartbeat:write", "roles:read", "voicemails:read", "account-billing:read", "account-phones:read", "acls:read", "apps:graphql", "authentication-settings:read", "authentication-settings:write", "billing-details:read", "contacts:read", "device-routing:read", "device-routing:write", "dispositions:read", "dispositions:write", "ring-groups:read", "interaction-quality-settings:read", "interaction-quality-settings:write", "password-policies:read", "password-policies:write", "policies:evaluate", "recordings:read", "roles:read", "teams:read", "teams:write", "user-disposition-settings:write", "user-ring-groups:write", "users-privacy-settings:write", "users:read", "user-ring-groups:read", "user-roles:read", "voicemails:read", "voicemails:write", "flow-definitions:read", "flow-definitions:write", "flow-resources:read", "functions:read", "functions:write", "functions:execute", "interaction-triggers:read", "interaction-triggers:write", "outage-mitigations-activation:read", "outage-mitigations-activation:write", "outage-mitigations:read", "outage-mitigations:write", "routing-components:read", "numbers:read", "mobile-settings:read", "mobile-settings:write", "message-templates-audio-file:write", "message-templates:read", "message-templates:write", "account-pricing-resources:read", "billable-numbers:read", "team-ring-groups:read", "enterprise-voice-numbers:read", "integrations:execute", "licenses:read", "license-orders:write", "license-orders:read", "classic-admin:read", "classic-admin:write", "seats:read", "seats:write", "attributes:read", "attributes:write", "actions:read", "actions:write", "automations:read", "automations:write", "connection-action-executions:write", "agent-assist-automations:read", "agent-assist-automations:write", "agent-assist-recommendations:read", "agent-assist-recommendations:write", "actions-executions:read", "actions-executions-details:read", "queues:read", "emergency-notification-groups:read", "emergency-endpoints:read", "emergency-locations:read", "emergency-settings:read", "emergency-locations:write", "emergency-notification-groups:write", "emergency-endpoints:write", "agent-settings-passthrough:write", "users:write"], "capabilities": ["authorization", "popup", "audio", "navigation", "form", "download", {"name": "lifecycle", "nonPerformant": true}, "messaging", {"name": "@atlas/monitoring", "provider": "dynatrace-otlp", "properties": {"owner": "cci-atlas"}}], "render": "portal", "src": "https://prd-cdn-talkdesk.talkdesk.com/main-ui/1.250.4/assets/resources/static-pages/index.html", "slug": "classic-admin-portal", "assets": {"icon": "https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/settings.svg"}, "version": "0.0.8", "version_id": "fed7bf57d4d64333a9a9794b080300ac"}], "isFallback": false}}