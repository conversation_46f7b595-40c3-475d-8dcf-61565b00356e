{"modeConfiguration.title": "Mode", "dialingStrategyConfiguration.disposition.toggle.disable.label": "Don't Apply", "dialingStrategyConfiguration.disposition.description": "custom rules based on system dispositions", "reportingConfiguration.seeOnExplore": "See on Explore", "dialingStrategyConfiguration.retryPeriodField.suffix": "between attempts,", "modals.leaveCampaign.cancelButton": "Keep editing", "modals.removeAssignedList.title": "Remove list", "visibleToTeam": "Visible to: {{teamName}}", "dialingStrategyConfiguration.callingHoursField.duration": "Duration", "dialingStrategyConfiguration.disposition.toggle.enable.label": "Apply", "dialingStrategyConfiguration.recordChain.attempts": "Attempts", "agentsConfiguration.emptyTitle": "No available agents", "sidePanels.recordLists.table.headers.team": "Team", "dialingStrategyConfiguration.phoneNumberSequence.ifItExitText": "if it exists,", "dialingStrategyConfiguration.phoneNumberSequence.waitAtLeastGlobalRetryPeriod": "Wait at least", "sidePanels.agentFilters.dropdown.label": "Ring Groups", "dialingStrategyConfiguration.studioFlow.title": "Studio Flow", "listsConfiguration.recordLists.table.column.defaultTeam": "No teams", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.hangupBeforeConnection": "Insert a number between 1 and 100 for maximum number of hangup before connection call attempts the dialer will perform on each record in this campaign", "dispositionsConfiguration.customDispositionsTitle": "System dispositions", "modals.leaveCampaign.message": "Your progress will not be saved", "agentsConfiguration.searchPlaceholder": "Search by agent name", "modeConfiguration.dialingModeField.defaultPlaceholder": "Select dialing mode", "dialingStrategyConfiguration.retryStrategy.title": "Retry strategy", "modeConfiguration.amdMetadata.topHint": "When enabled, an additional in-house AMD layer based on Metadata will improve the initial AMD decision", "modeConfiguration.maxAbandonmentRateField.errorOutOfBoundsWarning": "Please insert a valid number between {{min}} and {{max}}. Additionally, only numbers with up to {{decimals}} decimal places are accepted.", "priorityConfiguration.description": "Select the campaign's priority, taking into account that calls per agent are placed for higher priority campaigns, being 1 the lowest and 10 the highest", "dispositionsConfiguration.statusAfterCall.FINAL": "Final", "dialingStrategyConfiguration.callingHoursField.addCallingHoursLabel": "Add calling hours", "dialingStrategyConfiguration.recordChain.retryPeriod.title": "Retry time", "reportingConfiguration.title": "Reporting", "dialingStrategyConfiguration.connectionTimeoutField.label": "Connection timeout", "filters.ringGroups.single": "ring group", "dialingStrategyConfiguration.callerIdField.legacyLabel": "Caller ID", "agentsConfiguration.searchTooltip": "Type at least {{minLength}} characters", "listsConfiguration.doNotCallLists.table.headers.numberOfElementsInList": "Number of entries", "dialingStrategyConfiguration.timeOption.midNight": "Mid-night", "modals.deleteCampaign.title": "Delete campaign?", "modals.discardCreateCampaign.title": "Discard campaign?", "listsConfiguration.doNotCallLists.table.column.defaultTeam": "No teams", "modeConfiguration.amdMetadata.label": "AMD Metadata", "singleItemMultipleErrorsMessage": "{{field}} has {{errorCount}} errors", "dialingStrategyConfiguration.phoneNumberSequence.waitBaseOnDefaultTime": "Wait based on the default time between attempts", "sidePanels.agentFilters.dropdown.placeholder": "Select ring groups", "dispositionsConfiguration.customDispositionsDescription": "Configure the type and rules for each system disposition in this campaign", "dispositionsConfiguration.description": "Configure dispositions for this campaign", "modeConfiguration.dialingModeField.label": "Dialing mode", "sidePanels.callerIds.viewMode.title": "Selected caller IDs", "sidePanels.agentsAssociatedCampaigns.searchPlaceholder": "Search for a campaign", "dialingStrategyConfiguration.maxAttemptsPerRecordField.attemptCount": "{{count}} attempt", "dialingStrategyConfiguration.attemptsPerRecord.unitLabel": "attempt per record.", "agentsTabErrorMessage": "There are no agents", "sidePanels.recordLists.table.row_plural": "{{formattedCount}} records", "dialingStrategyConfiguration.recordChain.retryPeriod.hint": "Time the dialer will wait before calling the phone number again\t", "agentsConfiguration.title": "Agents", "dispositionsConfiguration.dispositions.agentHangupBeforeConnection": "Agent hangup before connection", "agentsConfiguration.emptyMessage": "The campaign doesn't have any agents working on it. Please edit the campaign and assign agents to it", "dispositionsConfiguration.dispositionHeaders.agentHangupBeforeConnection": "Agent hangup before connection system disposition", "dialingStrategyConfiguration.retryPeriodField.prefix": "By default, wait at least", "dialingStrategyConfiguration.retryPeriodField.tooltip": "The dialer may have to wait longer. Ex: due to records in queue or record’s priority", "dialingStrategyConfiguration.retryStrategy.subTitle": "For each record", "dispositionsConfiguration.dispositionHeaders.abandonment": "Abandoned system disposition", "dialingStrategyConfiguration.systemDispositions.toggle.title": "Enable system dispositions rules", "dialingStrategyConfiguration.phoneNumberSequence.outMaxAttempt": "The number cannot exceed the max attempts per record", "modals.leaveCampaign.title": "Leave campaign?", "dispositionsConfiguration.maxAttemptsPerRecord.label.busy": "Max busy attempts per record", "dialingStrategyConfiguration.phoneNumberSequence.repeatUntilExhausted": "Repeat until attempts are exhausted", "listsConfiguration.recordLists.table.row_plural": "{{formattedCount}} records", "dialingStrategyConfiguration.outboundIvr.hint": "When active, a Studio Flow must be selected for this Outbound Campaign", "reportingConfiguration.description": "Analyze how your campaign is performing\t", "dispositionsConfiguration.retryPeriod.topHint.answeringMachineDetection": "Insert the minimum time the dialer will wait in between answering machine detection call attempts", "sidePanels.agentFilters.loadingElements": "Loading elements", "sidePanels.agentFilters.loadMoreElements": "Load more elements", "agentsConfiguration.filters.title": "Filters", "reportingConfiguration.reportSubTitle": "Calls Report", "dispositionsConfiguration.systemDispositions.attempt": "1 attempt max. No retries, call the next number.", "dialingStrategyConfiguration.studioFlow.hint": "Select the Studio Flow to be used in this campaign", "dialingStrategyConfiguration.attemptsPerRecord.noRetry": "No retries, call the next record.", "dispositionsConfiguration.statusAfterCall.RETRY": "Retry", "dispositionsConfiguration.retryPeriod.label.agentHangupBeforeConnection": "Retry period after agent hangup before connection attempts", "dispositionsConfiguration.retryPeriod.topHint.agentHangupBeforeConnection": "Insert the minimum time the dialer will wait in between agent hangup before connection call attempts", "dispositionsConfiguration.retryPeriod.label.hangupBeforeConnection": "Retry period after hangup before connection attempts", "dispositionsConfiguration.retryPeriod.topHint.hangupBeforeConnection": "Insert the minimum time the dialer will wait in between hangup before connection call attempts", "dialingStrategyConfiguration.phoneNumberSequence.unitAfterAttempt": "time", "dialingStrategyConfiguration.phoneNumberSequence.withAtLeastText": "with at least", "sidePanels.recordLists.table.headers.name": "Name", "dialingStrategyConfiguration.maxSkipsPerRecordField.skipCount_plural": "{{count}} skips", "modals.editDisposition.paragraph": "Please choose from the disposition types below:", "modeConfiguration.description": "Configure the dialing mode properties of this campaign\t", "dialingStrategyConfiguration.outboundIvr.title": "Outbound IVR", "sidePanels.callerIds.viewMode.count_plural": "{{count}} caller IDs", "sidePanels.agentsAssociatedCampaigns.table.headers.dialingMode": "Dialing mode", "dispositionsConfiguration.maxAttemptsPerRecord.label.hangupBeforeConnection": "Max hangup before connection attempts per record\t", "sidePanels.recordLists.emptyTitle": "No Record lists yet", "modeConfiguration.abandonmentTimeoutField.unitLabel": "seconds", "dialingStrategyConfiguration.previewContext.secondStep": "Manually call the record", "dialingStrategyConfiguration.dialingOrder.lifo.hint": "The system will consume the record list from the last record to the first one", "dialingStrategyConfiguration.recordChain.phoneLabel": "Phone number type", "dialingStrategyConfiguration.callingHoursField.emptyWeekDays": "Please select at least one day", "dialingStrategyConfiguration.dialingStrategyField.placeholder": "Select a strategy", "agentsConfiguration.tooltipText": "For max efficiency assign different agents to different dialing modes campaigns", "modals.discardCreateCampaign.message": "Your progress won't be saved. You'll have to create a new campaign.\t", "modeConfiguration.abandonmentTimeoutField.label": "Abandonment timeout", "sidePanels.agentsAssociatedCampaigns.description": "Assignment details", "sidePanels.callerIds.editMode.title": "Caller IDs", "dialingStrategyConfiguration.attemptsPerRecord.tooltip": "Each call counts as 1 attempt", "dispositionsConfiguration.systemDispositions.middleText": "with that outcome, waiting at least", "dispositionsConfiguration.systemDispositions.make": "Make", "dispositionsConfiguration.systemDispositions.when": "When", "listsConfiguration.doNotCallLists.table.row_plural": "{{formattedCount}} entries", "modals.removeAssignedList.message": "Are you sure you want to remove the \"{{listName}}\" list?", "agentsConfiguration.showSelectedOnly": "Show selected only", "dispositionsConfiguration.retryPeriod.topHint.abandonment": "Insert the minimum time the dialer will wait in between abandoned call attempts", "sidePanels.callerIds.editMode.count_plural": "{{count}} caller IDs", "sidePanels.callerIds.editMode.count": "{{count}} caller ID", "dialingStrategyConfiguration.disposition.tooltip": "Specific call outcomes returned by the dialer (not agents)", "dialingStrategyConfiguration.retryPeriodField.suffixPlural": "between attempts", "dialingStrategyConfiguration.retryStrategy.hint": "Choose how the dialer will retry to reach each record", "dialingStrategyConfiguration.phoneNumberSequence.betweenAttemptText": "between attempts on {{type}},", "dialingStrategyConfiguration.attemptsPerRecords.unitLabel": "attempt(s) per record.", "sidePanels.agentsAssociatedCampaigns.emptyMessage": "The agent is not assigned to any campaign", "dialingStrategyConfiguration.callingHoursField.label": "Calling hours", "sidePanels.recordLists.count": "{{count}} list", "listsConfiguration.doNotCallLists.title": "Do not call lists", "dialingStrategyConfiguration.systemDispositions.toggle.hint": "Customize attempts based on specific call outcomes returned by the dialer (not agents)", "dispositionsConfiguration.statusAfterCallDescription.answeringMachineDetection": "Record state after an answering machine detection attempt", "dialingStrategyConfiguration.previewContext.topHint": "The Preview dialing mode enables a preview of the record's data in the Conversation App, while connecting the call.", "dialingStrategyConfiguration.retryPeriodField.dispositionPrefix": "By default, follow these system dispositions rules:", "dialingStrategyConfiguration.maxSkipsPerRecordField.skipCount": "{{count}} skip", "dialingStrategyConfiguration.connectionTimeoutField.topHint": "Insert a time value between {{minConnectionTimeout}} and {{maxConnectionTimeout}} seconds for the agent to consume data until the call is connected.", "agentsConfiguration.count_plural": "{{count}} agents", "dispositionsConfiguration.retryPeriod.label.abandonment": "Retry period after abandoned attempts", "multipleTabErrorMessage": "There are {{errorCount}} errors", "dialingStrategyConfiguration.dialingOrder.fifo.hint": "The system will consume the record list from the first record to the last one", "dialingStrategyConfiguration.dialingOrder.hint": "Choose the order in which records will be consumed", "modeConfiguration.maxDialingRatioField.unitLabel": "calls per agent", "modeConfiguration.abandonmentTimeoutField.topHint": "Insert a number between 2 and 30 in seconds for the time after which the dialer automatically hangs up answered calls that haven't been connected to an agent", "sidePanels.agentsAssociatedCampaigns.emptyTitle": "No campaigns assigned", "modeConfiguration.dialingModeField.topDefaultHint": "How your audience will be reached", "stepper.goToStep": "Go to Step {{step}}", "sidePanels.doNotCallLists.emptyMessage": "Upload a do not call list to view it in the list\t", "dialingStrategyConfiguration.previewContext.thirdStep": "Skip the call", "dispositionsConfiguration.dispositions.busy": "Busy", "dialingStrategyConfiguration.phoneNumberSequence.call": "Call", "modeConfiguration.answeringMachineDetectionField.label": "Answering machine detection", "sidePanels.callerIds.editMode.tableHeader": "Caller ID", "listsConfiguration.doNotCallLists.titleDescription": "Add a single do not call list of entries to be scrubbed by this campaign\t", "listsConfiguration.recordLists.table.headers.name": "Name", "listsConfiguration.recordLists.table.headers.numberOfElementsInList": "Records in list", "dispositionsConfiguration.dispositions.invalidNumber": "Invalid number", "listsConfiguration.recordLists.title": "Record lists", "dispositionsConfiguration.dispositions.answeringMachineDetection": "Answering machine detection", "dispositionsConfiguration.dispositions.hangupBeforeConnection": "Hangup before connection", "dispositionsConfiguration.dispositions.noAnswer": "No-answer", "dispositionsConfiguration.statusAfterCallDescription.busy": "Record state after a busy attempt", "listsConfiguration.title": "Lists", "dispositionsConfiguration.statusAfterCallDescription.hangupBeforeConnection": "Record state after an hangup before connection attempt\t", "dialingStrategyConfiguration.callerIdField.NoFriendlyName": "No friendly name", "listsConfiguration.recordLists.table.tooltip.delete": "Remove this record list", "sidePanels.callerIds.editMode.friendlyName": "Friendly name", "modeConfiguration.dialingModeField.warningMessage": "It is not allowed to change the dialing mode after running the campaign. If you want to do so, please duplicate or create a new campaign", "sidePanels.callerIds.editMode.searchTooltip": "Type at least 3 characters", "singleTabErrorMessage": "{{field}} has an error", "modals.discardEditCampaign.title": "Discard changes?", "campaignCreatedSuccessTitle": "Campaign created", "dispositionsConfiguration.systemDispositions.agentHangupBeforeConnection": "Agent hangup before connection", "dispositionsConfiguration.systemDispositions.answeringMachineDetection": "Answering Machine Detection", "dispositionsConfiguration.systemDispositions.hangupBeforeConnection": "Hangup before connection", "dispositionsConfiguration.systemDispositions.invalidNumber": "Invalid number", "dispositionsConfiguration.systemDispositions.noAnswer": "No answer", "dialingStrategyConfiguration.connectionTimeoutField.unitLabel": "seconds", "dialingStrategyConfiguration.dialingStrategyField.automaticOption": "Automatic", "dialingStrategyConfiguration.dialingStrategyField.manualOption": "Manual", "sidePanels.callerIds.editMode.noResultsMessage": "Please make sure that you are searching for an exact number - partial matches will return no results - or an existing friendly name.", "modals.deleteCampaign.message": "This campaign will no longer be available to run. You will have to create it again.", "priorityConfiguration.title": "Campaign priority", "dispositionsConfiguration.dispositionHeaders.hangupBeforeConnection": "Hangup before connection system disposition", "dispositionsConfiguration.systemDispositions.attemptCount": "{{count}} attempt max.", "dialingStrategyConfiguration.ivrStudioFlowField.label": "Studio Flow", "agentsConfiguration.showAssignment": "See assignment details", "editCampaignLabel": "Edit campaign", "dispositionsConfiguration.dispositionHeaders.edit": "Edit name", "listsConfiguration.doNotCallLists.table.tooltip.delete": "Remove this do not call list", "agentsConfiguration.emptySelectedAgentsTitle": "No agents selected", "agentsConfiguration.emptySelectedAgentsMessage": "There are no agents selected to work on this campaign", "agentsConfiguration.emptyAgentsTitle": "No agents found", "sidePanels.doNotCallLists.searchEmptyMessage": "Try using different keywords or checking for typos", "sidePanels.doNotCallLists.searchEmptyTitle": "No lists found", "sidePanels.doNotCallLists.searchTooltip": "Type at least {{minLength}} characters", "sidePanels.recordLists.searchEmptyTitle": "No lists found", "dialingStrategyConfiguration.previewContext.stepsDescription": "The screen is automatically configured with List and Record data, and allows the agent to:", "dialingStrategyConfiguration.recordChain.topHint": "Multiple phone numbers per record call hierarchy configuration", "modals.editDisposition.title": "Edit disposition type", "agentsConfiguration.description": "Assign agents to work on the campaign", "sidePanels.recordLists.table.row": "{{formattedCount}} record", "listsConfiguration.description": "Define the audience of this campaign", "agentsConfiguration.tableHeaders.name": "Name", "campaignCreatedSuccessContent": "Campaign {campaignName} was created successfully", "dialingStrategyConfiguration.dialingOrder.lifo.label": "Last in First Out", "dialingStrategyConfiguration.dialingOrder.title": "Dialing order", "dispositionsConfiguration.dispositionHeaders.busy": "Busy system disposition", "dispositionsConfiguration.statusAfterCallDescription.invalidNumber": "Record state after an invalid number attempt", "dispositionsConfiguration.statusAfterCallDescription.noAnswer": "Record state after a no-answer attempt", "dispositionsConfiguration.systemDispositions.tableHeaders.disposition": "Disposition", "dispositionsConfiguration.systemDispositions.tableHeaders.type": "Type", "editNameButtonLabel": "Edit campaign name", "modeConfiguration.maxAbandonmentRateField.warningMessage": "For Campaigns targeting strictly regulated markets like US/UK, it is advisable to monitor the abandonment rate in the dashboard and stop the campaign if it goes beyond the maximum rate specified on the campaign", "dialingStrategyConfiguration.recordChain.error": "The record chaining configuration could not be fetched. This campaign will be configured without it", "sidePanels.doNotCallLists.count_plural": "{{count}} lists", "sidePanels.doNotCallLists.table.row": "{{formattedCount}} entry", "sidePanels.doNotCallLists.table.row_plural": "{{formattedCount}} entries", "dispositionsConfiguration.retryPeriod.label.busy": "Retry period after busy attempts", "sidePanels.recordLists.count_plural": "{{count}} lists", "dialingStrategyConfiguration.callingHoursField.timeOverlap": "Setting {{count}} overlaps with another set of times", "dialingStrategyConfiguration.callingHoursField.topHint": "Add the calling hours upon which the dialer will call records. The dialer will make calls based on the timezone of each record", "dialingStrategyConfiguration.description": "Configure the dialing strategy for this campaign", "dialingStrategyConfiguration.callerIdField.missingCallerIdError": "Please select at least one caller ID", "dialingStrategyConfiguration.retryPeriodField.label": "Default retry period", "dispositionsConfiguration.retryPeriod.label.noAnswer": "Retry period after no answer attempts", "dialingStrategyConfiguration.attemptsPerRecord.suffix": "No retries, call the next record", "reportingConfiguration.dashboardSubTitle": "Campaign dashboard", "dispositionsConfiguration.callDispositionsParagraph": "To configure the call dispositions for this campaign, perform the following steps in the <1>admin area of your Talkdesk account</1>:", "dispositionsConfiguration.maxAttemptsPerRecord.label.abandonment": "Max abandoned attempts per record", "listsConfiguration.recordLists.table.row": "{{formattedCount}} record", "dialingStrategyConfiguration.phoneNumberSequence.error": "The record chaining configuration could not be fetched. This campaign will be configured without it", "dialingStrategyConfiguration.dialingOrder.LIFO.hint": "The system will consume the record list from the last record to the first one", "modeConfiguration.answeringMachineDetectionField.topHint": "When enabled, the dialer will automatically disconnect calls that reach answering machines or voicemails", "dispositionsConfiguration.maxAttemptsPerRecord.label.noAnswer": "Max no answer attempts per record", "dialingStrategyConfiguration.callingHoursField.setting": "Setting", "dispositionsConfiguration.systemDispositions.outMaxAttempt": "The number cannot exceed the max attempts per record", "sidePanels.recordLists.table.column.columnTeamDefault": "No teams", "agentsConfiguration.tableHeaders.team": "Team", "dialingStrategyConfiguration.recordChain.title": "Record chaining strategy", "dialingStrategyConfiguration.maxAttemptsPerRecordField.attemptCount_plural": "{{count}} attempts", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.abandonment": "Insert a number between 1 and 100 for maximum number of abandoned call attempts the dialer will perform on each record in this campaign", "sidePanels.agentFilters.title": "Filters", "dialingStrategyConfiguration.maxSkipsPerRecordField.unitLabel": "skip(s)", "dialingStrategyConfiguration.studioFlow.requiredFieldWarning": "Please select a Studio Flow", "dialingStrategyConfiguration.phoneNumberSequence.waitBaseOnSystemDispositions": "Wait based on system dispositions rules", "sidePanels.agentFilters.dropdown.noResultsMessage": "No results found", "sidePanels.recordLists.searchTooltip": "Type at least {{minLength}} characters", "sidePanels.recordLists.search": "Search by list name", "sidePanels.doNotCallLists.search": "Search by list name", "sidePanels.recordLists.table.headers.numberOfElementsInList": "Records in list", "dialingStrategyConfiguration.phoneNumberSequence.callRecordPhoneNumber": "Call the record's phone numbers in this sequence", "dispositionsConfiguration.systemDispositions.attemptCountPlural": "{{count}} attempts max", "dialingStrategyConfiguration.callerIdField.noSipNumbersMessage": "You have no SIP numbers configured in your account\t", "dialingStrategyConfiguration.callerIdField.placeholder": "Select caller ID", "dialingStrategyConfiguration.callerIdField.selectCallerIdLabel": "Select Caller IDs", "dialingStrategyConfiguration.callerIdField.topHint": "Select a single or multiple caller IDs for your campaign", "dialingStrategyConfiguration.callerIdField.addCallerIdLabel": "Add Caller ID", "dialingStrategyConfiguration.callerIdField.topHintLegacy": "You can choose one or more caller IDs for your campaign\t", "dialingStrategyConfiguration.maxAttemptsPerRecordField.label": "Global max. attempts per record", "sidePanels.agentsAssociatedCampaigns.count_plural": "{{count}} campaigns", "dialingStrategyConfiguration.dialingOrder.fifo.label": "First in First Out", "dialingStrategyConfiguration.dialingOrder.FIFO.label": "First in First Out", "dialingStrategyConfiguration.phoneNumberSequence.unitAfterMaxAttempts": "time(s)", "dialingStrategyConfiguration.dialingOrder.FIFO.hint": "The system will consume the record list from the first record to the last one", "dialingStrategyConfiguration.dialingOrder.LIFO.label": "Last in First Out", "sidePanels.recordLists.apply": "Apply", "modeConfiguration.maxDialingRatioField.label": "Max. dialing ratio", "modeConfiguration.maxDialingRatioField.topHint": "Insert a number between 1 and 10 for the maximum number of calls the dialer will launch per agent", "viewCampaignLabel": "View campaign", "sidePanels.recordLists.searchEmptyMessage": "Try using different keywords or checking for typos", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.busy": "Insert a number between 1 and 100 for the maximum number of busy call attempts the dialer will perform on each record in this campaign", "sidePanels.doNotCallLists.table.headers.name": "Name", "sidePanels.doNotCallLists.apply": "Apply", "listsConfiguration.doNotCallLists.table.headers.name": "Name", "dispositionsConfiguration.systemDispositions.busy": "Busy", "listsConfiguration.doNotCallLists.table.row": "{{formattedCount}} entry", "dispositionsConfiguration.maxAttemptsPerRecord.label.agentHangupBeforeConnection": "Max agent hangup before connection attempts per record", "agentsConfiguration.invalidSearchValueMessage": "We can't fetch your search results unless you type at least {{minInputSearchValue}} characters", "filters.ringGroups": "Ring groups", "dialingStrategyConfiguration.systemDispositions.title": "Advanced options", "agentsConfiguration.tableHeaders.ringGroups": "Ring groups", "agentsConfiguration.tableOptions.buttonTitle": "Table options", "modals.removeAssignedList.noRecordListWarning": "Campaigns can't be started without a list", "agentsConfiguration.emptyAgentsMessage": "Please use different criteria to search or filter for agents", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.agentHangupBeforeConnection": "Insert a number between 1 and 100 for maximum number of agent hangup before connection call attempts the dialer will perform on each record in this campaign", "sidePanels.agentsAssociatedCampaigns.alt": "No campaigns associated", "dialingStrategyConfiguration.callingHoursField.timeSelectError": "Please choose an end time later than the start time", "modeConfiguration.maxRingTimeField.label": "Max. ring time", "listsConfiguration.recordLists.titleDescription": "Add the lists of records to be reached by this campaign", "dispositionsConfiguration.dispositionHeaders.answeringMachineDetection": "Answering machine detection system disposition", "modals.discardEditCampaign.message": "Your new changes will not be saved", "listsConfiguration.doNotCallLists.buttonLabel": "Add DNC lists", "dispositionsConfiguration.title": "Dispositions", "agentsConfiguration.tableColumns.columnTeamDefault": "No teams", "agentsConfiguration.count": "{{count}} agent", "sidePanels.doNotCallLists.count": "{{count}} list", "modals.discardEditCampaign.cancelButton": "Keep editing", "reportingConfiguration.reportSubTitleDescription": "Information about calls launched by the dialer", "sidePanels.recordLists.title": "Record lists", "sidePanels.callerIds.editMode.search": "Search for phone number or friendly name", "sidePanels.callerIds.editMode.noCallerIdsForAccountMessage": "There are no Caller IDs available for this account. Please add a SIP number to your account in order to use it on a campaign.", "dialingStrategyConfiguration.recordChain.subTitle": "Multiple phone numbers per record call hierarchy configuration\t", "dialingStrategyConfiguration.dialingStrategyField.topHint": "Strategy on how records will be dialed", "dialingStrategyConfiguration.previewContext.firstStep": "Consume record data while waiting for call connection", "dialingStrategyConfiguration.callingHoursField.timeErrorAndWeekdaysError": "Please select at least one day and choose an end time later than the start time", "reportingConfiguration.dashboardSubTitleDescription": "Summary of the campaign progress", "modeConfiguration.maxAbandonmentRateField.topHint": "Insert a number between 1 and 100 percent for answered calls that are disconnected when there are no agents available", "modeConfiguration.maxRingTimeField.unitLabel": "seconds", "dispositionsConfiguration.callDispositionsTitle": "Call dispositions", "dialingStrategyConfiguration.callingHoursField.validationMessage": "Please select valid calling hours with at least one weekday and a time frame from 12:00 AM to 11:59 PM", "agentsConfiguration.tableOptions.contentDescription": "Items per page", "dialingStrategyConfiguration.maxAttemptsPerRecordField.topHint": "Insert a number between 1 and 100 for the maximum number of times the dialer will try to call each record in this campaign", "dialingStrategyConfiguration.retryPeriodField.topHint": "Insert the minimum time the dialer will wait before trying to call a record again", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.noAnswer": "nsert a number between 1 and 100 for maximum number of no answer call attempts the dialer will perform on each record in this campaign\t", "dispositionsConfiguration.retryPeriod.label.answeringMachineDetection": "Retry period after answering machine detection attempts\t", "dispositionsConfiguration.retryPeriod.topHint.busy": "Insert the minimum time the dialer will wait in between busy call attempts", "dispositionsConfiguration.retryPeriod.topHint.noAnswer": "Insert the minimum time the dialer will wait in between no answer call attempts", "dispositionsConfiguration.callDispositionsText": "1. Create a ring group for this campaign, or reuse one from other campaign, in case you want to have the same dispositions for both campaigns.<br/>2. Create or reuse a disposition set for the ring group, with call direction “Outbound”.<br />3. Assign the ring group to all the agents working in this campaign.", "dispositionsConfiguration.dispositionHeaders.invalidNumber": "Invalid number system disposition", "dispositionsConfiguration.dispositionHeaders.noAnswer": "No-answer system disposition", "sidePanels.agentsAssociatedCampaigns.table.headers.campaignName": "Campaign name", "dialingStrategyConfiguration.callerIdField.label": "Caller IDs", "sidePanels.callerIds.viewMode.count": "{{count}} caller ID", "sidePanels.agentsAssociatedCampaigns.count": "{{count}} campaign", "listsConfiguration.doNotCallLists.table.headers.team": "Team", "sidePanels.doNotCallLists.table.headers.team": "Team", "dialingStrategyConfiguration.attemptsPerRecord.prefix": "Never exceed", "modeConfiguration.maxRingTimeField.topHint": "Insert a number between 6 and 120 in seconds for the time after which unanswered calls will be automatically disconnected", "dispositionsConfiguration.statusAfterCallDescription.abandonment": "Record state after an abandoned attempt", "dialingStrategyConfiguration.attemptsPerRecordPlural.unitLabel": "attempts per record.", "listsConfiguration.recordLists.buttonLabel": "Add record lists", "dialingStrategyConfiguration.maxAttemptsPerRecordField.unitLabel": "attempt(s)", "dialingStrategyConfiguration.phoneNumberSequence.unitsAfterAttempt": "times", "modeConfiguration.maxAbandonmentRateField.label": "Max. abandonment rate", "dialingStrategyConfiguration.phoneNumberSequence.tooltip": "To edit label, go to Configurations", "dialingStrategyConfiguration.maxSkipsPerRecordField.topHint": "Insert a value between 0 and 20 for the number of times an agent can skip a call. Applicable to Re<PERSON>ue and Reschedule.", "agentsConfiguration.invalidSearchValueTitle": "Type {{minInputSearchValue}} characters to start searching", "agentsConfiguration.filters.availableFilters.ringGroups": "Ring groups", "agentsConfiguration.filters.clearButton": "Clear All", "dialingStrategyConfiguration.recordChain.label": "Record chaining strategy", "dispositionsConfiguration.systemDispositions.title": "System dispositions", "dispositionsConfiguration.withCustomRules": "With custom rules", "dispositionsConfiguration.maxAttemptsPerRecord.label.answeringMachineDetection": "Max answering machine detection attempts per record\t", "sidePanels.doNotCallLists.emptyTitle": "No Do not call lists yet", "sidePanels.doNotCallLists.table.headers.numberOfElementsInList": "Number of entries", "sidePanels.doNotCallLists.title": "Do not call lists", "sidePanels.recordLists.emptyMessage": "Upload a record list to view it in the list", "modeConfiguration.dialingModeField.type": "{{dialingMode}} dialing", "dialingStrategyConfiguration.previewContext.label": "Preview context", "filters.ringGroups.plural": "ring groups", "dispositionsConfiguration.systemDispositions.attemptsMax": "attempt(s) max", "sidePanels.callerIds.editMode.close": "Close", "dispositionsConfiguration.systemDispositions.abandonment": "Abandoned", "dialingStrategyConfiguration.maxSkipsPerRecordField.label": "Maximum skips per record", "dispositionsConfiguration.dispositions.abandonment": "Abandoned", "dialingStrategyConfiguration.dialingStrategyField.label": "Strategy type", "goToStep": "Go to step", "listsConfiguration.recordLists.table.headers.team": "Team", "dialingStrategyConfiguration.title": "Dialing strategy", "dispositionsConfiguration.maxAttemptsPerRecord.topHint.answeringMachineDetection": "Insert a number between 1 and 100 for maximum number of answering machine detection call attempts the dialer will perform on each record in this campaign", "dispositionsConfiguration.statusAfterCallDescription.agentHangupBeforeConnection": "Record state after an agent hangup before connection attempt", "sidePanels.doNotCallLists.table.column.columnTeamDefault": "No teams", "modals.deleteCampaign.cancelButton": "Cancel", "modals.discardCreateCampaign.cancelButton": "Cancel", "modeConfiguration.dialingModeField.topHint": "Select how your audience will be reached", "dialingStrategyConfiguration.callingHoursField.deleteTime": "Remove this date and time"}