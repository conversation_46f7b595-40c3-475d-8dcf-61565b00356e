{"users-settings.users-settings:create-user.last_name.placeholder": "<PERSON><PERSON>", "users-settings.users-settings:create-user.description": "Creates a new user", "conversation-app.trigger-voice-conversation.name": "Call", "conversation-app.trigger-voice-conversation.target.name": "Phone number", "contextual-help.search-kb.description": "Search input in Knowledge Base", "conversation-app.trigger-voice-conversation.target.placeholder": "****** 555 555", "atlas-dummy-app.dummy-protocol.dummy-param.description": "This is a dummy param representing a phone number, for testing purposes in the atlas-demo app", "users-settings.users-settings:create-user.last_name.description": "The last name of the user", "atlas-dummy-app.dummy-protocol.name": "Dummy <PERSON>", "atlas-dummy-app.dummy-protocol.dummy-param.placeholder": "****** 567 890", "atlas-dummy-app.dummy-protocol.dummy-param.name": "<PERSON><PERSON>", "contacts.contacts:search.name": "Search contacts", "contacts.contacts:search.keyword.placeholder": "<PERSON><PERSON>", "conversation-app.trigger-voice-conversation.target.description": "The phone number to call", "users-settings.users-settings:create-user.email.name": "Email", "users-settings.users-settings:create-user.email.description": "The email of the user", "users-settings.users-settings:create-user.name": "Create user", "users-settings.users-settings:create-user.first_name.placeholder": "<PERSON>", "atlas-dummy-app.dummy-protocol.description": "This is a dummy protocol for testing purposes in the atlas-demo app", "contacts.contacts:search.description": "Searches for a contact, and shows the results", "users-settings.users-settings:create-user.first_name.name": "First name", "contacts.contacts:search.keyword.description": "The search term", "users-settings.users-settings:create-user.first_name.description": "The first name of the user", "conversation-app.trigger-voice-conversation.description": "Make a phone call to a number using the conversation app", "users-settings.users-settings:create-user.last_name.name": "Last name", "contacts.contacts:search.keyword.name": "Contact", "contextual-help.search-kb.name": "Search Talkdesk Knowledge Base", "contextual-help.search-kb.query.placeholder": "How can we help?", "contextual-help.search-kb.query.name": "Topic to search", "users-settings.users-settings:create-user.email.placeholder": "<EMAIL>", "contextual-help.search-kb.query.description": "The topic to be searched in the Knowledge Base"}