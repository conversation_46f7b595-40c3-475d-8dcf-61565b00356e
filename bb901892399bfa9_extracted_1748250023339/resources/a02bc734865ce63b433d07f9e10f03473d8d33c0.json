{"runtime.session_engine.start_new_session.paragraph_1": "Talkdesk is already open in another tab or device. Click\n<strong>Start new session</strong> to open Conversations here instead.", "chat_content.search_placeholder": "Search this conversation", "extension_points.ui_blocks.filter_mode.filter_by": "FILTER BY", "countries_dropdown.countries.mu": "Mauritius", "extension_points.ui_blocks.components.countries_dropdown.countries.cf": "Central African Republic", "tab_host.failure.retry": "Retry", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.ring_groups": "Search for ring groups...", "extension_points.ui_blocks.interaction_actions.buttons.mute.false.text": "Mute", "countries_dropdown.countries.gr": "Greece", "extension_points.ui_blocks.components.countries_dropdown.countries.mq": "Martinique", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.text": "If you end the chat and this contact sends a new message, it will count as a new conversation.", "extension_points.commands.topics.default": "via {{topic}}", "extension_points.ui_blocks.components.caller_number_dropdown.error_message": "Couldn't load the list.", "extension_points.agent_status.busy": "On a Call", "extension_points.notifications.outbound_email_action": "Couldn't start new Em<PERSON>", "message_input.hint": "Enter to add a new line | Shift + Enter to send", "countries_dropdown.countries.ee": "Estonia", "extension_points.ui_blocks.email_content.to": "to", "extension_points.ui_blocks.components.countries_dropdown.countries.nr": "Nauru", "extension_points.ui_blocks.components.email_editor.from": "From", "extension_points.notifications.actions.agent_call.cancel": "Couldn't close the panel", "extension_points.commands.conference.removing": "Removing...", "countries_dropdown.countries.bm": "Bermuda", "caller_number_dropdown.placeholder": "Select a number", "extension_points.ui_blocks.components.countries_dropdown.countries.aw": "Aruba", "message_input.placeholder": "Message {{name}}", "panel.targets_list.error_states.ring_groups.message": "There was a problem loading the ring groups list, please try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.no": "Norway", "extension_points.commands.subtitles.consultation_offer": "Regarding {{name}}", "extension_points.ui_blocks.components.countries_dropdown.countries.im": "Isle of Man", "conversations_panel.group.all_conversations": "All conversations", "countries_dropdown.countries.va": "Vatican City", "extension_points.ui_blocks.components.email_editor.bcc": "Bcc", "extension_points.notifications.actions.transfer.start": "Couldn't start the transfer", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.cancelText": "Cancel", "countries_dropdown.countries.sz": "Swaziland", "conversations_panel.tabs.active_tab.failed_loading_channel_message_ay11_one": "Couldn't load one channel", "extension_points.ui_blocks.components.countries_dropdown.countries.ge": "Georgia", "panel.targets_list.tabs.labels.ring_groups": "Ring groups", "runtime.session_engine.session_override.title": "This session has ended", "conversations_panel.tabs.active_tab.sla_policy_popup.seconds": "{{seconds}}s", "conversations_panel.tabs.active_tab.description_other": "{{count}} conversations", "extension_points.ui_blocks.components.countries_dropdown.countries.om": "Oman", "extension_points.notifications.actions.accept_call": "Couldn't accept the call", "panel.targets_list.transfer_target.search_hint": "Type at least 3 characters to start searching", "extension_points.templates.empty.title": "No activity yet", "commands.live_chat.visitor": "Visitor", "extension_points.commands.subtitles.email_requested_conversation_with_name": "To {{name}} <{{email}}>", "extension_points.ui_blocks.interaction_info_consultation.active_states.hold_participant_conversation": "Talking", "extension_points.notifications.outbound_sms_unselected_sender.message": "Please select an outbound sender number", "extension_points.ui_blocks.trigger_outbound_actions.outbound_action": "Start Outbound", "conversations_panel.tabs.active_tab.sla_policy_popup.time_remaining_label": "Time to first response", "extension_points.ui_blocks.interaction_info.states.incoming_voice_consultation": "Incoming consultation", "countries_dropdown.countries.tv": "Tuvalu", "commands.subtitles.consultation_offer": "Regarding {{name}}", "extension_points.ui_blocks.interaction_actions.buttons.end_consultation.text": "End consultation", "extension_points.ui_blocks.filter_mode.internal_messages": "Internal messages", "dropdown.error_message": "Could not load.", "extension_points.ui_blocks.components.countries_dropdown.countries.ua": "Ukraine", "panel.targets_list.empty_states.no_search_results.title": "No results found", "extension_points.ui_blocks.panel.targets_list.tabs.labels.external_number": "External number", "extension_points.ui_blocks.components.countries_dropdown.countries.mh": "Marshall Islands", "conversations_panel.tabs.active_tab.sort_by_channel_option": "Channel", "extension_points.ui_blocks.components.countries_dropdown.countries.so": "Somalia", "countries_dropdown.countries.pm": "Saint Pierre and Miquelon", "core.notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "countries_dropdown.countries.tw": "Taiwan", "countries_dropdown.countries.th": "Thailand", "agent_status.available": "Available", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.confirmText": "Send to Inbox", "countries_dropdown.countries.al": "Albania", "conversations_panel.channels.email": "Email", "extension_points.ui_blocks.components.countries_dropdown.countries.io": "British Indian Ocean Territory", "notifications.actions.consultation.transfer_success_requested": "Call transferred successfully", "notifications.actions.transfer.cancel": "Couldn't cancel the transfer", "extension_points.ui_blocks.components.email_editor.touchpoints_placeholder": "Search...", "countries_dropdown.countries.cn": "China", "countries_dropdown.countries.mq": "Martinique", "countries_dropdown.countries.mx": "Mexico", "extension_points.ui_blocks.components.countries_dropdown.countries.af": "Afghanistan", "countries_dropdown.countries.uz": "Uzbekistan", "extension_points.ui_blocks.components.chat_content.virtual_agent": "Virtual agent", "extension_points.ui_blocks.email_content.forwarded": "Forwarded", "extension_points.notifications.actions.consultation.transfer_success_requested": "Call transferred successfully", "extension_points.ui_blocks.components.countries_dropdown.countries.sy": "Syria", "extension_points.ui_blocks.components.countries_dropdown.countries.rs": "Serbia", "countries_dropdown.countries.uy": "Uruguay", "extension_points.ui_blocks.components.countries_dropdown.countries.gu": "Guam", "extension_points.ui_blocks.interaction_actions.buttons.leave.text": "End call", "notifications.actions.consultation.transfer_success_received": "The call was transferred to you", "extension_points.ui_blocks.components.email_editor.reply": "Reply", "extension_points.notifications.actions.transfer.failure.title": "Unable to transfer the call", "extension_points.ui_blocks.components.countries_dropdown.countries.li": "Liechtenstein", "extension_points.ui_blocks.interaction_actions.buttons.dtmf.text": "Keypad", "countries_dropdown.countries.re": "Réunion", "extension_points.ui_blocks.components.countries_dropdown.countries.pl": "Poland", "countries_dropdown.countries.pa": "Panama", "countries_dropdown.countries.wf": "Wallis and Futuna", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_and_minutes": "{{hours}}h {{minutes}}m", "extension_points.notifications.actions.elevate_to_call": "Starting call", "extension_points.ui_blocks.components.countries_dropdown.countries.sa": "Saudi Arabia", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.title": "Call {{contact_person}}", "runtime.ui_engine.error.message": "Sorry, there was a problem loading this page. If the problem persists, contact your administrator", "extension_points.ui_blocks.components.chat_content.message_input.internal_chat": "Internal message", "extension_points.ui_blocks.components.chat_content.message_input.external_chat": "Message", "interaction_info.states.conversation_voice_call": "Talking", "extension_points.ui_blocks.interaction_info.states.hold_conversation_call": "On hold", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.text": "Send to Inbox", "extension_points.ui_blocks.components.countries_dropdown.countries.yt": "Mayotte", "extension_points.ui_blocks.interaction_actions.buttons.more.text": "More", "countries_dropdown.countries.zw": "Zimbabwe", "extension_points.ui_blocks.components.countries_dropdown.countries.de": "Germany", "notifications.actions.establish.title": "Couldn't establish the call", "extension_points.ui_blocks.interaction_actions.buttons.record.false.text": "Start recording", "runtime.session_engine.start_new_session.title": "Start new session", "extension_points.notifications.actions.send_email_failure.title": "Couldn't send the email", "extension_points.ui_blocks.components.chat_content.chat_message.location_message_content": "Latitude: {{latitude}}, Longitude: {{longitude}}", "extension_points.ui_blocks.components.countries_dropdown.countries.ke": "Kenya", "extension_points.ui_blocks.components.countries_dropdown.countries.lr": "Liberia", "countries_dropdown.countries.mt": "Malta", "extension_points.ui_blocks.interaction_info.states.live_chat_conversation_info": "Chatting on Chat", "countries_dropdown.countries.me": "Montenegro", "extension_points.ui_blocks.components.countries_dropdown.countries.pg": "Papua New Guinea", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.confirmText": "End chat", "extension_points.ui_blocks.components.countries_dropdown.countries.co": "Colombia", "extension_points.ui_blocks.components.email_editor.Heading1": "Heading 1", "extension_points.ui_blocks.components.countries_dropdown.countries.tl": "East Timor", "core.notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "extension_points.ui_blocks.interaction_info.states.dial_voice_call": "Dialing", "extension_points.ui_blocks.components.chat_content.message_input.button": "Send", "countries_dropdown.countries.ky": "Cayman Islands", "extension_points.notifications.actions.send_to_queue.success": "<PERSON><PERSON> sent to Inbox successfully", "countries_dropdown.countries.tc": "Turks and Caicos Islands", "extension_points.notifications.actions.contact_left": "Contact ended the call", "extension_points.ui_blocks.components.countries_dropdown.countries.kh": "Cambodia", "extension_points.notifications.start_outbound_sms_error.title": "An error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.ar": "Argentina", "extension_points.ui_blocks.components.countries_dropdown.countries.pn": "Pitcairn", "extension_points.commands.live_chat.visitor": "Visitor", "extension_points.commands.unknown_resource": "Unknown Resource", "countries_dropdown.countries.il": "Israel", "conversations_panel.nav_bar.unread.messages": "{{count}} unread", "conversations_panel.new_conversation.title": "New conversation", "countries_dropdown.countries.gl": "Greenland", "countries_dropdown.countries.pg": "Papua New Guinea", "extension_points.ui_blocks.components.countries_dropdown.countries.il": "Israel", "extension_points.ui_blocks.components.chat_content.message_input.discard": "Discard", "countries_dropdown.countries.to": "Tonga", "conversations_panel.tabs.active_tab.sort_by_newest_assignment_option": "Newest assignments", "extension_points.ui_blocks.components.countries_dropdown.countries.nz": "New Zealand", "extension_points.notifications.conference.guest_connected.title": "Added to conference", "extension_points.ui_blocks.components.countries_dropdown.countries.ws": "Samoa", "extension_points.ui_blocks.components.countries_dropdown.countries.tc": "Turks and Caicos Islands", "core.notifications.session_override.title": "Couldn't start a new session", "countries_dropdown.countries.pr": "Puerto Rico", "notifications.actions.transfer.failure.message": "Please try again or select another destination", "extension_points.commands.tags.dedicated": "Dedicated", "chat_message.format_not_supported": "Message format is not supported", "extension_points.notifications.start_new_email_conversation_not_allowed.title": "Couldn’t start new Email", "extension_points.ui_blocks.components.countries_dropdown.countries.do": "Dominican Republic", "countries_dropdown.countries.sm": "San Marino", "agent_status.custom_busy": "Busy", "extension_points.agent_status.custom_busy": "Busy", "extension_points.ui_blocks.components.countries_dropdown.countries.mm": "Myanmar", "extension_points.ui_blocks.components.countries_dropdown.countries.fj": "Fiji", "notifications.permission.microphone.title": "The application needs to have microphone access", "runtime.session_engine.browser.mode.only.title": "Desktop access is disabled", "countries_dropdown.countries.cu": "Cuba", "countries_dropdown.countries.gw": "Guinea-Bissau", "extension_points.notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "dropdown.options.loading": "Loading", "extension_points.ui_blocks.components.email_editor.cc": "Cc", "caller_number_dropdown.store.refine_search": "{{count}} more numbers. Use the search to refine further.", "countries_dropdown.countries.ga": "Gabon", "commands.topics.consultation": "Consultation", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.cancelText": "Cancel", "conversations_panel.tabs.active_tab.sort_by_contact_option": "Contact (A-Z)", "extension_points.ui_blocks.panel.targets_list.tabs.labels.agents": "Agents", "extension_points.ui_blocks.components.countries_dropdown.countries.mp": "Northern Mariana Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.mo": "Macau", "extension_points.ui_blocks.email_content.hide_details": "Hide details", "extension_points.notifications.actions.consultation.transfer_failure.message": "Please try again", "countries_dropdown.countries.gu": "Guam", "notifications.actions.target.empty_ring_group.message": "Please contact your administrator", "countries_dropdown.countries.ar": "Argentina", "extension_points.ui_blocks.components.countries_dropdown.countries.sn": "Senegal", "countries_dropdown.countries.nf": "Norfolk Island", "extension_points.ui_blocks.components.countries_dropdown.countries.bi": "Burundi", "countries_dropdown.countries.ag": "Antigua and Barbuda", "extension_points.ui_blocks.components.countries_dropdown.countries.br": "Brazil", "countries_dropdown.countries.is": "Iceland", "extension_points.notifications.actions.reject_live_chat.failure.title": "Couldn't reject the Chat", "extension_points.ui_blocks.components.countries_dropdown.countries.dj": "Djibouti", "notifications.actions.agent_call.failure": "Couldn't establish the call", "countries_dropdown.countries.ph": "Philippines", "extension_points.ui_blocks.components.countries_dropdown.countries.ck": "Cook Islands", "countries_dropdown.countries.mm": "Myanmar", "extension_points.ui_blocks.components.phone_input.error_message": "Invalid number", "extension_points.ui_blocks.components.countries_dropdown.countries.lv": "Latvia", "extension_points.notifications.actions.conference.add_guest_failure.title": "Couldn't add guest", "notifications.outbound_sms_action": "Couldn't send the SMS", "extension_points.ui_blocks.interaction_info_consultation.active_states.hold_participant_dial": "Dialing", "conversations_panel.tabs.active_tab.sort_by_button_tooltip": "Sort by", "conversations_panel.tabs.active_tab.sla_policy_popup.more_than_99_days": "99+ d", "extension_points.ui_blocks.components.countries_dropdown.countries.tr": "Turkey", "extension_points.ui_blocks.interaction_actions.buttons.start_consultation.text": "Consult", "extension_points.ui_blocks.components.countries_dropdown.countries.ph": "Philippines", "extension_points.ui_blocks.components.countries_dropdown.countries.ga": "Gabon", "extension_points.ui_blocks.components.email_editor.placeholder": "Type something...", "countries_dropdown.countries.mc": "Monaco", "extension_points.ui_blocks.components.countries_dropdown.countries.np": "Nepal", "extension_points.ui_blocks.components.countries_dropdown.countries.lu": "Luxembourg", "notifications.actions.unmute": "Couldn't unmute", "extension_points.ui_blocks.filter_mode.external_messages": "External messages", "extension_points.ui_blocks.components.countries_dropdown.countries.ai": "<PERSON><PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.sk": "Slovakia", "countries_dropdown.countries.gq": "Equatorial Guinea", "extension_points.ui_blocks.components.countries_dropdown.countries.sh": "Saint Helena", "countries_dropdown.countries.mr": "Mauritania", "extension_points.ui_blocks.interaction_info.accessibility.remove_guest": "<PERSON><PERSON><PERSON> guest", "extension_points.ui_blocks.components.countries_dropdown.countries.bj": "Benin", "extension_points.ui_blocks.components.countries_dropdown.countries.ie": "Ireland", "notifications.actions.auto_answer": "Couldn't acknowledge auto-answer", "conversations_panel.tabs.queue_tab.loading": "Loading...", "extension_points.ui_blocks.components.countries_dropdown.countries.pk": "Pakistan", "panel.subtitles.consultation": "Consult an agent, ring group or number", "extension_points.ui_blocks.components.countries_dropdown.countries.tj": "Tajikistan", "extension_points.ui_blocks.components.chat_content.message_input.hint": "Enter to add a new line | Shift + Enter to send", "countries_dropdown.countries.ss": "South Sudan", "countries_dropdown.countries.cy": "Cyprus", "notifications.unknown.title": "An unexpected error occurred", "notifications.actions.dial_number.title": "Couldn't initiate the call", "interaction_info_consultation.userStates.hold_contact_conversation": "Talking", "extension_points.ui_blocks.components.countries_dropdown.countries.bz": "Belize", "extension_points.ui_blocks.components.countries_dropdown.countries.me": "Montenegro", "extension_points.ui_blocks.interaction_actions.buttons.submit_wrap_up.text": "Submit", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_and_minutes_overdue": "Overdue {{hours}}h {{minutes}}m", "conversations_panel.tabs.active_tab.sort_by_most_recent_option": "Most recent", "countries_dropdown.countries.af": "Afghanistan", "extension_points.notifications.unrecoverable_errors.prepare_add_guest.title": "Couldn’t add guest", "extension_points.channels.email.display_name": "Email", "notifications.actions.consultation.end.title": "Couldn't end the consultation", "extension_points.notifications.file_upload.file_upload_error": "Couldn’t upload file. Please try again.", "countries_dropdown.countries.au": "Australia", "notifications.actions.transfer.success": "Call transferred successfully", "extension_points.ui_blocks.outbound_actions.caller_heading.title": "Outbound on:", "extension_points.ui_blocks.components.countries_dropdown.countries.ca": "Canada", "extension_points.ui_blocks.components.countries_dropdown.countries.al": "Albania", "countries_dropdown.countries.bz": "Belize", "extension_points.ui_blocks.panel.titles.consultation": "Consultation", "extension_points.ui_blocks.panel.targets_list.error_states.agents.title": "Could not load the agents list", "countries_dropdown.countries.sb": "Solomon Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.bt": "Bhutan", "countries_dropdown.countries.la": "Laos", "extension_points.notifications.invalid_number": "Invalid number", "extension_points.ui_blocks.components.countries_dropdown.countries.am": "Armenia", "extension_points.notifications.file_upload.only_file_format_are_allowed": "The file type you are trying to upload is not supported.", "extension_points.notifications.cti_not_connected.message": "Some capabilities won't work", "extension_points.ui_blocks.components.countries_dropdown.countries.la": "Laos", "countries_dropdown.countries.mw": "Malawi", "conversations_panel.nav_bar.open": "Open the conversations panel", "countries_dropdown.countries.om": "Oman", "extension_points.ui_blocks.start_interaction_menu.view_more": "More...", "countries_dropdown.countries.bn": "Brunei", "extension_points.ui_blocks.components.countries_dropdown.countries.ru": "Russia", "countries_dropdown.countries.td": "Chad", "countries_dropdown.countries.si": "Slovenia", "notifications.actions.accept_live_chat.failure.title": "Couldn't accept the live chat", "translator.hide_sent": "<PERSON><PERSON> sent", "countries_dropdown.countries.ki": "Kiribati", "countries_dropdown.countries.mz": "Mozambique", "extension_points.ui_blocks.email_content.cancel": "Cancel", "conversations_panel.channels.sms": "SMS", "extension_points.ui_blocks.components.countries_dropdown.countries.as": "American Samoa", "translator.translation_failed": "Failed to translate", "extension_points.notifications.permission.autoplay.title": "Ringtone is muted", "extension_points.ui_blocks.components.countries_dropdown.countries.cv": "Cape Verde", "commands.unknown_agent": "Unknown Agent", "runtime.session_engine.take_back_session.label": "Take back session", "extension_points.ui_blocks.components.countries_dropdown.countries.th": "Thailand", "extension_points.ui_blocks.outbound_actions.button_labels.start_digital_whatsapp_conversation": "Start WhatsApp", "countries_dropdown.countries.sy": "Syria", "countries_dropdown.countries.pl": "Poland", "extension_points.ui_blocks.components.countries_dropdown.countries.bf": "Burkina Faso", "extension_points.notifications.actions.target.empty_ring_group.title": "No colleagues assigned to this queue", "extension_points.channels.dg_whatsapp.display_name": "WhatsApp", "extension_points.ui_blocks.interaction_actions.buttons.park.text": "Park", "extension_points.notifications.actions.consultation.failure.message": "Please try again or select another destination", "extension_points.ui_blocks.components.countries_dropdown.extension_label": "Dial an extension", "countries_dropdown.countries.sc": "Seychelles", "extension_points.ui_blocks.interaction_actions.buttons.accept.text": "Accept call", "countries_dropdown.countries.do": "Dominican Republic", "countries_dropdown.countries.kr": "South Korea", "extension_points.ui_blocks.components.countries_dropdown.countries.ye": "Yemen", "extension_points.ui_blocks.components.countries_dropdown.countries.at": "Austria", "extension_points.ui_blocks.components.countries_dropdown.countries.dk": "Denmark", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_overdue": "Overdue {{hours}}h", "extension_points.ui_blocks.components.countries_dropdown.countries.sb": "Solomon Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.hu": "Hungary", "countries_dropdown.countries.qa": "Qatar", "extension_points.notifications.actions.send_to_flow.success": "Conversation transferred successfully", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.agents": "Search for agents...", "extension_points.ui_blocks.components.caller_number_dropdown.placeholder": "Select a number", "extension_points.ui_blocks.components.countries_dropdown.countries.gd": "Grenada", "trigger_outbound_actions.outbound_action": "Start Outbound", "countries_dropdown.countries.br": "Brazil", "countries_dropdown.countries.se": "Sweden", "countries_dropdown.countries.de": "Germany", "extension_points.ui_blocks.components.countries_dropdown.countries.an": "Netherlands Antilles", "countries_dropdown.countries.nu": "Niue", "countries_dropdown.countries.gb": "United Kingdom", "notifications.actions.cancel_call": "Couldn't cancel the call", "extension_points.notifications.file_upload.file_total_size_exceeds_maximum": "The total size of the attached files exceeds the maximum size allowed.", "extension_points.ui_blocks.components.caller_number_dropdown.error_load_more_message": "Error loading more items. Try again.", "extension_points.templates.empty.message": "Start a new conversation or assign one yourself", "extension_points.ui_blocks.components.countries_dropdown.countries.bb": "Barbados", "countries_dropdown.countries.my": "Malaysia", "notifications.actions.stop_recording": "Couldn't stop the recording", "countries_dropdown.countries.cd": "Congo", "dates.yesterday": "Yesterday", "notifications.actions.end_text_conversation.success": "Chat ended successfully", "countries_dropdown.countries.na": "Namibia", "countries_dropdown.countries.cv": "Cape Verde", "extension_points.ui_blocks.email_content.via": "via", "extension_points.ui_blocks.components.countries_dropdown.countries.tk": "Tokelau", "interaction_info.states.dial_voice_call": "Dialing", "countries_dropdown.countries.tr": "Turkey", "extension_points.ui_blocks.email_content.cc": "Cc", "test.polyglot": "Testing Polyglot Sync - Runtime", "extension_points.ui_blocks.components.countries_dropdown.countries.md": "Moldova", "extension_points.ui_blocks.components.countries_dropdown.countries.az": "Azerbaijan", "extension_points.commands.subtitles.email_received_conversation": "From {{email}}", "countries_dropdown.countries.ml": "Mali", "countries_dropdown.countries.kw": "Kuwait", "extension_points.ui_blocks.components.email_editor.reply_all": "Reply All", "countries_dropdown.countries.li": "Liechtenstein", "agent_status.away": "Away", "extension_points.ui_blocks.components.email_editor.Heading2": "Heading 2", "extension_points.agent_status.away": "Away", "countries_dropdown.countries.tl": "Timor-Leste", "notifications.unrecoverable_errors.offer.title": "Couldn't answer the call", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.confirmText": "Call", "extension_points.ui_blocks.interaction_actions.buttons.end_conference.text": "End conference", "extension_points.ui_blocks.components.interaction_header.actions.recording": "REC", "panel.targets_list.empty_states.no_agents.title": "No agents found", "countries_dropdown.countries.pw": "<PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.gm": "Gambia", "countries_dropdown.countries.lr": "Liberia", "extension_points.ui_blocks.components.countries_dropdown.countries.zm": "Zambia", "panel.titles.consultation": "Consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.ir": "Iran", "extension_points.ui_blocks.components.countries_dropdown.countries.tt": "Trinidad and Tobago", "countries_dropdown.countries.lk": "Sri Lanka", "countries_dropdown.countries.gy": "Guyana", "extension_points.ui_blocks.interaction_info_consultation.targetStates.hold_contact_dial": "Dialing", "extension_points.notifications.actions.reject_call": "Couldn't reject the call", "extension_points.ui_blocks.email_content.auto_reply_sent_to": "Auto-reply sent to {{name}}", "notifications.actions.consultation.transfer_failure.title": "Couldn't transfer the call", "extension_points.notifications.actions.wrap_up.submit.title": "Couldn't submit the wrap-up", "extension_points.ui_blocks.components.countries_dropdown.countries.td": "Chad", "notifications.actions.reject_live_chat.failure.title": "Couldn't reject the live chat", "extension_points.notifications.actions.end_text_conversation.failure.title": "Couldn't end the chat", "extension_points.notifications.actions.dial_number.title": "Couldn't initiate the call", "extension_points.ui_blocks.components.countries_dropdown.countries.et": "Ethiopia", "extension_points.channels.live_chat.display_name": "Cha<PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.cc": "Cocos Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.tv": "Tuvalu", "countries_dropdown.countries.kg": "Kyrgyzstan", "countries_dropdown.countries.py": "Paraguay", "extension_points.ui_blocks.components.email_editor.subject": "Subject", "countries_dropdown.countries.ms": "Montserrat", "extension_points.ui_blocks.components.countries_dropdown.countries.hn": "Honduras", "extension_points.ui_blocks.components.countries_dropdown.countries.wf": "Wallis and Futuna", "extension_points.ui_blocks.interaction_info.states.incoming_voice_conference": "Incoming conference", "countries_dropdown.countries.mp": "Northern Mariana Islands", "translator.hide_original": "Hide original", "notifications.active_text_conversation_error.title": "Couldn't load SMS", "panel.targets_list.tabs.labels.external_number": "External number", "extension_points.ui_blocks.components.countries_dropdown.countries.sx": "Sint Maarten", "countries_dropdown.countries.hr": "Croatia", "extension_points.ui_blocks.interaction_actions.buttons.hold.false.text": "Hold", "extension_points.notifications.emergency.no_address.title": "No emergency address defined", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_and_seconds_overdue": "Overdue {{minutes}}m {{seconds}}s", "extension_points.channels.apple_messages_for_business.display_name": "Apple Messages For Business", "extension_points.ui_blocks.components.countries_dropdown.countries.gl": "Greenland", "extension_points.ui_blocks.components.countries_dropdown.countries.kg": "Kyrgyzstan", "outbound_actions.button_labels.start_sms_conversation": "Start SMS", "extension_points.ui_blocks.components.countries_dropdown.countries.cz": "Czech Republic", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.text": "This chat will be sent back to the Inbox for agent reassignment.", "countries_dropdown.countries.ad": "Andorra", "extension_points.ui_blocks.components.countries_dropdown.countries.se": "Sweden", "countries_dropdown.countries.ug": "Uganda", "countries_dropdown.countries.ro": "Romania", "extension_points.notifications.conference.guest_connected.message": "Guest was successfully added to conference", "conversations_panel.tabs.active_tab.title": "Active", "notifications.actions.transfer.start": "Couldn't start the transfer", "extension_points.notifications.file_upload.maximum_tips": "Maximum file size: {{size}}", "conversations_panel.tabs.empty_state.message": "Your active conversations will be displayed here", "interaction_info.states.live_chat_conversation_info": "Chatting on Live chat", "countries_dropdown.countries.hu": "Hungary", "extension_points.ui_blocks.components.caller_number_dropdown.search_options.placeholder": "Search...", "notifications.actions.park": "Couldn't park this conversation", "translator.show_original": "Show original", "extension_points.ui_blocks.components.caller_number_dropdown.retry": "Retry", "extension_points.notifications.actions.consultation.switch_call.title": "Couldn't switch calls", "extension_points.ui_blocks.components.countries_dropdown.countries.mz": "Mozambique", "countries_dropdown.countries.aw": "Aruba", "extension_points.ui_blocks.interaction_actions.buttons.mute.true.text": "Unmute", "countries_dropdown.countries.sd": "Sudan", "extension_points.ui_blocks.components.countries_dropdown.countries.bh": "Bahrain", "extension_points.ui_blocks.interaction_actions.buttons.start_add_guest.text": "Add guest", "notifications.actions.consultation.transfer_failure.message": "Please try again", "extension_points.notifications.active_text_conversation_error.title": "Couldn't load SMS", "extension_points.ui_blocks.components.countries_dropdown.countries.ch": "Switzerland", "countries_dropdown.countries.ht": "Haiti", "extension_points.ui_blocks.components.countries_dropdown.countries.za": "South Africa", "extension_points.notifications.unrecoverable_errors.offer.title": "Couldn't answer the call", "extension_points.notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "countries_dropdown.countries.ke": "Kenya", "countries_dropdown.countries.zm": "Zambia", "extension_points.ui_blocks.interaction_info.states.incoming_voice_agent_call": "Agent to agent call", "countries_dropdown.countries.at": "Austria", "countries_dropdown.countries.ng": "Nigeria", "extension_points.ui_blocks.components.caller_number_dropdown.loading": "Loading", "extension_points.channels.sms.display_name": "SMS", "notifications.actions.establish.message": "An unexpected problem occurred while trying to establish this call", "extension_points.ui_blocks.components.countries_dropdown.countries.ht": "Haiti", "extension_points.ui_blocks.components.countries_dropdown.countries.in": "India", "extension_points.ui_blocks.components.countries_dropdown.countries.sm": "San Marino", "extension_points.ui_blocks.interaction_info.states.whatsapp_conversation_info": "Chatting on WhatsApp", "extension_points.channels.whatsapp.display_name": "WhatsApp", "countries_dropdown.countries.so": "Somalia", "extension_points.ui_blocks.interaction_actions.buttons.hold.true.text": "Unhold", "extension_points.ui_blocks.components.chat_content.automatic_message": "Automatic message", "extension_points.ui_blocks.email_content.hide_quoted_text": "<PERSON><PERSON> quoted text", "extension_points.ui_blocks.components.countries_dropdown.countries.tg": "Togo", "extension_points.notifications.file_upload.file_delete_error": "File delete error", "extension_points.ui_blocks.components.countries_dropdown.countries.vn": "Vietnam", "interaction_info.states.incoming_voice_call": "Inbound call", "countries_dropdown.countries.ly": "Libya", "extension_points.ui_blocks.components.countries_dropdown.countries.qa": "Qatar", "panel.titles.agent_call": "Call an agent", "extension_points.ui_blocks.components.countries_dropdown.countries.fi": "Finland", "extension_points.ui_blocks.components.countries_dropdown.countries.id": "Indonesia", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle_with_action": "If the problem persists, contact your administrator.", "runtime.renderer.mode.error.reconnection.notification": "Please reconnect the host widget and try again.", "countries_dropdown.countries.fi": "Finland", "notifications.unrecoverable_errors.offer_outbound.title": "Couldn't load the dialing screen", "extension_points.ui_blocks.components.chat_content.message_input.shortcut_key_tips": "Enter to add a new line | Cmd/Shift + Enter to send", "extension_points.notifications.dialed_number_has_already_an_ongoing_conversation_error": "The dialed number already has an ongoing conversation", "extension_points.ui_blocks.panel.subtitles.consultation": "Consult an agent, ring group or number", "extension_points.ui_blocks.components.countries_dropdown.countries.vc": "Saint Vincent and the Grenadines", "core.notifications.session_override.message": "Please try again. If the problem persists, contact your administrator\t", "notifications.actions.agent_call.cancel": "Couldn't close the panel", "extension_points.notifications.agent_status_synced.message": "Agent status synced with omnichannel", "extension_points.ui_blocks.components.countries_dropdown.countries.pa": "Panama", "countries_dropdown.countries.hn": "Honduras", "extension_points.ui_blocks.components.countries_dropdown.countries.cy": "Cyprus", "countries_dropdown.countries.tk": "Tokelau", "extension_points.ui_blocks.components.countries_dropdown.countries.cg": "Republic of the Congo", "extension_points.ui_blocks.components.countries_dropdown.countries.ag": "Antigua and Barbuda", "extension_points.notifications.file_upload.maximum_file": "The file you are trying to upload exceeds the maximum size allowed.", "extension_points.ui_blocks.components.caller_number_dropdown.load_more": "Load more elements...", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.message": "There was a problem loading the ring groups list, please try again.", "error.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.bs": "Bahamas", "extension_points.ui_blocks.components.caller_number_dropdown.store.default_friendly_name": "<PERSON><PERSON><PERSON>", "extension_points.notifications.actions.unmute": "Couldn't unmute", "extension_points.ui_blocks.components.countries_dropdown.countries.us": "United States", "countries_dropdown.countries.km": "Comoros", "agent_status.offline": "Offline", "extension_points.ui_blocks.components.countries_dropdown.countries.ni": "Nicaragua", "extension_points.ui_blocks.components.countries_dropdown.countries.it": "Italy", "extension_points.notifications.start_outbound_digital_whatsapp_error.title": "An error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.my": "Malaysia", "countries_dropdown.countries.ca": "Canada", "extension_points.ui_blocks.components.countries_dropdown.countries.mg": "Madagascar", "panel.titles.blind_transfer": "Blind transfer", "countries_dropdown.countries.et": "Ethiopia", "commands.subtitles.consultation_offer_outbound": "Consulting {{name}}", "conversations_panel.tabs.empty_state.title": "No conversations", "interaction_header.actions.recording": "REC", "extension_points.notifications.actions.session_override.message": "Please try again. If the problem persists, contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.lt": "Lithuania", "notifications.actions.end_call.title": "Couldn't end the call", "extension_points.ui_blocks.components.countries_dropdown.countries.dm": "Dominica", "countries_dropdown.countries.nz": "New Zealand", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.title": "No results found", "session_override.title": "The session has ended", "countries_dropdown.countries.sg": "Singapore", "extension_points.ui_blocks.components.countries_dropdown.countries.vu": "Vanuatu", "extension_points.ui_blocks.components.countries_dropdown.countries.by": "Belarus", "extension_points.ui_blocks.components.countries_dropdown.countries.xk": "Kosovo", "notifications.outbound_sms_unselected_sender.message": "Please select an outbound sender number", "extension_points.commands.subtitles.conference_received_conversation": "Multiple participants", "interaction_info.states.incoming_live_chat_conversation": "Incoming Live chat", "countries_dropdown.countries.bf": "Burkina Faso", "extension_points.ui_blocks.components.countries_dropdown.countries.bq": "Caribbean Netherlands", "extension_points.commands.unknown_contact": "Unknown Contact", "extension_points.ui_blocks.components.countries_dropdown.countries.mw": "Malawi", "notifications.unrecoverable_errors.offer_consultation.title": "Couldn't answer the consultation", "countries_dropdown.countries.mn": "Mongolia", "countries_dropdown.countries.vn": "Vietnam", "notifications.actions.hold": "Couldn't hold", "extension_points.ui_blocks.components.countries_dropdown.countries.sj": "Svalbard and <PERSON>", "countries_dropdown.countries.dz": "Algeria", "runtime.dialog_engine.dialog.closed.button": "Close", "extension_points.ui_blocks.components.countries_dropdown.countries.gy": "Guyana", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.title": "End chat?", "extension_points.notifications.actions.transfer.failure.message": "Please try again or select another destination", "countries_dropdown.countries.sr": "Suriname", "extension_points.ui_blocks.interaction_info.states.emergency_voice_call": "Emergency call", "countries_dropdown.countries.ls": "Lesotho", "countries_dropdown.countries.ck": "Cook Islands", "extension_points.commands.topics.emergency_call": "Emergency call", "extension_points.commands.subtitles.conference": "Multiple participants", "extension_points.ui_blocks.interaction_info.states.facebook_conversation_info": "Chatting on Facebook Messenger", "extension_points.ui_blocks.components.email_editor.Paragraph": "Paragraph", "extension_points.ui_blocks.components.countries_dropdown.countries.nl": "Netherlands", "countries_dropdown.countries.mk": "Macedonia", "notifications.unrecoverable_errors.offer_outbound.message": "Please wait for the call to be picked up. If this problem persists, please contact your administrator", "extension_points.channels.fbm.display_name": "Facebook Messenger", "countries_dropdown.countries.tm": "Turkmenistan", "extension_points.ui_blocks.components.countries_dropdown.countries.ad": "Andorra", "countries_dropdown.countries.bh": "Bahrain", "countries_dropdown.countries.lu": "Luxembourg", "extension_points.ui_blocks.components.countries_dropdown.countries.kr": "South Korea", "runtime.session_engine.notification.override_session_request_error.title": "Couldn’t start a new session", "extension_points.ui_blocks.components.countries_dropdown.countries.cu": "Cuba", "extension_points.ui_blocks.components.countries_dropdown.countries.lk": "Sri Lanka", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.cancelText": "Cancel", "extension_points.ui_blocks.components.chat_content.message_input.placeholder_internal": "This message will be visible to teammates only... \n Use @ to mention people", "countries_dropdown.countries.er": "Eritrea", "extension_points.ui_blocks.components.email_editor.forward": "Forward", "conversations_panel.tabs.queue_tab.failure_widget.title": "Could not load contents", "countries_dropdown.countries.mf": "Saint <PERSON>", "countries_dropdown.countries.gm": "Gambia", "panel.targets_list.empty_states.no_ring_groups.title": "No ring groups found", "extension_points.ui_blocks.components.countries_dropdown.countries.gp": "Guadeloupe", "panel.subtitles.blind_transfer": "Transfer the call to an agent, ring group or number", "countries_dropdown.countries.ch": "Switzerland", "extension_points.notifications.unrecoverable_errors.prepare_agent_call.title": "Couldn't initiate the agent call", "extension_points.ui_blocks.interaction_actions.buttons.complete.text": "Auto-answer is enabled", "extension_points.notifications.permission.microphone.title": "The application needs to have microphone access", "extension_points.ui_blocks.interaction_info_consultation.buttons.switch_call.text": "Switch to this call", "extension_points.commands.subtitles.consultation_offer_outbound": "Consulting {{name}}", "conversations_panel.tabs.active_tab.failed_loading_channel_message_other": "Couldn't load {{channels}} channels", "extension_points.commands.unknown_email_subject": "(no subject)", "extension_points.ui_blocks.components.chat_content.chat_message.format_not_supported": "Message format is not supported", "extension_points.ui_blocks.components.countries_dropdown.countries.bw": "Botswana", "countries_dropdown.countries.sa": "Saudi Arabia", "dropdown.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.sr": "Suriname", "extension_points.ui_blocks.components.countries_dropdown.countries.ls": "Lesotho", "countries_dropdown.countries.cl": "Chile", "runtime.renderer.mode.error.lost.connection.notification": "Lost connection with Talkdesk Workspace host.", "countries_dropdown.countries.sk": "Slovakia", "notifications.actions.send_to_queue.success": "<PERSON><PERSON> sent to Queue successfully", "interaction_info.states.after_voice_call": "Wrap-up", "extension_points.ui_blocks.components.countries_dropdown.countries.is": "Iceland", "countries_dropdown.countries.sn": "Senegal", "extension_points.ui_blocks.components.caller_number_dropdown.search_options.character_limit_message": "Type at least 3 characters", "extension_points.ui_blocks.components.countries_dropdown.countries.km": "Comoros", "countries_dropdown.countries.sv": "El Salvador", "extension_points.ui_blocks.components.countries_dropdown.countries.fo": "Faroe Islands", "extension_points.ui_blocks.panel.targets_list.transfer_target.buttons.retry": "Retry", "extension_points.commands.subtitles.consultation_requested_conversation": "Consulting {{name}}", "countries_dropdown.countries.ws": "Samoa", "countries_dropdown.countries.kh": "Cambodia", "countries_dropdown.countries.ao": "Angola", "extension_points.ui_blocks.components.countries_dropdown.countries.eg": "Egypt", "extension_points.ui_blocks.components.chat_content.reachability_status.contact_connected": "Contact has entered the chat at {{time}}", "extension_points.agent_status.available": "Available", "notifications.dialed_number_has_already_an_ongoing_conversation_error.message": "The dialed number already has an ongoing conversation", "extension_points.ui_blocks.components.countries_dropdown.countries.fm": "Micronesia", "extension_points.ui_blocks.components.email_editor.email_address_error": "The email address in the \"{{field}}\" field was not recognized", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle_without_action": "Please reload the app or refresh the page. If the problem persists, contact your administrator.", "conversations_panel.tabs.active_tab.sla_policy_popup.days_and_hours_overdue": "Overdue {{days}}d {{hours}}h", "extension_points.ui_blocks.outbound_actions.button_labels.start_agent_voice_conversation": "Call an agent", "conversations_panel.tabs.active_tab.description_one": "{{count}} conversation", "panel.targets_list.empty_states.no_agents.message": "There are no agents to transfer this call to.", "extension_points.channels.google_business_messages.display_name": "Google Business Messages", "outbound_actions.caller_heading.title": "Outbound on:", "countries_dropdown.countries.kp": "North Korea", "extension_points.ui_blocks.components.caller_number_dropdown.store.refine_search": "{{count}} more numbers. Use the search to refine further.", "conversations_panel.nav_bar.close": "Close the conversations panel", "notifications.dialed_number_has_already_a_conversation_in_queue_error.message": "The dialed number already number has a conversation on <PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.bd": "Bangladesh", "extension_points.notifications.unknown.title": "An unexpected error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.ve": "Venezuela", "extension_points.ui_blocks.components.countries_dropdown.countries.ug": "Uganda", "extension_points.notifications.actions.conference.remove_guest_success.title": "Removed from conference", "extension_points.ui_blocks.components.countries_dropdown.countries.dz": "Algeria", "panel.targets_list.transfer_target.store.refine_search": "{{count}} more agents. Use the search to refine further.", "countries_dropdown.countries.tt": "Trinidad and Tobago", "extension_points.ui_blocks.tabs.empty_state_message": "There are no tabs configured for this conversation", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.text": "This chat will be available during the call.", "error.message": "Sorry, there was a problem loading this page. If the problem persists, contact your administrator", "notifications.actions.accept_call": "Couldn't accept the call", "runtime.session_engine.desktop.mode.only.paragraph_1": "To use Conversations App, please use Talkdesk desktop app. Conversations app access via web browser is disabled by your account administrator. Please reach out the administrator for more information.", "countries_dropdown.countries.kz": "Kazakhstan", "extension_points.ui_blocks.components.countries_dropdown.countries.kn": "Saint Kitts and Nevis", "countries_dropdown.countries.gp": "Guadeloupe", "countries_dropdown.countries.ne": "Niger", "core.notifications.permission_microphone.action.button": "How to enable", "notifications.error_persist_contact_administrator_try_again": "Please try again. If this problem persists, please contact your administrator", "agent_status.after_call_work": "After Call Work", "extension_points.notifications.actions.agent_call.unavailable": "Couldn't start the call. Agent is unavailable", "notifications.actions.send_to_queue.failure.title": "Couldn't send the chat to <PERSON>ue", "countries_dropdown.countries.io": "British Indian Ocean Territory", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.text": "Are you sure you want to dismiss the wrap-up stage? You will lose any changes you made.", "countries_dropdown.countries.sx": "Sint Maarten", "core.notifications.session_already_exist.message": "Please ensure you have only one active Conversations app while using other Talkdesk apps", "extension_points.ui_blocks.tabs.right_control": "<PERSON>roll tabs forward", "notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "extension_points.notifications.outbound_digital_whatsapp_unselected_sender.message": "Please select an outbound sender number", "extension_points.commands.unknown_participant": "Unknown Participant", "countries_dropdown.countries.bj": "Benin", "extension_points.ui_blocks.panel.targets_list.error_states.agents.message": "There was a problem loading the agents list, please try again.", "countries_dropdown.countries.lb": "Lebanon", "countries_dropdown.countries.mv": "Maldives", "extension_points.ui_blocks.components.email_editor.select_outbound_id": "Select outbound ID", "countries_dropdown.countries.mo": "Macau", "interaction_info.states.incoming_voice_consultation": "Incoming consultation", "extension_points.ui_blocks.components.email_editor.no_contacts_found": "No contacts found", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.title": "Dismiss wrap-up?", "conversations_panel.tabs.active_tab.description_plural": "{{count}} conversations", "extension_points.ui_blocks.components.countries_dropdown.countries.mu": "Mauritius", "countries_dropdown.countries.fm": "Micronesia", "countries_dropdown.countries.md": "Moldova", "extension_points.ui_blocks.components.countries_dropdown.countries.pr": "Puerto Rico", "notifications.actions.reject_call": "Couldn't reject the call", "start_new_session.paragraph_2": "Unsaved changes will be lost and any call in progress will be automatically disconnected.", "notifications.actions.contact_switch": "Couldn't change to the selected contact", "countries_dropdown.countries.ni": "Nicaragua", "agent_status.busy": "On a Call", "extension_points.notifications.actions.start_recording": "Couldn't start the recording", "caller_number_dropdown.store.default_friendly_name": "<PERSON><PERSON><PERSON>", "runtime.session_engine.browser.mode.only.paragraph_1": "To use Conversations App, please use Talkdesk on your web browser. Conversations app access via desktop app is disabled by your account administrator. Please reach out the administrator for more information.", "countries_dropdown.countries.bb": "Barbados", "countries_dropdown.countries.jm": "Jamaica", "extension_points.notifications.actions.stop_recording": "Couldn't stop the recording", "extension_points.ui_blocks.components.countries_dropdown.countries.ss": "South Sudan", "extension_points.notifications.unrecoverable_errors.prepare_consultation.title": "Couldn't initiate the consultation", "extension_points.notifications.actions.send_email.failure.title": "Couldn't send the email", "core.notifications.permission_autoplay.title": "Ringtone is muted", "extension_points.ui_blocks.components.countries_dropdown.countries.cr": "Costa Rica", "extension_points.ui_blocks.email_content.wrote_message_by": "On {{date}} at {{time}} {{name}} wrote:", "conversations_panel.tabs.active_tab.sla_policy_popup.days": "{{days}}d", "extension_points.commands.subtitles.consultation_received_conversation": "Regarding {{name}}", "conversations_panel.tabs.queue_tab.failure_widget.message": "There was a problem while trying to load this tab's contents.", "tab_host.failure.title": "Could not load contents", "countries_dropdown.countries.ba": "Bosnia and Herzegovina", "extension_points.ui_blocks.components.countries_dropdown.countries.tn": "Tunisia", "extension_points.ui_blocks.components.chat_content.chat_message.unknown_message": "You’ve received an unsupported message", "notifications.actions.agent_call.unavailable": "Couldn't start the call. Agent is unavailable", "conversations_panel.tabs.active_tab.sla_policy_popup.title": "SLA Policy", "extension_points.ui_blocks.digital_interaction_with_tabs.tab.chat_tab": "Cha<PERSON>", "chat_content.automatic_message": "Automatic message", "extension_points.ui_blocks.components.phone_input.keypad_tooltip": "Keypad", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle": "Please reload the app or refresh the page. If the problem persists, contact your administrator.", "commands.unknown_resource": "Unknown Resource", "extension_points.notifications.unrecoverable_errors.wrap_up.message": "The wrap-up was automatically dismissed. If this problem persists, please contact your administrator", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_and_seconds": "{{minutes}}m {{seconds}}s", "extension_points.ui_blocks.components.countries_dropdown.countries.cd": "Democratic Republic of the Congo", "extension_points.ui_blocks.components.chat_content.message_input.template_shortcut_tips": "Enter to send", "conversations_panel.tabs.active_tab.sla_policy_popup.seconds_overdue": "Overdue {{seconds}}s", "conversations_panel.tabs.active_tab.sla_policy_popup.more_than_99_days_overdue": "Overdue 99+ d", "countries_dropdown.countries.tj": "Tajikistan", "runtime.dialog_engine.dialog.conversation.unavailable.title": "Conversations unavailable", "runtime.session_engine.session_override.paragraph_1": "A new session was started in another tab or device.", "extension_points.notifications.emergency.no_address.message": "If you call 911, additional costs will incur. Please edit your profile and add an emergency address", "extension_points.notifications.actions.conference.remove_guest_failure.message": "Please try again", "extension_points.ui_blocks.components.countries_dropdown.countries.sg": "Singapore", "conversations_panel.tabs.active_tab.failed_loading_channel_message_ay11_other": "Couldn't load more than one channel", "notifications.actions.consultation.cancel": "Couldn't cancel the consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.ee": "Estonia", "countries_dropdown.countries.pt": "Portugal", "extension_points.ui_blocks.components.email_editor.send": "Send", "countries_dropdown.countries.bl": "<PERSON>", "extension_points.notifications.actions.conference.add_guest_failure.message": "Please try again", "extension_points.notifications.file_upload.file_uploading": "File is being uploaded, do not operate", "extension_points.ui_blocks.email_content.reply": "Reply", "extension_points.ui_blocks.components.countries_dropdown.countries.sl": "Sierra Leone", "countries_dropdown.countries.sl": "Sierra Leone", "notifications.actions.consultation.contact_left_consultation": "Contact has left the call", "notifications.chat_error.title": "An unexpected error occurred", "countries_dropdown.countries.ye": "Yemen", "extension_points.ui_blocks.components.countries_dropdown.countries.mn": "Mongolia", "extension_points.notifications.actions.auto_answer": "Couldn't acknowledge auto-answer", "extension_points.notifications.actions.conference.remove_guest_failure.title": "Couldn’t remove guest", "caller_number_dropdown.error_message": "Couldn't load the list.", "countries_dropdown.countries.it": "Italy", "countries_dropdown.countries.dj": "Djibouti", "notifications.start_outbound_sms_error.title": "An error occurred", "extension_points.ui_blocks.components.email_editor.recipients": "Recipients", "notifications.unrecoverable_errors.wrap_up.message": "The wrap-up was automatically dismissed. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.mf": "Saint <PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.ae": "United Arab Emirates", "phone_input.error_message": "Invalid number", "countries_dropdown.extension_label": "Dial an extension", "extension_points.notifications.actions.consultation.failure.title": "Couldn't start the consultation call", "extension_points.notifications.dialed_number_has_already_a_conversation_in_queue_error": "The dialed number already has a conversation on Inbox", "countries_dropdown.countries.ae": "United Arab Emirates", "extension_points.notifications.start_new_email_conversation_not_allowed.message": "There are no outbound touchpoints available. Please contact your administrator.", "extension_points.notifications.connection.internet_unstable": "Your internet connection is unstable", "notifications.permission.microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "notifications.unrecoverable_errors.prepare_agent_call.title": "Couldn't initiate the agent call", "extension_points.commands.another_colleague": "another colleague", "runtime.session_engine.desktop.mode.only.title": "Browser access is disabled", "extension_points.ui_blocks.email_content.starting_different_conversation_tips": "You are trying to send a reply to a different contact person. This will start a different conversation with that contact person. Do you want to proceed?", "translator.translated": "Translated", "extension_points.ui_blocks.interaction_info_consultation.targetStates.hold_contact_conversation": "Talking", "extension_points.notifications.unrecoverable_errors.offer_consultation.title": "Couldn't answer the consultation", "notifications.actions.transfer.failure.title": "Unable to transfer the call", "panel.targets_list.transfer_target.search_placeholder": "Search for agents...", "extension_points.notifications.actions.wrap_up.dismiss": "Couldn't dismiss the wrap-up", "extension_points.notifications.agent_status_change_error.message": "Please make sure to manually update your status to the desired one", "countries_dropdown.countries.kn": "Saint Kitts and Nevis", "extension_points.ui_blocks.components.chat_content.message_input.placeholder": "Message {{name}}", "extension_points.agent_status.after_call_work": "After Call Work", "countries_dropdown.countries.fo": "Faroe Islands", "message_input.button": "Send", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.favorites": "Search favorites...", "countries_dropdown.countries.fr": "France", "countries_dropdown.countries.bg": "Bulgaria", "extension_points.notifications.actions.park": "Couldn't park this conversation", "extension_points.notifications.file_upload.file_deleting": "File is being deleting, do not operate", "extension_points.ui_blocks.components.countries_dropdown.countries.nc": "New Caledonia", "templates.empty.title": "No activity yet", "extension_points.ui_blocks.components.countries_dropdown.countries.st": "São Tomé and Príncipe", "countries_dropdown.countries.dk": "Denmark", "extension_points.notifications.unrecoverable_errors.offer_outbound.title": "Couldn't load the dialing screen", "runtime.ui_engine.error.retry": "Retry", "countries_dropdown.countries.mg": "Madagascar", "notifications.actions.session_override.title": "Couldn't start a new session", "extension_points.ui_blocks.email_content.start_different_conversation": "Start new conversation", "runtime.session_engine.notification.override_session_request_error.message": "Please try again. If the problem persists, please contact your administrator", "extension_points.ui_blocks.panel.targets_list.transfer_target.buttons.call": "Call", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.text": "End chat", "core.notifications.permission_autoplay.message": "To unmute your ringtone temporarily, click anywhere on the page. You can also enable the ringtone permanently on the link below.", "extension_points.ui_blocks.components.countries_dropdown.countries.ly": "Libya", "extension_points.ui_blocks.panel.subtitles.conference": "Add an agent or a number to a conference call", "extension_points.notifications.actions.send_to_queue.failure.title": "Couldn't send the chat to Inbox", "extension_points.ui_blocks.components.countries_dropdown.countries.jp": "Japan", "extension_points.ui_blocks.components.countries_dropdown.countries.cx": "Christmas Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.mt": "Malta", "extension_points.notifications.actions.establish.title": "Couldn't establish the call", "notifications.permission.autoplay.title": "Ringtone is muted", "translator.translating": "Translating...", "conversations_panel.tabs.active_tab.failed_loading_channel_message_one": "Couldn't load the {{channels}} channel", "extension_points.notifications.permission.microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "countries_dropdown.countries.bt": "Bhutan", "commands.unknown_contact": "Unknown Contact", "countries_dropdown.countries.tz": "Tanzania", "extension_points.commands.subtitles.conference_offer": "Multiple participants", "interaction_info_consultation.disconnected_tag": "Disconnected", "runtime.renderer.mode.error.content": "Please check if conversation-app widget is available!", "extension_points.ui_blocks.components.countries_dropdown.countries.ro": "Romania", "extension_points.ui_blocks.components.countries_dropdown.countries.ao": "Angola", "extension_points.ui_blocks.components.countries_dropdown.countries.gt": "Guatemala", "extension_points.ui_blocks.email_content.reply_all": "Reply All", "outbound_actions.button_labels.start_voice_conversation": "Call", "extension_points.ui_blocks.panel.titles.blind_transfer": "Blind transfer", "notifications.actions.session_override.message": "Please try again. If the problem persists, contact your administrator", "extension_points.ui_blocks.email_content.forward": "Forward", "extension_points.notifications.active_text_conversation_error.message": "Please refresh this page", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.title": "No favorites found", "notifications.unrecoverable_errors.wrap_up.title": "Couldn't load the wrap-up screen", "extension_points.notifications.permission.autoplay.message": "Allow the sound permission by clicking the lock icon next to the URL bar, click on Site Settings, and scroll down to find the Sound permissions. To unmute your ringtone temporarily, click anywhere on the page.", "notifications.invalid_number": "Invalid number", "dropdown.options.no_results": "Type at least 3 characters", "extension_points.ui_blocks.components.countries_dropdown.countries.gi": "Gibraltar", "panel.targets_list.empty_states.no_ring_groups.message": "There are no ring groups to transfer this call to.", "extension_points.notifications.actions.consultation.transfer_success_received": "The call was transferred to you", "extension_points.ui_blocks.panel.subtitles.blind_transfer": "Transfer the call to an agent, ring group or number", "extension_points.notifications.unrecoverable_errors.prepare_conference.title": "Couldn’t add guest", "conversations_panel.group.incoming": "Incoming", "extension_points.notifications.actions.cancel_call": "Couldn't cancel the call", "extension_points.ui_blocks.email_content.placeholder": "Message {{name}}", "countries_dropdown.countries.gn": "Guinea", "countries_dropdown.countries.cg": "Congo", "countries_dropdown.countries.id": "Indonesia", "extension_points.ui_blocks.panel.targets_list.tabs.labels.ring_groups": "Ring groups", "interaction_info.states.incoming_voice_agent_call": "Agent to agent call", "countries_dropdown.countries.rw": "Rwanda", "commands.topics.default": "via {{topic}}", "extension_points.ui_blocks.components.countries_dropdown.countries.ps": "Palestine", "extension_points.ui_blocks.components.countries_dropdown.countries.ng": "Nigeria", "panel.targets_list.transfer_target.buttons.call": "Call", "panel.targets_list.error_states.ring_groups.title": "Could not load the ring groups list", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_overdue": "Overdue {{minutes}}m", "extension_points.ui_blocks.components.countries_dropdown.countries.pt": "Portugal", "extension_points.notifications.unrecoverable_errors.wrap_up.title": "Couldn't load the wrap-up screen", "countries_dropdown.countries.ps": "Palestine", "extension_points.commands.unknown_guest": "Unknown Guest", "extension_points.ui_blocks.components.countries_dropdown.countries.sv": "El Salvador", "countries_dropdown.countries.vi": "U.S. Virgin Islands", "commands.subtitles.consultation_requested_conversation": "Consulting {{name}}", "extension_points.notifications.actions.transfer.cancel": "Couldn't cancel the transfer", "notifications.error_persist_contact_administrator": "If this problem persists, please contact your administrator", "countries_dropdown.countries.us": "United States", "countries_dropdown.countries.gf": "French Guiana", "notifications.actions.elevate_to_call": "Starting call", "countries_dropdown.countries.ua": "Ukraine", "runtime.session_engine.session_override.paragraph": "A new session was started in another tab or device. You can now close this one.", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.title": "Could not load the ring groups list", "extension_points.notifications.actions.consultation.start": "Couldn't start the consultation", "dropdown.options.load_more": "Load more elements...", "countries_dropdown.countries.cf": "Central African Republic", "tab_host.failure.message": "There was a problem while trying to load this tab's contents.", "countries_dropdown.countries.bs": "Bahamas", "countries_dropdown.countries.vu": "Vanuatu", "extension_points.ui_blocks.interaction_info.states.incoming_voice_call": "Inbound call", "conversations_panel.tabs.active_tab.sla_policy_popup.days_and_hours": "{{days}}d {{hours}}h", "extension_points.ui_blocks.interaction_actions.buttons.transfer_consultation.text": "Transfer", "notifications.unrecoverable_errors.prepare_blind_transfer.title": "Couldn't initiate the transfer", "extension_points.commands.subtitles.conference_requested_conversation": "Conference: {{names}}", "extension_points.ui_blocks.tabs.empty_state_title": "No content", "extension_points.notifications.unrecoverable_errors.offer_outbound.message": "Please wait for the call to be picked up. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.ec": "Ecuador", "countries_dropdown.countries.pf": "French Polynesia", "extension_points.notifications.file_upload.supported_files": "Supported files: {{formats}}", "extension_points.ui_blocks.components.countries_dropdown.countries.jm": "Jamaica", "extension_points.commands.subtitles.email_received_conversation_with_name": "From {{name}} <{{email}}>", "extension_points.ui_blocks.filter_mode.clear_filter": "Clear filter", "countries_dropdown.countries.ma": "Morocco", "extension_points.ui_blocks.components.countries_dropdown.countries.ne": "Niger", "extension_points.ui_blocks.components.countries_dropdown.countries.vg": "British Virgin Islands", "extension_points.notifications.actions.consultation.transfer_failure.title": "Couldn't transfer the call", "extension_points.ui_blocks.components.countries_dropdown.countries.nf": "Norfolk Island", "countries_dropdown.countries.bo": "Bolivia", "notifications.actions.target.empty_ring_group.title": "No agents assigned to this ring group", "extension_points.ui_blocks.components.countries_dropdown.countries.hr": "Croatia", "extension_points.ui_blocks.components.countries_dropdown.countries.je": "Jersey", "countries_dropdown.countries.cm": "Cameroon", "extension_points.notifications.chat_error.title": "An unexpected error occurred", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.text": "<PERSON><PERSON><PERSON>", "countries_dropdown.countries.no": "Norway", "notifications.actions.wrap_up.dismiss": "Couldn't dismiss the wrap-up", "countries_dropdown.countries.tn": "Tunisia", "extension_points.notifications.actions.target.empty_ring_group.message": "Please contact your administrator", "countries_dropdown.countries.gd": "Grenada", "commands.subtitles.consultation_received_conversation": "Regarding {{name}}", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.cancelText": "Back to chat", "countries_dropdown.countries.ve": "Venezuela", "runtime.session_engine.session_override.paragraph_2": "You can now close this one.", "extension_points.commands.topics.conference": "Conference", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.message": "There are no ring groups to transfer this call to.", "extension_points.ui_blocks.components.countries_dropdown.countries.iq": "Iraq", "runtime.dialog_engine.dialog.conversation.closed.title": "Conversation closed", "extension_points.notifications.outbound_sms_action": "Couldn't send the SMS", "extension_points.ui_blocks.outbound_actions.button_labels.start_sms_conversation": "Start SMS", "extension_points.notifications.actions.accept_live_chat.failure.title": "Couldn't accept the Chat", "conversations_panel.tabs.queue_tab.title": "Inbox", "conversations_panel.channels.voice": "Voice", "countries_dropdown.countries.bi": "Burundi", "countries_dropdown.countries.jp": "Japan", "extension_points.ui_blocks.components.email_editor.to": "To", "start_new_session.paragraph_1": "Conversations is already open in another tab or device. Click <strong>Start new session</strong> to open Conversations here instead.", "extension_points.ui_blocks.components.countries_dropdown.countries.bl": "<PERSON>", "notifications.actions.contact_left": "Contact ended the call", "conversations_panel.tabs.active_tab.description": "{{count}} conversation", "extension_points.ui_blocks.components.caller_number_dropdown.error_fetch_settings_message": "Couldn't load the settings.", "extension_points.ui_blocks.components.countries_dropdown.countries.vi": "U.S. Virgin Islands", "countries_dropdown.countries.ci": "Côte d’Ivoire", "countries_dropdown.countries.lv": "Latvia", "notifications.actions.consultation.failure.title": "Couldn't start the consultation call", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes": "{{minutes}}m", "extension_points.notifications.actions.agent_call.failure": "Couldn't establish the call", "extension_points.notifications.actions.establish.message": "An unexpected problem occurred while trying to establish this call", "extension_points.ui_blocks.components.countries_dropdown.countries.gf": "French Guiana", "extension_points.notifications.actions.contact_switch": "Couldn't change to the selected contact", "core.notifications.permission_microphone.title": "The application needs access to the microphone", "extension_points.commands.unknown_agent": "Unknown Agent", "countries_dropdown.countries.nc": "New Caledonia", "countries_dropdown.countries.lc": "Saint Lucia", "extension_points.ui_blocks.components.countries_dropdown.countries.cw": "Curaçao", "extension_points.ui_blocks.interaction_actions.buttons.reject_live_chat.text": "Reject", "panel.targets_list.transfer_target.buttons.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.pw": "<PERSON><PERSON>", "conversations_panel.title": "Conversations", "extension_points.ui_blocks.components.countries_dropdown.countries.ba": "Bosnia and Herzegovina", "extension_points.ui_blocks.components.countries_dropdown.countries.gh": "Ghana", "extension_points.agent_status.unknown": "Unknown status", "extension_points.ui_blocks.components.countries_dropdown.countries.gn": "Guinea", "extension_points.ui_blocks.components.email_editor.no_outbound_id": "No Outbound ID", "extension_points.ui_blocks.components.countries_dropdown.countries.be": "Belgium", "countries_dropdown.countries.pk": "Pakistan", "countries_dropdown.countries.be": "Belgium", "extension_points.ui_blocks.components.chat_content.search_placeholder": "Search this conversation", "countries_dropdown.countries.co": "Colombia", "countries_dropdown.countries.gh": "Ghana", "notifications.actions.agent_call.start": "Couldn't start the call", "extension_points.notifications.actions.click_to_call": "Starting call", "countries_dropdown.countries.iq": "Iraq", "extension_points.ui_blocks.components.countries_dropdown.countries.bg": "Bulgaria", "notifications.permission.autoplay.message": "Allow the sound permission by clicking the lock icon next to the URL bar, click on Site Settings, and scroll down to find the Sound permissions. To unmute your ringtone temporarily, click anywhere on the page.", "extension_points.ui_blocks.components.countries_dropdown.countries.bn": "Brunei", "conversations_panel.tabs.active_tab.sla_policy_popup.days_overdue": "Overdue {{days}}d", "countries_dropdown.countries.vc": "Saint Vincent and the Grenadines", "countries_dropdown.countries.tg": "Togo", "extension_points.ui_blocks.interaction_info.states.after_voice_call": "Wrap-up", "countries_dropdown.countries.am": "Armenia", "extension_points.ui_blocks.components.countries_dropdown.countries.nu": "Niue", "countries_dropdown.countries.cr": "Costa Rica", "extension_points.notifications.actions.conference.start": "Couldn't start adding guest", "interaction_info.states.hold_conversation_call": "On hold", "runtime.ui_engine.error.title": "Couldn't load the page", "countries_dropdown.countries.hk": "Hong Kong", "conversations_panel.tabs.active_tab.title_assigned": "Assigned to you", "notifications.actions.consultation.start": "Couldn't start the consultation", "extension_points.ui_blocks.interaction_actions.buttons.reject.text": "Reject", "extension_points.ui_blocks.components.countries_dropdown.countries.fr": "France", "countries_dropdown.countries.lt": "Lithuania", "countries_dropdown.countries.nl": "Netherlands", "panel.targets_list.error_states.agents.message": "There was a problem loading the agents list, please try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.ki": "Kiribati", "extension_points.ui_blocks.components.countries_dropdown.countries.uy": "Uruguay", "extension_points.ui_blocks.panel.targets_list.tabs.labels.favorites": "Favorites", "extension_points.ui_blocks.components.countries_dropdown.countries.gw": "Guinea-Bissau", "extension_points.notifications.file_upload.drag_tips": "Drag a file here", "countries_dropdown.countries.es": "Spain", "countries_dropdown.countries.pe": "Peru", "extension_points.notifications.actions.session_override.title": "Couldn't start a new session", "countries_dropdown.countries.ec": "Ecuador", "extension_points.notifications.actions.hold": "Couldn't hold", "countries_dropdown.countries.cw": "Curaçao", "tab_host.loading": "Loading...", "extension_points.ui_blocks.outbound_email.title": "New email", "extension_points.ui_blocks.components.email_editor.please_add_recipient": "Please add, at least, one recipient", "phone_input.placeholder": "Type or paste a number", "extension_points.ui_blocks.components.countries_dropdown.countries.pf": "French Polynesia", "extension_points.ui_blocks.components.countries_dropdown.countries.cn": "China", "core.notifications.permission_autoplay.action.button": "Learn more", "countries_dropdown.countries.np": "Nepal", "countries_dropdown.countries.vg": "British Virgin Islands", "countries_dropdown.countries.sh": "Saint Helena", "extension_points.ui_blocks.components.countries_dropdown.countries.si": "Slovenia", "extension_points.ui_blocks.interaction_actions.buttons.cancel.text": "End call", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.title": "No ring groups found", "dropdown.placeholder": "Please select...", "extension_points.ui_blocks.interaction_info.states.conversation_voice_call": "Talking", "extension_points.ui_blocks.email_content.cannot_forward_to_the_same_person": "Can't forward to the recipient that started the thread", "extension_points.ui_blocks.components.countries_dropdown.countries.ml": "Mali", "extension_points.ui_blocks.interaction_actions.buttons.start_transfer.text": "Blind transfer", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.confirmText": "Yes, dismiss", "runtime.renderer.mode.error.title": "Widget connection lost", "countries_dropdown.countries.ru": "Russia", "countries_dropdown.countries.ge": "Georgia", "extension_points.ui_blocks.components.countries_dropdown.countries.tz": "Tanzania", "extension_points.notifications.actions.consultation.contact_left_consultation": "Contact has left the call", "extension_points.ui_blocks.components.countries_dropdown.countries.cm": "Cameroon", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.title": "Could not load the favorites list", "extension_points.ui_blocks.components.countries_dropdown.countries.sd": "Sudan", "extension_points.ui_blocks.components.countries_dropdown.countries.kp": "North Korea", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.text": "Call", "extension_points.notifications.error_persist_contact_administrator": "If this problem persists, please contact your administrator", "panel.targets_list.error_states.agents.title": "Could not load the agents list", "extension_points.notifications.actions.unhold": "Couldn't unhold", "extension_points.ui_blocks.components.chat_content.reachability_status.contact_disconnected": "Contact has left the chat at {{time}}", "notifications.actions.start_recording": "Couldn't start the recording", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.message": "There are no agents to transfer this call to.", "extension_points.notifications.agent_status_change_error.title": "Couldn't update your status automatically", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.title": "No agents found", "core.notifications.permission_microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "extension_points.notifications.actions.agent_call.start": "Couldn't start the call", "extension_points.ui_blocks.components.email_editor.cannot_send_empty_message": "Can't send an empty message", "extension_points.ui_blocks.components.countries_dropdown.countries.ci": "Ivory Coast", "countries_dropdown.countries.fk": "Falkland Islands", "extension_points.ui_blocks.components.email_editor.send_tips": "Cmd + Enter to send", "countries_dropdown.countries.dm": "Dominica", "extension_points.commands.topics.agent_call": "Agent to agent call", "extension_points.ui_blocks.components.countries_dropdown.countries.kw": "Kuwait", "extension_points.ui_blocks.components.countries_dropdown.countries.sz": "Swaziland", "start_new_session.title": "Start new session", "extension_points.notifications.outbound_digital_whatsapp_action": "Couldn't send the WhatsApp", "conversations_panel.tabs.active_tab.sla_policy_popup.hours": "{{hours}}h", "countries_dropdown.countries.az": "Azerbaijan", "interaction_info.states.incoming_voice_transfer_call": "Transferred call", "countries_dropdown.countries.fj": "Fiji", "extension_points.ui_blocks.components.countries_dropdown.countries.lc": "Saint Lucia", "notifications.actions.end_text_conversation.failure.title": "Couldn't end the chat", "notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "countries_dropdown.countries.bw": "Botswana", "countries_dropdown.countries.za": "South Africa", "notifications.actions.mute": "Couldn't mute", "extension_points.ui_blocks.components.countries_dropdown.countries.mv": "Maldives", "countries_dropdown.countries.ir": "Iran", "extension_points.ui_blocks.components.countries_dropdown.countries.na": "Namibia", "countries_dropdown.countries.nr": "Nauru", "core.notifications.session_already_exist.title": "Conversations is open in another tab or device", "extension_points.ui_blocks.components.countries_dropdown.countries.bm": "Bermuda", "extension_points.ui_blocks.interaction_info_consultation.disconnected_tag": "Disconnected", "extension_points.ui_blocks.interaction_info.states.incoming_live_chat_conversation": "Incoming Chat", "extension_points.ui_blocks.components.countries_dropdown.countries.ms": "Montserrat", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.message": "There was a problem loading the favorites list, please try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.fk": "Falkland Islands", "countries_dropdown.countries.st": "São Tomé and Príncipe", "translator.show_sent": "Show sent", "outbound_actions.button_labels.start_agent_voice_conversation": "Call an agent", "error.title": "Couldn't load the page", "extension_points.ui_blocks.components.countries_dropdown.countries.to": "Tonga", "extension_points.notifications.actions.end_text_conversation.success": "Chat ended successfully", "extension_points.ui_blocks.components.chat_content.assign_message": "The conversation was assigned at {{time}}", "notifications.connection.internet_unstable": "Your internet connection is unstable", "extension_points.ui_blocks.interaction_info.states.text_conversation_info": "Chatting on SMS", "countries_dropdown.countries.ie": "Ireland", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.refine_search": "{{count}} more agents. Use the search to refine further.", "countries_dropdown.countries.by": "Belarus", "notifications.actions.consultation.failure.message": "Please try again or select another destination", "dropdown.error_load_more_message": "Error loading more items. Try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.va": "Vatican City", "extension_points.ui_blocks.components.countries_dropdown.countries.au": "Australia", "countries_dropdown.countries.mh": "Marshall Islands", "extension_points.ui_blocks.components.email_editor.shortcut_key_tips": "Cmd/Shift + Enter to send", "extension_points.ui_blocks.interaction_actions.buttons.accept_live_chat.text": "Accept <PERSON><PERSON>", "extension_points.ui_blocks.components.chat_content.chat_message.location_message_title": "You’ve received a location message", "countries_dropdown.countries.as": "American Samoa", "extension_points.ui_blocks.components.countries_dropdown.countries.mx": "Mexico", "extension_points.ui_blocks.components.countries_dropdown.countries.gg": "Guernsey", "notifications.active_text_conversation_error.message": "Please refresh this page", "notifications.actions.wrap_up.submit.title": "Couldn't submit the wrap-up", "conversations_panel.channels.live_chat": "Live chat", "extension_points.notifications.actions.conference.remove_guest_success.message": "Guest was successfully removed from conference", "extension_points.ui_blocks.components.countries_dropdown.countries.gq": "Equatorial Guinea", "extension_points.ui_blocks.components.countries_dropdown.countries.cl": "Chile", "countries_dropdown.countries.gt": "Guatemala", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_hint": "Type at least 3 characters to start searching", "extension_points.ui_blocks.components.countries_dropdown.countries.pm": "Saint Pierre and Miquelon", "extension_points.ui_blocks.panel.titles.conference": "Add guest", "extension_points.ui_blocks.panel.titles.agent_call": "Call an agent", "notifications.unrecoverable_errors.prepare_consultation.title": "Couldn't initiate the consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.hk": "Hong Kong", "extension_points.ui_blocks.components.countries_dropdown.countries.gb": "United Kingdom", "extension_points.ui_blocks.interaction_actions.buttons.record.true.text": "Stop recording", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.title": "Send to Inbox?", "extension_points.ui_blocks.email_content.show_details": "Show details", "extension_points.ui_blocks.components.countries_dropdown.countries.uz": "Uzbekistan", "extension_points.ui_blocks.email_content.bcc": "Bcc", "extension_points.ui_blocks.components.countries_dropdown.countries.rw": "Rwanda", "extension_points.ui_blocks.components.countries_dropdown.countries.kz": "Kazakhstan", "extension_points.ui_blocks.components.countries_dropdown.countries.py": "Paraguay", "countries_dropdown.countries.eg": "Egypt", "extension_points.ui_blocks.components.phone_input.placeholder": "Type or paste a number", "extension_points.ui_blocks.components.countries_dropdown.countries.gr": "Greece", "extension_points.agent_status.offline": "Offline", "commands.topics.agent_call": "Agent to agent call", "extension_points.ui_blocks.components.countries_dropdown.countries.lb": "Lebanon", "templates.empty.message": "Start a new conversation or assign one yourself", "extension_points.ui_blocks.outbound_actions.button_labels.start_voice_conversation": "Call", "conversations_panel.tabs.queue_tab.failure_widget.retry": "Try again", "conversations_panel.tabs.active_tab.sort_by_oldest_assignment_option": "Oldest assignments", "extension_points.ui_blocks.components.email_editor.please_select_outbound_id": "Please select outbound ID", "extension_points.ui_blocks.components.countries_dropdown.countries.er": "Eritrea", "extension_points.ui_blocks.components.countries_dropdown.countries.re": "Réunion", "extension_points.notifications.conference.guest_disconnected": "Guest has left the call", "extension_points.notifications.cti_not_connected.title": "CTI is not connected", "extension_points.ui_blocks.email_content.starting_different_conversation": "Starting a different conversation?", "extension_points.ui_blocks.interaction_info.states.incoming_voice_transfer_call": "Transferred call", "extension_points.ui_blocks.components.countries_dropdown.countries.bo": "Bolivia", "extension_points.ui_blocks.components.countries_dropdown.countries.sc": "Seychelles", "countries_dropdown.countries.ai": "<PERSON><PERSON><PERSON>", "conversations_panel.tabs.active_tab.failed_loading_channel_message_button": "Reload", "extension_points.ui_blocks.components.countries_dropdown.countries.ky": "Cayman Islands", "countries_dropdown.countries.bd": "Bangladesh", "extension_points.notifications.actions.consultation.end.title": "Couldn't end the consultation", "interaction_info.states.text_conversation_info": "Chatting on SMS", "extension_points.commands.topics.consultation": "Consultation", "extension_points.notifications.actions.mute": "Couldn't mute", "caller_number_dropdown.search_options.placeholder": "Search...", "extension_points.notifications.actions.send_fbm_failure.title": "Couldn't send the message", "extension_points.ui_blocks.components.countries_dropdown.countries.mr": "Mauritania", "extension_points.ui_blocks.components.countries_dropdown.countries.mc": "Monaco", "session_override.paragraph": "A new session was started in another tab or device. You can now close this one.", "extension_points.notifications.actions.consultation.cancel": "Couldn't cancel the consultation", "countries_dropdown.countries.jo": "Jordan", "countries_dropdown.countries.rs": "Serbia", "extension_points.notifications.actions.transfer.success": "Call transferred successfully", "countries_dropdown.countries.gi": "Gibraltar", "extension_points.ui_blocks.components.countries_dropdown.countries.ma": "Morocco", "extension_points.ui_blocks.interaction_info.states.email_conversation_info": "Emailing", "extension_points.ui_blocks.components.countries_dropdown.countries.mk": "Macedonia", "extension_points.ui_blocks.components.countries_dropdown.countries.tw": "Taiwan", "notifications.actions.unhold": "Couldn't unhold", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.message": "There are no favorite numbers configured on your account", "extension_points.ui_blocks.tabs.left_control": "<PERSON><PERSON> tabs back", "extension_points.ui_blocks.components.countries_dropdown.countries.pe": "Peru", "extension_points.ui_blocks.components.countries_dropdown.countries.es": "Spain", "countries_dropdown.countries.cz": "Czech Republic", "countries_dropdown.countries.bq": "Caribbean Netherlands", "extension_points.commands.subtitles.email_requested_conversation": "To {{email}}", "runtime.session_engine.start_new_session.paragraph_2": "Unsaved changes will be lost and any call in progress will be automatically disconnected.", "extension_points.notifications.unrecoverable_errors.prepare_blind_transfer.title": "Couldn't initiate the transfer", "extension_points.ui_blocks.components.chat_content.campaign_message.name": "Campaign", "extension_points.notifications.actions.end_call.title": "Couldn't end the call", "extension_points.ui_blocks.components.countries_dropdown.countries.zw": "Zimbabwe", "phone_input.keypad_tooltip": "Keypad", "extension_points.ui_blocks.components.countries_dropdown.countries.tm": "Turkmenistan", "extension_points.channels.voice.display_name": "Voice", "panel.targets_list.tabs.labels.agents": "Agents", "extension_points.ui_blocks.components.countries_dropdown.countries.eh": "Western Sahara", "extension_points.notifications.error_persist_contact_administrator_try_again": "Please try again. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.jo": "Jordan", "countries_dropdown.countries.in": "India"}