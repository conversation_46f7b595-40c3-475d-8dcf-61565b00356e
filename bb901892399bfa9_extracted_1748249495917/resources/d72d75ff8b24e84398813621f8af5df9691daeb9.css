/* ------------------------------
 * -- Main Global Styling -------
 * ------------------------------ */

.\_pendo-guide\_ {
  /* -- Overarching settings for all guides -- */
}

.\_pendo-guide-container\_ {
  /* -- Guide border, shadow, background, etc. -- */
}

.\_pendo-guide-container\_ .\_pendo-guide-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
}


/* ------------------------------
 * -- Lightbox Specific Styling -
 * ------------------------------ */

.\_pendo-guide-lb\_ {
  /* -- Overarching settings for Lightbox -- */
}

.\_pendo-guide-lb\_ .\_pendo-guide-container\_ {
  /* -- Guide border, shadow, background, etc. -- */
}

.\_pendo-guide-lb\_ .\_pendo-guide-container\_ .\_pendo-guide-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
}

.\_pendo-backdrop\_ {
  /* The backdrop displayed behind lightboxes */
  background-color: #031E2A;
  opacity: 0.85
}


/* ------------------------------
 * -- Tooltip Specific Styling --
 * ------------------------------ */

.\_pendo-guide-tt\_ {
  /* Overarching settings for tooltip */
}

.\_pendo-guide-tt\_ .\_pendo-guide-container\_ {
  /* -- Guide border, shadow, background, etc. -- */
}

.\_pendo-guide-tt\_ .\_pendo-guide-container\_ .\_pendo-guide-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
}

/* -- Pointer on the edge of the tooltip -- */
.\_pendo-guide-arrow\_ {
}

.\_pendo-guide-arrow-top\_ {
}

.\_pendo-guide-arrow-left\_ {
}

.\_pendo-guide-arrow-right\_ {
}

.\_pendo-guide-arrow-bottom\_ {
}

.\_pendo-guide-arrow-border-top\_ {
}

.\_pendo-guide-arrow-border-left\_ {
}

.\_pendo-guide-arrow-border-right\_ {
}

.\_pendo-guide-arrow-border-bottom\_ {
}


/* ------------------------------
 * -- Mobile Lightbox Styling ---
 * ------------------------------ */

.\_pendo-guide-mobile-lb\_ {
  /* Overarching settings for Mobile Lightbox */
}

.\_pendo-guide-mobile-lb\_ .\_pendo-guide-container\_ {
  /* -- Guide border, shadow, background, etc. -- */
}

.\_pendo-guide-mobile-lb\_ .\_pendo-guide-container\_ .\_pendo-guide-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
}

/* ------------------------------
 * -- Mobile Tooltip Styling ---
 * ------------------------------ */
 .\_pendo-guide-mobile-tt\_ {
  /* Overarching settings for mobile tooltip */
}

.\_pendo-guide-mobile-tt\_ .\_pendo-guide-container\_ {
  /* -- Guide border, shadow, background, etc. -- */
}

.\_pendo-guide-mobile-tt\_ .\_pendo-guide-container\_ .\_pendo-guide-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
}


/* ------------------------------
 * -- Launcher Styling ----------
 * ------------------------------ */

.\_pendo-launcher\_ {
  /* Overarching settings for Launcher */
  font-family: 'Open Sans', sans-serif;
}

.\_pendo-launcher\_ .\_pendo-guide-container\_ {
  /* -- Launcher border, shadow, background, etc. -- */
  /* -- (uses tooltip styles by default) -- */
  
  
  border-radius: 4px;
  box-shadow: none;
    border: none;
    box-shadow: 0px 5px 15px rgba(0,0,0,0.1),
    0px 15px 35px rgba(0,0,25,0.15),
    0px 50px 100px rgba(0,0,25,0.1);
}

.\_pendo-launcher\_ .\_pendo-launcher-content\_ {
  /* -- Content area -- use for font attributes, padding, etc. */
  
  border: none;
  
}
.\_pendo-close-guide\_ {
  transition: opacity 0.1s ease-in-out;
      position: absolute;
    top: 16px;
    right: 16px;
    line-height: 6px;
  font-weight: 400;
  color: black;
  opacity:0.25;
}

.\_pendo-close-guide\_:hover {

  opacity:1;
}
.\_pendo-launcher\_ .\_pendo-launcher-header\_ {
  /* -- Launcher header area -- */
  padding: 24px 16px 0px;
  margin: 0px;
  border: none;
  padding-top: 24px;
  
}

.\_pendo-launcher\_ .\_pendo-launcher-header\_ img {
  /* -- Launcher header area image styles -- */
  display: none;
}

.\_pendo-launcher\_ .\_pendo-launcher-header\_ .\_pendo-launcher-title\_ {
  /* -- Launcher header area text styles -- */
  font-size: 24px;
  line-height: 24px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  color: #222C34;
  
}

.\_pendo-launcher\_ .\_pendo-launcher-header\_ .\_pendo-launcher-search-box\_ {
  /* -- Launcher search area (if enabled) -- */
}

.\_pendo-launcher\_ .\_pendo-launcher-header\_ .\_pendo-launcher-search-box\_ input[type='text'] {
  /* -- Launcher search area text box -- */
}

.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ {
  /* -- Launcher guide listing area -- */
  padding: 0px;
  box-sizing: border-box;
  padding: 24px 24px 0px;
  
  height: auto;
}

.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_ {
  /* -- An individual item in the launcher guide listing -- */
  transition: transform 0.15s ease-in-out;
  
  margin: 0px;
  margin-bottom: 16px;
  width: 100%;
  background-color: #3399FF;
  height: 56px;
  border-radius: 56px;
  position: relative;
  line-height: 56px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  color: white;
  
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  
  box-shadow: 0px 8px 16px rgba(0,0,25,0.15);
}
.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_:hover {
	transform: scale(1.01) translateY(-4px);
}


.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_ a{
  color: white;
  cursor: pointer;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: relative
 
}
.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_ a:hover {
	text-decoration: none;
}

@keyframes pulse\_td {
	0% {
      
		height: 56px;
      width: 100%;
		opacity: 0;
	}
	40% {
		opacity: 0.25;
	}
	80% {
		height: 80px;
      width: calc(100% + 24px);
		opacity: 0;
	}
  100% {
	height: 80px;
    width: calc(100% + 24px);
		opacity: 0;
	}
}
.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_:after {
	content: "";
	display: block;
  background-color: #3399FF;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  border-radius: 100px;
  z-index: 0;
  animation-name: pulse\_td;
  animation-duration: 2.4s;
  animation-iteration-count: infinite;
  
}

.\_pendo-launcher\_ .\_pendo-launcher-guide-listing\_ .\_pendo-launcher-item\_:before {
	content: "1";
  display: block;
  width: 48px; height: 48px;
  position: absolute;
  left: 4px;
  top: 4px;
  border-radius: 50%;
  line-height: 48px;
  font-size: 18px;
  font-weight: 600;
  background-color: #0281FF;
  z-index: 2;
}

.\_pendo-launcher\_ .\_pendo-launcher-header\_ .td\_launcher\_p {
	font-size: 14px;
  line-height: 20px !important;
  font-family: 'Open Sans', sans-serif;
  color: #222C34;
  margin: 0px;
  margin-top: 8px;
  margin-bottom: 8px;
  font-weight: 400;
  
  
}

.td\_launcher\_item {
	display: block;
  cursor: pointer;
  width: 100%;
  color: #69859D;
  background-color: white;
  margin-bottom: 16px;
  float: left;
  border-radius: 100px;
  height: 56px;
  border: 2px solid #D4DCE3;
  box-sizing: border-box;
  
  position: relative;
  line-height: 56px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}
.td\_l\_num {
position: absolute;
display: block;
  width: 48px; height: 48px;
  position: absolute;
  left: 2px;
  top: 2px;
  border-radius: 50%;
  line-height: 48px;
  font-size: 18px;
  font-weight: 600;
  background-color: white;
}


.td\_foot {
position: relative;
  box-sizing: border-box;
  padding: 0px 24px;
  padding-top: 8px
}
.td\_kb {
display: block;
text-align: left;
  float: left;
  color: #A5AAB1;
}
.td\_cs {
display: block;
text-align: right;
float: right;
color: #A5AAB1;}

.td\_foot a:hover {
	text-decoration: none;
  color: #3399FF;
}



.\_pendo-launcher\_ .\_pendo-launcher-footer\_ {
  /* -- Launcher footer area -- */
}
.\_pendo-launcher-badge\_:hover {
	transform: scale(1.1) translateY(-5px);
}
.\_pendo-launcher-badge\_ {
  /* -- Styles for the launcher badge -- */
  transition: transform 0.15s ease-in-out;
    background-color: #00A6F1;
  width: 64px;
  height: 64px;
  opacity: 1;
  border-radius: 50%;
  
  background-image: url('data:image/png;base64,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');
  background-size: 32px;
  background-position: center center;
  background-repeat: no-repeat;
	box-shadow: 0px 5px 15px rgba(0,0,0,0.2),
    0px 15px 35px rgba(0,0,25,0.50),
    0px 50px 100px rgba(0,0,25,0.25);
}

/* -- Styles for launcher badge orientations -- */
.\_pendo-launcher-badge-bottom-right\_ {
  right: 32px;
  bottom: 32px;
}

.\_pendo-launcher-badge-bottom-left\_ {
}

.\_pendo-launcher-badge-top-right\_ {
}

.\_pendo-launcher-badge-top-left\_ {
}

/* -- Styles for launcher orientations -- */
.\_pendo-launcher-bottom-right\_ {
  right: 24px;
  bottom: 96px
}

.\_pendo-launcher-bottom-left\_ {
}

.\_pendo-launcher-top-right\_ {
}

.\_pendo-launcher-top-left\_ {
}

/* ------------------------------
 * -- Poll Styling --------------
 * ------------------------------ */
 
.\_pendo-guide-container\_ .\_pendo-poll\_ {
 /* -- Poll container -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ {
 /* -- Question and answer container (everything but the submit button) -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question-prompt\_ {
 /* -- Question text -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ label {
 /* -- Number Scale Poll positioning and styles for radio button text and inputs -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ select {
 /* -- Dropdown styling -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ .\_pendo-poll-npsrating-choices\_ {
 /* -- NPS Poll ratings container -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ .\_pendo-poll-npsrating-choices\_ label {
 /* -- NPS Poll individual rating - the number scale items -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ .\_pendo-poll-npsrating-choices\_ label span {
  /* -- NPS Poll individual rating - accompanying text for number scale items -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-npsreason\_ {
 /* -- NPS Poll optional user-provided explanation container -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-npsreason\_ .\_pendo-poll-question-prompt\_ {
 /* -- NPS Poll optional user-provided explanation text -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-question\_ button {
  /* -- Yes/No Poll buttons styles -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-submit\_ {
 /* -- Submit button styles -- */
}

.\_pendo-guide-container\_ .\_pendo-poll-message\_ {
 /* -- "Success message" styles -- */
}


/* ------------------------------
 * -- Additional Global Styling -
 * ------------------------------ */

.\_pendo-close-guide\_ {
  /* -- The dismiss guide button displayed in the corner of every guide -- */
}

.\_pendo-badge\_ {
  /* -- Styles for badges -- */
}


/* ------------------------------
 * -- Your Custom Classes/IDs ---
 * ------------------------------ */
.\_pendo-guide-banner-top\_ {
	top: 60px;
  	height: 50px !important;
  	-webkit-animation: none !important;
 -moz-animation: none !important;
 -o-animation: none !important;
 -ms-animation: none !important;
 animation: none !important;
}