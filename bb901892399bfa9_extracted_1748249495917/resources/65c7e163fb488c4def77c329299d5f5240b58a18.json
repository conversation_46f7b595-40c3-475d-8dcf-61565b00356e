{"sidePanels.filters.status.label": "Status", "modals.uploadDoNotCallList.recordListLabel": "Do Not Call list", "emptyDoNotCallListsMessage": "Upload a new Do Not Call list to associate with a campaign\t", "modals.uploadList.template.allFieldsExceptExternalRequired": "All fields in the template are required, except External ID and Provider", "modals.uploadList.dropDown.externalField": "External ID and Provider", "filters.campaigns.plural": "campaigns", "sidePanels.filters.created.label": "Created", "emptyMessage": "Upload a new record list to start your outbound campaign\t", "entryList.onHover": "Users can only see the records lists within their permissions", "modals.uploadDoNotCallList.hoverSelectTeamTip": "If you select one team, the list won't be visible to other team users; If you select “All”, the list will be visible to all users.", "doNotCallListsCount": "{{count}} DNC list", "modals.deleteDoNotCallListConfirmation.title": "Delete do not call list", "modals.recordListApi.title": "List API", "sidePanels.filters.all": "All", "modals.uploadList.dropDown.phoneNumberField": "Phone Number (default)", "modals.uploadList.toggleMessage": "Remove records with the same", "modals.uploadDoNotCallList.errorNameNotUnique": "This list name is already taken", "modals.uploadList.cancelUploadButtonLabel": "Cancel upload", "sidePanels.filters.campaign.search.loading": "Loading", "modals.deleteDoNotCallListConfirmation.message": "Are you sure you want to delete the \"{{name}}\" list? You won't be able to retrieve it back.", "filters.clearAllButton": "Clear all", "filters.created": "Created", "modals.uploadList.uploadFileButtonLabel": "Upload file", "tableHeaders.numberOfEntries": "Total entries", "modals.uploadDoNotCallList.chooseCsvButtonLabel": "Choose a CSV file", "entryList.save": "Save", "sidePanels.filters.campaign.search.minLimit": "Type at least 3 characters", "newDoNotCallListButtonLabel": "Add Do Not Call list", "modals.uploadList.chooseCsvButtonLabel": "Choose a CSV file", "modals.uploadList.duplicatesLabel": "Duplicates", "sidePanels.filters.campaign.title": "Campaigns", "tableHeaders.name": "Name", "sidePanels.filters.campaign.search.noCampaigns": "There are no campaigns to use as filter", "sidePanels.filters.campaign.search.noResults": "No campaigns found", "tableLists.tableRow.downloadButton.latest.tooltip.expired": "Expired CSV", "modals.uploadDoNotCallList.nameLabel": "Name", "modals.uploadDoNotCallList.title": "Upload Do Not Call list", "modals.uploadList.nameLabel": "Name", "tableLists.tableRow.downloadButton.latest.tooltip.incomplete": "CSV not processed", "entryList.contradictedTip": "Please unlink the record list with the following campaign(s), then delete the team.", "entryList.update.contradictedTip": "Please unlink the record list with the following campaign(s), then change the team.", "entriesCount": "{{formattedCount}} entry", "searchTooltip": "Type at least {{minLength}} characters", "sidePanels.filters.campaign.search": "Search for a name", "modals.uploadList.dropDown.phoneNumberSubtitle": "Any type", "noTeam": "No team", "tableLists.field.all": "All", "modals.uploadList.template.fieldsWithGuidelines": "Required fields may vary in some situations. Check guidelines to learn more.", "searchEmptyMessage": "Try using different keywords or checking for typos", "modals.uploadList.selectOutScopeTeam": "You can't upload a record list for this team. Please select a team within your management scope.", "modals.uploadList.hoverSelectTeamTip": "If you select one team, the list won't be visible to other team users; If you select “All”, the list will be visible to all users.", "modals.uploadDoNotCallList.downloadListTemplateLabel": "Download template", "filters.campaigns.single": "campaign", "modals.uploadList.seeGuidelinesLabel": "See guidelines", "sidePanels.filters.campaign.maxCampaignsHint": "Select up to 10 campaigns", "modals.uploadDoNotCallList.template.allFieldsExceptExpirationDateRequired": "All fields in the template are required, except Expiration Date.", "newButtonLabel": "Add record list", "modals.uploadList.errorMaxUploadFileLinesExceedTitle": "File exceeds max limit of {{maxRecordListFileLimit}} records", "modals.uploadDoNotCallList.teamPermission.hint": "Users can only see the do not call lists within their permissions", "modals.uploadList.teamPermission.hint": "Users can only see the records lists within their permissions", "recordsCount": "{{formattedCount}} record", "modals.uploadList.title": "Upload record list", "modals.uploadList.errorMaxFileSizeExceeded": "This file exceeds {{maxFileSize}}. Please select another file.", "tableHeaders.numberOfRecords": "Total records", "tableHeaders.createdAt.hint": "All times are displayed in the {{timezone}} timezone", "entryList.delete.contradictedTip": "Please unlink the record list with the following campaign(s), then delete the team.", "tableLists.tableRow.downloadButton.latest.tooltip.ready": "Prepare list for download", "filters.campaigns": "Campaigns", "newDoNotCallListButtonLabelTooltip": "Do Not Call list limit reached", "modals.uploadList.recordListLabel": "Record list", "emptyTitle": "Upload your first record list", "modals.deleteListConfirmation.title": "Delete record list", "modals.doNotCallListApi.title": "DNCL API", "modals.uploadDoNotCallList.cancelUploadButtonLabel": "Cancel upload", "modals.uploadDoNotCallList.uploadFileButtonLabel": "Upload file", "modals.uploadList.errorMaxFileNameExceededTitle": "Long name", "noRecordMessage": "Please use different criteria to search or filter for records.", "modals.uploadList.namePlaceholder": "e.g. Prospects", "tabs.doNotCall": "Do Not Call", "loadMore.loadingElements": "Loading", "filters.filterText": "Filters", "loadMore.loadMoreElements": "Load more", "tableHeaders.campaigns": "Campaigns", "searchEmptyTitle": "No lists found", "tableLists.field.noTeam": "No teams", "entryList.team": "Team", "search": "Search by list name", "filters.emptyMessage": "Try using different criteria", "modals.uploadDoNotCallList.errorMaxFileSizeExceeded": "This file exceeds {{maxFileSize}}. Please select another file.", "modals.uploadList.errorMaxFileNameExceededMessage": "File names cannot be longer than {{maxFileNameSize}} characters.", "filters.status": "Status", "modals.uploadDoNotCallList.namePlaceholder": "e.g. Prospects", "tableLists.tableRow.downloadButton.latest.tooltip.corrupted": "Damaged CSV", "modals.uploadList.removeLabel": "Remove file", "modals.uploadList.errorNameNotUnique": "This list name is already taken", "tableHeaders.createdAt.label": "Created", "modals.deleteListConfirmation.message": "Are you sure you want to delete the \"{{recordListName}}\" list? You won't be able to retrieve it back.", "count_plural": "{{count}} lists", "recordsCount_plural": "{{formattedCount}} records", "doNotCallListsCount_plural": "{{count}} DNC lists", "sidePanels.filters.title": "Filters", "tableLists.tableRow.downloadButton.latest.tooltip.noRecord": "No records in this list", "tableLists.tableRow.downloadButton.latest.tooltip.validating": "Still validating list", "modals.listApi.content.title": "ID", "newButtonLabelTooltip": "Records list limit reached", "modals.uploadDoNotCallList.removeLabel": "Remove file", "tableHeaders.status": "Status", "modals.uploadDoNotCallList.selectOutScopeTeam": "You can't upload a Do Not Call list for this team. Please select a team within your management scope.", "modals.uploadList.team.hint": "Team users can only see lists from their own team", "modals.uploadList.errorMaxUploadFileLinesExceedDescription": "Please choose another file or divide this list in two or more files", "modals.uploadList.downloadListTemplateLabel": "Download template", "entryList.editTeam": "Edit team", "noRecordTitle": "No records found", "entriesCount_plural": "{{formattedCount}} entries", "filters.emptyTitle": "No lists found", "count": "{{count}} list", "team": "Team", "emptyDoNotCallListsTitle": "Upload your first Do Not Call list", "tableHeaders.team": "Team", "modals.uploadDoNotCallList.team.hint": "Team users can only see lists from their own team", "tabs.records": "Records", "modals.uploadList.template.allFieldsRequired": "All fields in the template are required.", "tabs.permission.hint": "You don't have access to this", "headerLabel": "Lists"}