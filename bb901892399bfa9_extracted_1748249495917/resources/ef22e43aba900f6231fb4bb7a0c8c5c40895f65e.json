{"guides": [{"description": "Onboarding Guide for Express users who see the Setup Wizard", "createdByUser": {"id": "", "username": "", "first": "", "last": "", "role": 0, "userType": "", "hasLoggedIn": false}, "createdAt": 1743913366177, "lastUpdatedByUser": {"id": "", "username": "", "first": "", "last": "", "role": 0, "userType": "", "hasLoggedIn": false}, "lastUpdatedAt": *************, "appId": -323232, "id": "usg3OnRpzzBsgpRV_dinD_7HLBs", "name": "Express Onboarding - Guide A", "state": "public", "emailState": "", "launchMethod": "dom", "isMultiStep": true, "isTraining": false, "attributes": {"activation": {"selector": "[data-testid=\"onboarding-exit-button-exit-setup\"], [data-testid=\"onboarding-tour-modal-button-dismiss\"]", "inheritStepOne": false, "event": ["click"]}, "capping": {"maxSessionImpressions": 1, "maxImpressions": 1, "displayEveryTime": false}, "dates": {"en-US": "May 6, 2025"}, "device": {"type": "desktop"}, "dom": {"isOnlyShowOnce": false, "showGuideOnlyOnElementClick": true}, "elementSelectionType": "custom", "priority": 606, "sharedServiceVersion": "5.3.0", "type": "building-block"}, "audience": [{"source": {"visitors": null}}, {"eval": {"accountId": "metadata.auto.accountids"}}, {"unwind": {"field": "accountId", "keepEmpty": true}}, {"select": {"visitorId": "visitorId"}}], "audienceUiHint": {"filters": [{"kind": "visitor", "segmentId": "everyone"}]}, "authoredLanguage": "en-US", "recurrence": 0, "recurrenceEligibilityWindow": 0, "resetAt": 0, "publishedAt": *************, "publishedEver": true, "currentFirstEligibleToBeSeenAt": *************, "showsAfter": *************, "isTopLevel": false, "isModule": false, "editorType": "visualDesignStudio", "dependentMetadata": [], "autoCreateFeedback": false, "language": "en-US", "steps": [{"id": "JkPZsOLS-PiyJwwblFCtfzKRQ64", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "pageId": "xgAOU-AKvC9JPEJetxhYjJcRN70", "regexUrlRule": "(?:^https?://[^/]*/atlas/apps/home/<USER>//[^/]*/home-app/[^/#?;]+/index\\.html/?(?:;[^#]*)?(?:\\?[^#]*)?(?:#.*)?$|^https?://[^/]*/home/<USER>", "elementPathRule": "", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/JkPZsOLS-PiyJwwblFCtfzKRQ64/en2t9NFr-t3pWL9rSHHPMyg-nw4.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/JkPZsOLS-PiyJwwblFCtfzKRQ64/S3Z7I6tP_1KhRiQvISbOZPoAOwE.dom.json?sha256=x3P-uQBdToE-XdbpcaS5G_VhI_wqpBDGFBM1LmTtzkw", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/JkPZsOLS-PiyJwwblFCtfzKRQ64/-sB09GHE1RKeqZxmZWFFriAVEaA.dom.jsonp?sha256=eVrdhIVB0_3z55BNZoXIXn7aeplzAns8nWi5ySDxYIs", "rank": 1, "advanceMethod": "", "attributes": {"advanceActions": null, "blockOutUI": {"additionalElements": "", "enabled": false, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "elementSelectionType": "step1", "isAutoFocus": true, "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746484397974, "resetAt": 0, "hideCredits": true}, {"id": "lllLpEV8j02lMnm6cpVzYsYT08o", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "name": "Dock", "elementPathRule": "[data-testid=\"dock\"]", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/lllLpEV8j02lMnm6cpVzYsYT08o/wbUhEajyRDWu4GvsRVlSmajeBaU.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/lllLpEV8j02lMnm6cpVzYsYT08o/ljAKVfQCiSzpgzKo2UVESKDe2nk.dom.json?sha256=0fcff8wPZT9p5cxSLMK2JMMGShRvcszvDIvAVRtnaJ0", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/lllLpEV8j02lMnm6cpVzYsYT08o/zokGA-rgluEYgl7gZgnEQnFWZAg.dom.jsonp?sha256=DW38Hy2wzRA_fWSQj5ls1kXmgOLy4BedwaXMQ1Z50is", "rank": 2, "advanceMethod": "", "attributes": {"advanceActions": {"elementClick": true, "elementHover": false}, "blockOutUI": {"additionalElements": "", "enabled": true, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "elementSelectionType": "custom", "isAutoFocus": true, "layoutDir": "right", "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746481856598, "resetAt": 0, "hideCredits": true}, {"id": "yLGTSw4fE1FMM7SYdcTrYDLKLds", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "name": "Title Bar", "elementPathRule": ".title-bar-root-component-module__root", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/yLGTSw4fE1FMM7SYdcTrYDLKLds/MlG2dBZfYCV7v6-9tny1vkr6CdU.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/yLGTSw4fE1FMM7SYdcTrYDLKLds/7VdPHByMeMS7JHiOvcsZm1MTT7U.dom.json?sha256=AAPGpZcUOOG-Mehg8060ztpmlfr2F2qRc4tICGF69gk", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/yLGTSw4fE1FMM7SYdcTrYDLKLds/wkenz8DpEHS3Inmv2wdJJ6K83YE.dom.jsonp?sha256=Ad8ZXbXRqeq5gNpjHdlWGIlyjV_SdL43ocpWZaV4rDE", "rank": 3, "advanceMethod": "", "attributes": {"advanceActions": null, "blockOutUI": {"additionalElements": "", "enabled": true, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "elementSelectionType": "custom", "isAutoFocus": true, "layoutDir": "auto", "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746481856606, "resetAt": 0, "hideCredits": true}, {"id": "laTMVcbKBGDhv30S5JWQ76Ay1E8", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "name": "Notifications", "elementPathRule": "[name=\"notification-badge-button-title-bar\"]", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/laTMVcbKBGDhv30S5JWQ76Ay1E8/dd85XbGit7oQooSJMr0N1C4HB1w.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/laTMVcbKBGDhv30S5JWQ76Ay1E8/8flsuBdUB4H979ltmipT6LCm25s.dom.json?sha256=KrNSrC80YKJ2GaKoGHACWygEmm-hixmvXApfc9iZstk", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/laTMVcbKBGDhv30S5JWQ76Ay1E8/hx6w5ZLQh_GjUzeqVgiSTJLCs-8.dom.jsonp?sha256=vovNrCif0NcJC9adud9tL8XYDTvH5TUo2_ChBEluHB0", "rank": 4, "advanceMethod": "", "attributes": {"advanceActions": {"elementClick": true, "elementHover": false}, "blockOutUI": {"additionalElements": "", "enabled": true, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "elementSelectionType": "suggested", "isAutoFocus": true, "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746481856676, "resetAt": 0, "hideCredits": true}, {"id": "a0503WtgZr2nKhmuahKn9mxjOg8", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "name": "Customer support button", "elementPathRule": "[data-testid=\"customer-support-button\"]", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/a0503WtgZr2nKhmuahKn9mxjOg8/akoPGnXW8AhkvYf9W17wlZguGRM.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/a0503WtgZr2nKhmuahKn9mxjOg8/EEd8BxSLxXZBb87F84zofd-IgIY.dom.json?sha256=sh08yceYtF9YUsixCzL-j71FhX3dB3sxfJUVR0htbl0", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/a0503WtgZr2nKhmuahKn9mxjOg8/JFmzH4wrA3uwWq_K9VoeBiMIZGQ.dom.jsonp?sha256=Tp-sqqRd8e_fAX6CCOmUJKQ-et0XzHxCyqgd2YnA12I", "rank": 6, "advanceMethod": "", "attributes": {"advanceActions": null, "blockOutUI": {"additionalElements": "", "enabled": true, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "elementSelectionType": "custom", "isAutoFocus": true, "layoutDir": "auto", "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746481856691, "resetAt": 0, "hideCredits": true}, {"id": "aK43KD__eADd0YQHKV5grYqNIiE", "guideId": "usg3OnRpzzBsgpRV_dinD_7HLBs", "templateId": "building-block-guide", "type": "", "name": "Final step", "elementPathRule": "", "triggerElementPathRule": "", "confirmationElementPathRule": "", "contentType": "text/html; charset=utf-8", "buildingBlocksUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/aK43KD__eADd0YQHKV5grYqNIiE/zGMLsFyuc9ESTPOuTyYQQv9EWGc.buildingBlocks.json", "domUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/aK43KD__eADd0YQHKV5grYqNIiE/D2rikLfibRZqQs1dI1R4thJFtgo.dom.json?sha256=yiTTt5TNtlO-f7g8JcfUWxkFknq0Mj10s5Mw4VrVzQs", "domJsonpUrl": "https://content.productdatausage.external.talkdeskapp.com/guide-content/usg3OnRpzzBsgpRV_dinD_7HLBs/aK43KD__eADd0YQHKV5grYqNIiE/tO4i-BTimgwBezINYIqQdg6X3b4.dom.jsonp?sha256=9a_0JuYHJYRkqHyc6aPNtj_0hg8PlIplJvvfpHmAlk4", "rank": 7, "advanceMethod": "", "attributes": {"advanceActions": null, "blockOutUI": {"additionalElements": "", "enabled": false, "padding": {"bottom": 0, "left": 0, "right": 0, "top": 0}}, "isAutoFocus": true, "themeId": "C6xFkMGL4ly-UtP9_hLHNX4qq9Q"}, "lastUpdatedAt": 1746398973957, "resetAt": 0, "hideCredits": true}], "guideDismissCount": 0}], "normalizedUrl": "http://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/index.html?depth=1&monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1jYS0xLWNhLWNlbnRyYWwtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwY2FcXC5jb20iXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZDMwMWYwNWNiNWM5NTA4YjdkYyIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2ODNkMjRlNTU2MjJkNDg5OTI1NyIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbmNhIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiI0OGVmODQ5Ni0wNGJmLTQxMTMtYWRlYS1jMGU2MjllYWI0YTQiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtY2EtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJjYS1jZW50cmFsLTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiYWdlbnQtYXNzaXN0LXNlY29uZGFyeSIsImF0bGFzLm93bmVyIjoiYWktZW5naW5lZXJpbmcifX0%3D&nonce=6bacfc66-9bea-42f4-9634-5e914556db85&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com", "lastGuideStepSeen": {"guideId": "TY9dmR3eJZIAKzzmWe9D4I4IzB0", "guideStepId": "MJCfygLJBoNw9YV5mCW6IBxL43E", "time": *************, "lastDismissedAutoAt": *************, "isMultiStep": false, "state": "dismissed"}, "guideWidget": {"enabled": false, "hidePoweredBy": true, "data": {"guideCssUrl": "https://pendo-static-****************.storage.googleapis.com/guide.*************.css", "onboarding": false}}, "guideCssUrl": "https://pendo-static-****************.storage.googleapis.com/guide.*************.css", "throttling": {"count": 1, "enabled": true, "interval": 1, "unit": "Hour"}, "autoOrdering": [], "globalJsUrl": "", "segmentFlags": [], "designerEnabled": false, "features": null, "latestDismissedAutoAt": *************, "id": "21", "preventCodeInjection": false}